#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从新的帮战名单表.xlsx文件中更新团队分配信息
"""

import pandas as pd
import json

def analyze_new_excel_structure(excel_file):
    """分析新Excel文件的结构"""
    print("=" * 60)
    print("分析新Excel文件：帮战名单表.xlsx")
    print("=" * 60)
    
    try:
        # 获取所有工作表名称
        excel_file_obj = pd.ExcelFile(excel_file)
        sheet_names = excel_file_obj.sheet_names
        
        print(f"工作表列表: {sheet_names}")
        print()
        
        # 分析每个工作表
        for sheet_name in sheet_names:
            print(f"--- 工作表: {sheet_name} ---")
            try:
                df = pd.read_excel(excel_file, sheet_name=sheet_name, header=None)
                print(f"大小: {df.shape[0]}行 x {df.shape[1]}列")
                
                # 显示前几行的非空数据
                print("前10行内容预览:")
                for i in range(min(10, df.shape[0])):
                    row_data = []
                    for j in range(min(10, df.shape[1])):
                        cell = df.iloc[i, j]
                        if pd.notna(cell):
                            row_data.append(f"列{j+1}:'{cell}'")
                    if row_data:
                        print(f"  行{i+1}: {' | '.join(row_data)}")
                print()
            except Exception as e:
                print(f"读取失败: {e}")
                print()
        
        return sheet_names
        
    except Exception as e:
        print(f"分析Excel文件失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def extract_team_data_from_new_excel(excel_file):
    """从新Excel文件中提取团队数据"""
    print("=" * 60)
    print("提取团队分配数据")
    print("=" * 60)
    
    try:
        # 尝试读取第一个工作表
        df = pd.read_excel(excel_file, sheet_name=0, header=None)
        
        # 加载现有成员列表
        with open('guild_members.json', 'r', encoding='utf-8') as f:
            guild_members = json.load(f)
        
        member_names = [m['name'] for m in guild_members]
        team_assignments = {}
        
        print(f"在Excel中查找 {len(member_names)} 名成员的团队分配...")
        
        # 遍历整个表格查找成员和团队信息
        for row_idx in range(df.shape[0]):
            for col_idx in range(df.shape[1]):
                cell_value = df.iloc[row_idx, col_idx]
                if pd.notna(cell_value):
                    cell_text = str(cell_value).strip()
                    
                    # 检查是否是成员名字
                    if cell_text in member_names:
                        print(f"找到成员 '{cell_text}' 在位置: 行{row_idx+1}, 列{col_idx+1}")
                        
                        # 查找团队信息（在同行或附近查找）
                        team_info = None
                        
                        # 在同行查找团队标识
                        for check_col in range(max(0, col_idx-10), min(df.shape[1], col_idx+10)):
                            check_cell = df.iloc[row_idx, check_col]
                            if pd.notna(check_cell):
                                check_text = str(check_cell).strip()
                                # 查找团队关键词
                                team_keywords = ['一团', '二团', '三团', '四团', '1团', '2团', '3团', '4团', '防守团', '进攻团']
                                for keyword in team_keywords:
                                    if keyword in check_text:
                                        team_info = check_text
                                        print(f"  -> 在同行找到团队信息: {team_info}")
                                        break
                                if team_info:
                                    break
                        
                        # 如果同行没找到，查找列标题或附近区域
                        if not team_info:
                            # 向上查找列标题
                            for check_row in range(max(0, row_idx-10), row_idx):
                                check_cell = df.iloc[check_row, col_idx]
                                if pd.notna(check_cell):
                                    check_text = str(check_cell).strip()
                                    team_keywords = ['一团', '二团', '三团', '四团', '1团', '2团', '3团', '4团', '防守团', '进攻团']
                                    for keyword in team_keywords:
                                        if keyword in check_text:
                                            team_info = check_text
                                            print(f"  -> 在列标题找到团队信息: {team_info} (行{check_row+1})")
                                            break
                                    if team_info:
                                        break
                        
                        if team_info:
                            team_assignments[cell_text] = team_info
                        else:
                            print(f"  -> 未找到团队信息")
        
        print(f"\n成功提取 {len(team_assignments)} 名成员的团队分配:")
        for member, team in team_assignments.items():
            print(f"  {member} -> {team}")
        
        return team_assignments
        
    except Exception as e:
        print(f"提取团队数据失败: {e}")
        import traceback
        traceback.print_exc()
        return {}

def update_members_with_new_data():
    """用新Excel数据更新成员信息"""
    print("\n" + "=" * 60)
    print("更新成员团队信息")
    print("=" * 60)
    
    # 提取新的团队分配
    team_assignments = extract_team_data_from_new_excel("帮战名单表.xlsx")
    
    # 加载现有成员数据
    with open('guild_members.json', 'r', encoding='utf-8') as f:
        members = json.load(f)
    
    # 团队名称标准化映射
    team_mapping = {
        '一团': '1团',
        '二团': '2团',
        '三团': '3团',
        '四团': '4团',
        '1团': '1团',
        '2团': '2团',
        '3团': '3团',
        '4团': '4团',
        '防守团': '防守团',
        '进攻团': '进攻团'
    }
    
    # 更新成员团队信息
    updated_count = 0
    for member in members:
        member_name = member['name']
        if member_name in team_assignments:
            original_team = team_assignments[member_name]
            
            # 标准化团队名称
            standardized_team = '未分配'
            for key, value in team_mapping.items():
                if key in original_team:
                    standardized_team = value
                    break
            
            member['team'] = standardized_team
            member['status'] = '主力'
            updated_count += 1
            print(f"更新 {member_name}: {original_team} -> {standardized_team}")
        else:
            # 保持原有团队信息或设为未分配
            if 'team' not in member or not member['team']:
                member['team'] = '未分配'
            member['status'] = '主力'
    
    # 保存更新后的数据
    with open('members_with_updated_teams.json', 'w', encoding='utf-8') as f:
        json.dump(members, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 成功更新 {updated_count} 名成员的团队信息")
    print(f"📁 保存到: members_with_updated_teams.json")
    
    # 统计团队分布
    team_stats = {}
    for member in members:
        team = member.get('team', '未分配')
        team_stats[team] = team_stats.get(team, 0) + 1
    
    print(f"\n📊 更新后的团队分布:")
    for team, count in sorted(team_stats.items()):
        print(f"  {team}: {count}人")
    
    # 按团队显示成员
    print(f"\n👥 详细团队成员分布:")
    teams = {}
    for member in members:
        team = member.get('team', '未分配')
        if team not in teams:
            teams[team] = []
        teams[team].append(f"{member['name']}({member['profession']})")
    
    for team, member_list in sorted(teams.items()):
        print(f"\n【{team}】({len(member_list)}人):")
        for member_info in member_list:
            print(f"  - {member_info}")
    
    return members

def main():
    """主函数"""
    excel_file = "帮战名单表.xlsx"
    
    # 分析新Excel文件结构
    sheet_names = analyze_new_excel_structure(excel_file)
    
    # 更新成员团队信息
    updated_members = update_members_with_new_data()
    
    print(f"\n" + "=" * 60)
    print("✅ 团队分配更新完成！")
    print("使用新的帮战名单表.xlsx数据")
    print("=" * 60)

if __name__ == "__main__":
    main()
