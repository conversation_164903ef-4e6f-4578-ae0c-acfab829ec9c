#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
纸落云烟帮会管理系统启动脚本
"""

import os
import sys

def check_requirements():
    """检查依赖是否安装"""
    try:
        import flask
        print("✅ Flask 已安装")
        return True
    except ImportError:
        print("❌ Flask 未安装")
        print("请运行: pip install -r requirements.txt")
        return False

def check_data_file():
    """检查数据文件是否存在"""
    data_file = 'data/guild_members.json'
    if os.path.exists(data_file):
        print(f"✅ 数据文件存在: {data_file}")
        return True
    else:
        print(f"❌ 数据文件不存在: {data_file}")
        print("请确保数据文件已复制到 data/ 目录")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 纸落云烟帮会管理系统启动检查")
    print("=" * 60)
    
    # 检查依赖
    if not check_requirements():
        sys.exit(1)
    
    # 检查数据文件
    if not check_data_file():
        sys.exit(1)
    
    print("=" * 60)
    print("✅ 所有检查通过，启动Web应用...")
    print("=" * 60)
    
    # 启动应用
    from app import app
    app.run(debug=True, host='0.0.0.0', port=5000)

if __name__ == '__main__':
    main()
