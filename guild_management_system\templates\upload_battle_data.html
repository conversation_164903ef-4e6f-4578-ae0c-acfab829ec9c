{% extends "base.html" %}

{% block title %}上传战斗数据 - 纸落云烟帮会管理系统{% endblock %}

{% block content %}
<div class="stat-card" style="margin-bottom: 20px;">
    <h3 style="color: #667eea; margin-bottom: 15px;">📤 上传战斗数据</h3>
    <p style="color: #6c757d; margin-bottom: 20px;">
        上传格式：<code>纸落云烟_对手帮会.csv</code> 或 <code>日期_时间_纸落云烟_对手帮会.csv</code>
    </p>
    
    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
        <h5 style="color: #495057; margin-bottom: 10px;">📋 上传说明</h5>
        <ul style="color: #6c757d; margin: 0; padding-left: 20px;">
            <li>每场战斗单独上传一个CSV文件</li>
            <li>文件名格式：<strong>纸落云烟_对手帮会.csv</strong></li>
            <li>支持带时间戳的文件名：<strong>20250531_203611_纸落云烟_初影未来.csv</strong></li>
            <li>上传后会自动分析战斗数据，包括新成员和替补</li>
            <li>系统会根据数据表现推断进攻团/防守团类型</li>
        </ul>
    </div>
</div>

<!-- 上传表单 -->
<div class="stat-card">
    <form id="uploadForm" enctype="multipart/form-data" style="text-align: center;">
        <div style="border: 2px dashed #667eea; border-radius: 12px; padding: 40px; margin-bottom: 20px; background: rgba(102, 126, 234, 0.05);">
            <div style="font-size: 48px; color: #667eea; margin-bottom: 15px;">📁</div>
            <h4 style="color: #667eea; margin-bottom: 10px;">选择战斗数据文件</h4>
            <p style="color: #6c757d; margin-bottom: 20px;">拖拽文件到此处或点击选择</p>
            <input type="file" id="battleFile" name="battle_file" accept=".csv" style="display: none;">
            <button type="button" onclick="document.getElementById('battleFile').click()" 
                    style="background: #667eea; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-size: 16px;">
                选择CSV文件
            </button>
        </div>
        
        <div id="fileInfo" style="display: none; margin-bottom: 20px; padding: 15px; background: #e3f2fd; border-radius: 8px;">
            <div style="display: flex; align-items: center; justify-content: center; gap: 10px;">
                <span style="font-size: 24px;">📄</span>
                <div>
                    <div id="fileName" style="font-weight: bold; color: #1976d2;"></div>
                    <div id="fileSize" style="font-size: 12px; color: #666;"></div>
                </div>
            </div>
        </div>
        
        <button type="submit" id="uploadBtn" disabled 
                style="background: #28a745; color: white; border: none; padding: 15px 30px; border-radius: 8px; cursor: pointer; font-size: 16px; font-weight: bold;">
            🚀 上传并分析
        </button>
    </form>
</div>

<!-- 上传进度 -->
<div id="uploadProgress" style="display: none;" class="stat-card">
    <h4 style="color: #667eea; margin-bottom: 15px;">📊 上传进度</h4>
    <div style="background: #f8f9fa; border-radius: 8px; padding: 4px;">
        <div id="progressBar" style="background: linear-gradient(90deg, #667eea, #764ba2); height: 20px; border-radius: 6px; width: 0%; transition: width 0.3s ease;"></div>
    </div>
    <div id="progressText" style="text-align: center; margin-top: 10px; color: #6c757d;"></div>
</div>

<!-- 上传结果 -->
<div id="uploadResult" style="display: none;" class="stat-card">
    <div id="resultContent"></div>
    <div style="text-align: center; margin-top: 20px;">
        <a href="/battle_analysis" style="background: #667eea; color: white; text-decoration: none; padding: 12px 24px; border-radius: 8px; display: inline-block;">
            📈 查看战斗分析
        </a>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
const fileInput = document.getElementById('battleFile');
const fileInfo = document.getElementById('fileInfo');
const fileName = document.getElementById('fileName');
const fileSize = document.getElementById('fileSize');
const uploadBtn = document.getElementById('uploadBtn');
const uploadForm = document.getElementById('uploadForm');
const uploadProgress = document.getElementById('uploadProgress');
const progressBar = document.getElementById('progressBar');
const progressText = document.getElementById('progressText');
const uploadResult = document.getElementById('uploadResult');
const resultContent = document.getElementById('resultContent');

// 文件选择处理
fileInput.addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        if (file.name.endsWith('.csv')) {
            fileName.textContent = file.name;
            fileSize.textContent = `文件大小: ${(file.size / 1024).toFixed(1)} KB`;
            fileInfo.style.display = 'block';
            uploadBtn.disabled = false;
            uploadBtn.style.background = '#28a745';
            uploadBtn.style.cursor = 'pointer';
        } else {
            alert('请选择CSV文件！');
            fileInput.value = '';
            fileInfo.style.display = 'none';
            uploadBtn.disabled = true;
            uploadBtn.style.background = '#6c757d';
            uploadBtn.style.cursor = 'not-allowed';
        }
    }
});

// 拖拽上传
const dropZone = document.querySelector('form');
dropZone.addEventListener('dragover', function(e) {
    e.preventDefault();
    dropZone.style.background = 'rgba(102, 126, 234, 0.1)';
});

dropZone.addEventListener('dragleave', function(e) {
    e.preventDefault();
    dropZone.style.background = '';
});

dropZone.addEventListener('drop', function(e) {
    e.preventDefault();
    dropZone.style.background = '';
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        fileInput.files = files;
        fileInput.dispatchEvent(new Event('change'));
    }
});

// 表单提交
uploadForm.addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData();
    formData.append('battle_file', fileInput.files[0]);
    
    // 显示进度
    uploadProgress.style.display = 'block';
    uploadResult.style.display = 'none';
    uploadBtn.disabled = true;
    uploadBtn.textContent = '上传中...';
    
    // 模拟进度
    let progress = 0;
    const progressInterval = setInterval(() => {
        progress += Math.random() * 30;
        if (progress > 90) progress = 90;
        progressBar.style.width = progress + '%';
        progressText.textContent = `上传进度: ${Math.round(progress)}%`;
    }, 200);
    
    // 发送请求
    fetch('/upload_battle_data', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        clearInterval(progressInterval);
        progressBar.style.width = '100%';
        progressText.textContent = '上传完成: 100%';
        
        setTimeout(() => {
            uploadProgress.style.display = 'none';
            uploadResult.style.display = 'block';
            
            if (data.success) {
                resultContent.innerHTML = `
                    <div style="text-align: center;">
                        <div style="font-size: 48px; color: #28a745; margin-bottom: 15px;">✅</div>
                        <h4 style="color: #28a745; margin-bottom: 10px;">上传成功！</h4>
                        <p style="color: #6c757d;">${data.message}</p>
                        <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 15px; margin: 20px 0;">
                            <h5 style="color: #155724; margin-bottom: 10px;">📊 接下来可以：</h5>
                            <ul style="color: #155724; text-align: left; margin: 0; padding-left: 20px;">
                                <li>查看详细的战斗数据分析</li>
                                <li>对比排表成员和实际参战成员</li>
                                <li>发现新成员和替补的表现</li>
                                <li>根据进攻/防守团类型查看评分</li>
                            </ul>
                        </div>
                    </div>
                `;
            } else {
                resultContent.innerHTML = `
                    <div style="text-align: center;">
                        <div style="font-size: 48px; color: #dc3545; margin-bottom: 15px;">❌</div>
                        <h4 style="color: #dc3545; margin-bottom: 10px;">上传失败</h4>
                        <p style="color: #6c757d;">${data.error}</p>
                        <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 15px; margin: 20px 0;">
                            <h5 style="color: #721c24; margin-bottom: 10px;">💡 请检查：</h5>
                            <ul style="color: #721c24; text-align: left; margin: 0; padding-left: 20px;">
                                <li>文件格式是否为CSV</li>
                                <li>文件名是否包含"纸落云烟"</li>
                                <li>文件内容是否符合战斗数据格式</li>
                            </ul>
                        </div>
                    </div>
                `;
            }
            
            uploadBtn.disabled = false;
            uploadBtn.textContent = '🚀 上传并分析';
        }, 1000);
    })
    .catch(error => {
        clearInterval(progressInterval);
        uploadProgress.style.display = 'none';
        uploadResult.style.display = 'block';
        
        resultContent.innerHTML = `
            <div style="text-align: center;">
                <div style="font-size: 48px; color: #dc3545; margin-bottom: 15px;">⚠️</div>
                <h4 style="color: #dc3545; margin-bottom: 10px;">网络错误</h4>
                <p style="color: #6c757d;">请检查网络连接后重试</p>
            </div>
        `;
        
        uploadBtn.disabled = false;
        uploadBtn.textContent = '🚀 上传并分析';
    });
});
</script>
{% endblock %}
