#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
纸落云烟帮会管理系统 - 成员访问系统
独立的只读访问系统，与管理后台数据实时同步
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for, make_response
import json
import os
from datetime import datetime

app = Flask(__name__)

# 数据文件路径（指向上级目录的data文件夹，与管理后台共享）
MEMBERS_FILE = '../data/guild_members.json'
BATTLE_RECORDS_FILE = '../data/battle_records.json'

def load_members():
    """加载成员数据"""
    try:
        with open(MEMBERS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return []

def load_battle_records():
    """加载战斗记录"""
    try:
        with open(BATTLE_RECORDS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return []

def get_organization_structure(members):
    """获取三层级组织结构"""
    structure = {
        '进攻团': {},
        '防守团': {},
        '其他团': {}
    }

    for member in members:
        # 解析成员的组织信息
        main_group = member.get('main_group', '其他团')  # 进攻团/防守团/其他团
        sub_team = member.get('sub_team', '一团')        # 一团/二团/三团
        squad = member.get('squad', '1队')              # 1队/2队/3队/4队/5队

        # 初始化结构
        if main_group not in structure:
            structure[main_group] = {}
        if sub_team not in structure[main_group]:
            structure[main_group][sub_team] = {
                'count': 0,
                'squads': {},
                'members': [],
                'professions': {},
                'status': {'主力': 0, '替补': 0, '请假': 0}
            }
        if squad not in structure[main_group][sub_team]['squads']:
            structure[main_group][sub_team]['squads'][squad] = {
                'count': 0,
                'members': []
            }

        # 添加成员到结构中
        structure[main_group][sub_team]['count'] += 1
        structure[main_group][sub_team]['members'].append(member)
        structure[main_group][sub_team]['squads'][squad]['count'] += 1
        structure[main_group][sub_team]['squads'][squad]['members'].append(member)

        # 统计职业
        prof = member['profession']
        structure[main_group][sub_team]['professions'][prof] = \
            structure[main_group][sub_team]['professions'].get(prof, 0) + 1

        # 统计状态
        status = member.get('status', '主力')
        structure[main_group][sub_team]['status'][status] = \
            structure[main_group][sub_team]['status'].get(status, 0) + 1

    return structure

def get_member_battle_history(member_name):
    """获取成员的战斗历史记录"""
    try:
        battle_records = load_battle_records()
        member_battles = []

        for record in battle_records:
            member_performance = record.get('member_performance', {})
            if member_name in member_performance:
                performance = member_performance[member_name]
                battle_info = {
                    'battle_id': record.get('battle_id', ''),
                    'our_guild': record.get('our_guild', ''),
                    'enemy_guild': record.get('enemy_guild', ''),
                    'upload_time': record.get('upload_time', ''),
                    'score': performance.get('score', 0),
                    'battle_data': performance.get('battle_data', {}),
                    'bonus_items': performance.get('bonus_items', []),
                    'penalty_items': performance.get('penalty_items', [])
                }
                member_battles.append(battle_info)

        # 按时间排序
        member_battles.sort(key=lambda x: x['upload_time'])
        return member_battles

    except Exception as e:
        print(f"获取成员战斗历史失败: {e}")
        return []

# 路由定义

@app.route('/')
def index():
    """主页 - 重定向到成员详情页面"""
    return redirect(url_for('members_list'))

@app.route('/members')
def members_list():
    """成员列表页面"""
    members = load_members()

    # 获取三层级组织结构
    organization = get_organization_structure(members)

    # 为了兼容，也保留旧的team分组（用于统计）
    teams = {
        '1团': [],
        '2团': [],
        '3团': [],
        '4团': [],
        '未分配': []
    }

    for member in members:
        team = member.get('team', '未分配')
        if team not in teams:
            teams[team] = []
        teams[team].append(member)

    # 按名字排序
    for team in teams:
        teams[team].sort(key=lambda x: x['name'])

    # 创建响应并添加防缓存头
    response = make_response(render_template('members.html',
                                           members=members,
                                           teams=teams,
                                           organization=organization))
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'

    return response

@app.route('/member_detail/<member_name>')
def member_detail(member_name):
    """成员详情页面"""
    members = load_members()
    member = next((m for m in members if m['name'] == member_name), None)

    if not member:
        return redirect(url_for('members_list'))

    # 获取该成员的战斗历史
    battle_history = get_member_battle_history(member_name)

    return render_template('member_detail.html',
                         member=member,
                         battle_history=battle_history)

@app.route('/organization_chart')
def organization_chart():
    """组织架构图页面"""
    members = load_members()

    # 获取三层级组织结构
    organization = get_organization_structure(members)

    return render_template('organization_chart.html',
                         members=members,
                         organization=organization)

@app.route('/battle_analysis')
def battle_analysis():
    """战斗数据分析页面 - 显示所有战斗记录卡片"""
    # 加载所有战斗记录
    battle_records = load_battle_records()

    # 按时间倒序排列（最新的在前）
    battle_records.sort(key=lambda x: x['upload_time'], reverse=True)

    return render_template('battle_analysis.html',
                         battle_records=battle_records)

@app.route('/api/battle/<battle_id>')
def get_battle_detail(battle_id):
    """获取战斗详情API"""
    battle_records = load_battle_records()

    battle = None
    for record in battle_records:
        if record.get('battle_id') == battle_id:
            battle = record
            break

    if not battle:
        return jsonify({'error': '战斗记录不存在'}), 404

    return jsonify(battle)

@app.route('/api/members')
def api_members():
    """API: 获取所有成员"""
    members = load_members()
    return jsonify(members)

if __name__ == '__main__':
    # 确保必要目录存在
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    os.makedirs('../data', exist_ok=True)

    print("=" * 60)
    print("👥 纸落云烟帮会管理系统 - 成员访问系统启动中...")
    print("=" * 60)
    print("📊 功能包括:")
    print("   - 成员详情 (只读查看)")
    print("   - 组织架构 (只读查看)")
    print("   - 战斗分析 (只读查看)")
    print("   - 与管理后台数据实时同步")
    print()
    print("🌐 访问地址: http://localhost:5000")
    print("=" * 60)

    # 生产环境配置
    import os
    debug_mode = os.environ.get('FLASK_ENV') == 'development'
    port = int(os.environ.get('PORT', 5000))
    host = os.environ.get('HOST', '0.0.0.0')

    app.run(debug=debug_mode, host=host, port=port)
