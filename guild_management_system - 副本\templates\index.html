{% extends "base.html" %}

{% block title %}首页 - 纸落云烟帮会管理系统{% endblock %}

{% block content %}
<!-- 统计卡片 -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-number">{{ total_members }}</div>
        <div class="stat-label">总成员数</div>
    </div>
    <div class="stat-card">
        <div class="stat-number">4</div>
        <div class="stat-label">活跃团队</div>
    </div>
    <div class="stat-card">
        <div class="stat-number">{{ profession_stats|length }}</div>
        <div class="stat-label">职业类型</div>
    </div>
    <div class="stat-card">
        <div class="stat-number">100%</div>
        <div class="stat-label">数据完整度</div>
    </div>
</div>

<!-- 团队概览 -->
<div class="team-grid">
    {% for team_name, team_data in stats.items() %}
    <div class="team-card team-{{ team_name[0] }}">
        <div class="team-header">
            {{ team_name }} ({{ team_data.count }}人)
        </div>
        <div class="team-content">
            <div class="profession-breakdown">
                {% for prof, count in team_data.professions.items() %}
                <span class="profession-badge profession-{{ prof }}">{{ prof }}({{ count }}人)</span>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% endblock %}

{% block scripts %}
{% endblock %}
