{% extends "base.html" %}

{% block title %}组织架构图 - 纸落云烟帮会管理系统{% endblock %}

{% block content %}
<style>
    .org-chart {
        margin: 10px;
        max-width: 100%;
    }

    .battle-formation {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 10px;
        padding: 10px;
        box-sizing: border-box;
        align-items: start;
    }

    .formation-column {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.95) 100%);
        border-radius: 12px;
        padding: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(255,255,255,0.3);
        display: flex;
        flex-direction: column;
        backdrop-filter: blur(10px);
    }

    .column-header {
        margin: 0 0 15px 0;
        font-size: 1.1em;
        color: white;
        text-align: center;
        font-weight: 600;
        padding: 12px;
        border-radius: 8px;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .column-header.attack { background: linear-gradient(135deg, #00b894 0%, #00cec9 100%); }
    .column-header.defense { background: linear-gradient(135deg, #e17055 0%, #fd79a8 100%); }
    .column-header.special { background: linear-gradient(135deg, #636e72 0%, #2d3436 100%); }

    .squad-section {
        margin-bottom: 12px;
        background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,249,250,0.8) 100%);
        border-radius: 10px;
        padding: 12px;
        border-left: 4px solid #667eea;
        display: flex;
        flex-direction: column;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        transition: all 0.2s ease;
    }

    .squad-section:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    }

    .squad-title {
        font-size: 0.8em;
        font-weight: 600;
        color: #495057;
        margin-bottom: 10px;
        text-align: center;
        background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(240,242,247,0.95) 100%);
        padding: 6px 10px;
        border-radius: 6px;
        flex-shrink: 0;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }

    .squad-members {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        align-content: flex-start;
        justify-content: flex-start;
        min-height: 40px;
    }

    .member-card {
        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        padding: 6px 10px;
        border-radius: 10px;
        font-size: 0.75em;
        color: #495057;
        font-weight: 500;
        box-shadow: 0 2px 6px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        text-align: center;
        min-width: 55px;
        max-width: 75px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex-shrink: 0;
        border: 1px solid rgba(255,255,255,0.5);
        cursor: pointer;
    }

    .member-card:hover {
        transform: translateY(-2px) scale(1.02);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        background: linear-gradient(135deg, #dee2e6 0%, #ced4da 100%);
    }

    /* 特殊区域 */
    .special-area {
        background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,249,250,0.8) 100%);
        border-radius: 10px;
        padding: 12px;
        margin-bottom: 12px;
        border-left: 4px solid #6c757d;
        display: flex;
        flex-direction: column;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        transition: all 0.2s ease;
    }

    .special-area:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    }

    .special-title {
        font-size: 0.8em;
        font-weight: 600;
        color: #495057;
        margin-bottom: 10px;
        text-align: center;
        background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(240,242,247,0.95) 100%);
        padding: 6px 10px;
        border-radius: 6px;
        flex-shrink: 0;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }
        
    /* 职业颜色 */
    .member-card[data-profession="素问"] { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); color: #333; }
    .member-card[data-profession="九灵"] { background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%); color: #333; }
    .member-card[data-profession="潮光"] { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); color: white; }
    .member-card[data-profession="血河"] { background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%); color: white; }
    .member-card[data-profession="神相"] { background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%); color: white; }
    .member-card[data-profession="玄机"] { background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%); color: #333; }
    .member-card[data-profession="铁衣"] { background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%); color: #333; }
    .member-card[data-profession="龙吟"] { background: linear-gradient(135deg, #00b894 0%, #00cec9 100%); color: white; }
    .member-card[data-profession="碎梦"] { background: linear-gradient(135deg, #2d3436 0%, #636e72 100%); color: white; }
</style>

<div class="org-chart">
    <div class="battle-formation">
        <!-- 一团 -->
        <div class="formation-column">
            <div class="column-header attack">一团 (进攻)</div>
            {% if organization.get('进攻团', {}).get('一团', {}).get('squads') %}
                {% for squad_name, squad_data in organization['进攻团']['一团']['squads'].items() %}
                    <div class="squad-section">
                        <div class="squad-title">{{ squad_name }}</div>
                        <div class="squad-members">
                            {% for member in squad_data.members %}
                                <div class="member-card" data-profession="{{ member.profession }}">{{ member.name }}</div>
                            {% endfor %}
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
        </div>

        <!-- 二团 -->
        <div class="formation-column">
            <div class="column-header attack">二团 (进攻)</div>
            {% if organization.get('进攻团', {}).get('二团', {}).get('squads') %}
                {% for squad_name, squad_data in organization['进攻团']['二团']['squads'].items() %}
                    <div class="squad-section">
                        <div class="squad-title">{{ squad_name }}</div>
                        <div class="squad-members">
                            {% for member in squad_data.members %}
                                <div class="member-card" data-profession="{{ member.profession }}">{{ member.name }}</div>
                            {% endfor %}
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
        </div>

        <!-- 三团 -->
        <div class="formation-column">
            <div class="column-header attack">三团 (进攻)</div>
            {% if organization.get('进攻团', {}).get('三团', {}).get('squads') %}
                {% for squad_name, squad_data in organization['进攻团']['三团']['squads'].items() %}
                    <div class="squad-section">
                        <div class="squad-title">{{ squad_name }}</div>
                        <div class="squad-members">
                            {% for member in squad_data.members %}
                                <div class="member-card" data-profession="{{ member.profession }}">{{ member.name }}</div>
                            {% endfor %}
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
        </div>

        <!-- 防守团 -->
        <div class="formation-column">
            <div class="column-header defense">防守团</div>
            {% if organization.get('防守团', {}).get('防守团', {}).get('squads') %}
                {% for squad_name, squad_data in organization['防守团']['防守团']['squads'].items() %}
                    <div class="squad-section">
                        <div class="squad-title">{{ squad_name }}</div>
                        <div class="squad-members">
                            {% for member in squad_data.members %}
                                <div class="member-card" data-profession="{{ member.profession }}">{{ member.name }}</div>
                            {% endfor %}
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
        </div>

        <!-- 替补和请假 -->
        <div class="formation-column">
            <div class="column-header special">其他</div>

            <!-- 替补 -->
            {% if organization.get('其他团', {}).get('替补', {}).get('squads') %}
                <div class="special-area">
                    <div class="special-title">替补</div>
                    <div class="squad-members">
                        {% for squad_name, squad_data in organization['其他团']['替补']['squads'].items() %}
                            {% for member in squad_data.members %}
                                <div class="member-card" data-profession="{{ member.profession }}">{{ member.name }}</div>
                            {% endfor %}
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <!-- 请假 -->
            {% if organization.get('其他团', {}).get('请假', {}).get('squads') %}
                <div class="special-area">
                    <div class="special-title">请假</div>
                    <div class="squad-members">
                        {% for squad_name, squad_data in organization['其他团']['请假']['squads'].items() %}
                            {% for member in squad_data.members %}
                                <div class="member-card" data-profession="{{ member.profession }}">{{ member.name }}</div>
                            {% endfor %}
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{% endblock %}
