#!/bin/bash

# 检查服务状态

echo "📊 纸落云烟帮会管理系统 - 服务状态"
echo "=" * 50

# 检查端口占用情况
echo "🔍 端口占用情况:"
echo "   端口 5888 (管理后台):"
if netstat -tlnp 2>/dev/null | grep :5888 > /dev/null; then
    echo "   ✅ 正在运行"
    netstat -tlnp 2>/dev/null | grep :5888
else
    echo "   ❌ 未运行"
fi

echo "   端口 5002 (API服务):"
if netstat -tlnp 2>/dev/null | grep :5002 > /dev/null; then
    echo "   ✅ 正在运行"
    netstat -tlnp 2>/dev/null | grep :5002
else
    echo "   ❌ 未运行"
fi

echo "   端口 5000 (成员访问):"
if netstat -tlnp 2>/dev/null | grep :5000 > /dev/null; then
    echo "   ✅ 正在运行"
    netstat -tlnp 2>/dev/null | grep :5000
else
    echo "   ❌ 未运行"
fi

echo ""
echo "🔍 进程状态:"
ps aux | grep -E "(app\.py|api_app\.py|member_app\.py)" | grep -v grep

echo ""
echo "📋 PID文件状态:"
if [ -d "pids" ]; then
    for pidfile in pids/*.pid; do
        if [ -f "$pidfile" ]; then
            pid=$(cat "$pidfile")
            service=$(basename "$pidfile" .pid)
            if ps -p $pid > /dev/null; then
                echo "   ✅ $service: 运行中 (PID: $pid)"
            else
                echo "   ❌ $service: PID文件存在但进程不存在 (PID: $pid)"
            fi
        fi
    done
else
    echo "   ⚠️ 未找到PID目录"
fi

echo ""
echo "📁 日志文件:"
if [ -d "logs" ]; then
    for logfile in logs/*.log; do
        if [ -f "$logfile" ]; then
            size=$(du -h "$logfile" | cut -f1)
            echo "   📄 $(basename "$logfile"): $size"
        fi
    done
else
    echo "   ⚠️ 未找到日志目录"
fi

echo ""
echo "🌐 访问地址:"
echo "   🔧 管理后台: http://localhost:5888"
echo "   🔌 API服务:  http://localhost:5002"
echo "   👥 成员访问: http://localhost:5000"
echo "=" * 50
