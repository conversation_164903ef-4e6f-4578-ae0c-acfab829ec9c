#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os

def load_battle_records():
    """加载战斗记录"""
    try:
        # 获取脚本所在目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        file_path = os.path.join(script_dir, 'data', 'battle_records.json')
        print(f"尝试加载战斗记录: {file_path}")
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError as e:
        print(f"战斗记录文件未找到: {e}")
        return []

def load_members():
    """加载成员数据"""
    try:
        # 获取脚本所在目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        file_path = os.path.join(script_dir, 'data', 'guild_members.json')
        print(f"尝试加载成员数据: {file_path}")
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError as e:
        print(f"成员数据文件未找到: {e}")
        return []

def get_member_battle_history(member_name):
    """获取成员的战斗历史记录"""
    try:
        battle_records = load_battle_records()
        member_battles = []

        for record in battle_records:
            # 检查该成员是否参与了这场战斗
            member_performance = record.get('member_performance', {})
            if member_name in member_performance:
                performance = member_performance[member_name]

                # 提取战斗日期（从battle_id或upload_time）
                battle_date = extract_battle_date(record)

                battle_info = {
                    'battle_id': record['battle_id'],
                    'battle_date': battle_date,
                    'enemy_guild': record['enemy_guild'],
                    'score': performance.get('score', 0),
                    'rating': performance.get('rating', 'N/A'),
                    'battle_data': performance.get('battle_data', {}),
                    'bonus_items': performance.get('bonus_items', []),
                    'penalty_items': performance.get('penalty_items', []),
                    'details': performance.get('details', [])
                }
                member_battles.append(battle_info)

        # 按日期排序（最早的在前）
        member_battles.sort(key=lambda x: x['battle_date'])

        return member_battles

    except Exception as e:
        print(f"获取成员战斗历史失败: {e}")
        return []

def extract_battle_date(record):
    """从战斗记录中提取日期"""
    try:
        # 尝试从battle_id中提取日期
        battle_id = record.get('battle_id', '')
        if battle_id and '_' in battle_id:
            date_part = battle_id.split('_')[0]
            if len(date_part) == 8 and date_part.isdigit():
                # 格式：20250531 -> 2025-05-31
                year = date_part[:4]
                month = date_part[4:6]
                day = date_part[6:8]
                return f"{year}-{month}-{day}"

        # 如果无法从battle_id提取，使用upload_time
        upload_time = record.get('upload_time', '')
        if upload_time:
            # 格式：2025-06-02T15:18:56.543590 -> 2025-06-02
            return upload_time.split('T')[0]

        return '未知日期'

    except Exception as e:
        print(f"提取战斗日期失败: {e}")
        return '未知日期'

if __name__ == '__main__':
    print("🔍 调试成员详情功能")
    
    # 加载数据
    battle_records = load_battle_records()
    members = load_members()
    
    print(f"📊 战斗记录数量: {len(battle_records)}")
    print(f"👥 成员数量: {len(members)}")
    
    if battle_records:
        print(f"\n📋 第一场战斗记录:")
        first_record = battle_records[0]
        print(f"  - 战斗ID: {first_record['battle_id']}")
        print(f"  - 对手: {first_record['enemy_guild']}")
        print(f"  - 参战人数: {first_record['participated_members']}")
        
        # 获取前几个成员名字
        member_performance = first_record.get('member_performance', {})
        member_names = list(member_performance.keys())[:5]
        print(f"  - 前5个成员: {member_names}")
        
        # 测试第一个成员的战斗历史
        if member_names:
            test_member = member_names[0]
            print(f"\n🧪 测试成员: {test_member}")
            battle_history = get_member_battle_history(test_member)
            print(f"  - 战斗历史数量: {len(battle_history)}")
            
            if battle_history:
                print(f"  - 第一场战斗:")
                first_battle = battle_history[0]
                for key, value in first_battle.items():
                    if key != 'battle_data':  # 战斗数据太长，不显示
                        print(f"    {key}: {value}")
            else:
                print("  - 没有找到战斗历史")
    
    print("\n✅ 调试完成")
