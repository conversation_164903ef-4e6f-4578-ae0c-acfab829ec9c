# 根据提供的职业表格更新职业信息
import sys
import os
sys.path.append('nishuihan_analyzer/src')

from data_processor import DataProcessor

# 根据表格创建职业映射
profession_mapping = {
    # 从表格中提取的职业信息
    '林若道': '九灵',
    '逸尘': '素问', 
    '今念': '素问',
    '老铁真爱的清纯妹': '神相',
    '旧事': '碎梦',
    '秋实': '碎梦',
    '迟夏': '碎梦',
    '徐远师爷救苦': '龙吟',
    '折翼': '血河',
    '侯爵花开': '血河',
    '海妖瞳爱的太喜真': '潮光',
    '不连跳': '紫霞',
    
    # 其他已知的职业信息（从之前的分析中获得）
    '小辞星': '龙吟',
    '瑾亦': '九灵',
    '与假': '潮光',
    '不命': '九灵',
    '彧墨': '铁衣',
    '冬予眠': '铁衣',
    '烤全羊': '铁衣',
    '绝乂': '素问',
    '阿橘喵': '九灵',
    '望予': '神相',
    '小昭枝': '神相',
    '循梅': '神相',
    '彼岸花开乀': '龙吟',
    '小金丶': '龙吟',
    '祁寒': '龙吟',
    '楚晚寕': '铁衣',
    '月夜之瞳': '素问'
}

def update_profession_mapping():
    """更新职业映射并测试"""
    try:
        print("=" * 60)
        print("更新职业信息映射")
        print("=" * 60)
        
        # 加载数据处理器
        processor = DataProcessor("nishuihan_analyzer/data", "nishuihan_analyzer/纸落弈酒.xlsx")
        
        # 手动更新职业映射
        updated_count = 0
        for player_name, profession in profession_mapping.items():
            unique_id = f"{player_name}_{profession}"
            if player_name in processor.guild_members:
                # 如果玩家已存在但职业不同，更新职业信息
                processor.guild_members[unique_id] = {
                    'name': player_name,
                    'profession': profession,
                    'original_name': player_name
                }
                
                # 更新或创建职业列表
                if isinstance(processor.guild_members[player_name], list):
                    if profession not in processor.guild_members[player_name]:
                        processor.guild_members[player_name].append(profession)
                else:
                    processor.guild_members[player_name] = [profession]
                
                updated_count += 1
                print(f"✅ 更新: {player_name} -> {profession}")
            else:
                # 新增玩家
                processor.guild_members[unique_id] = {
                    'name': player_name,
                    'profession': profession,
                    'original_name': player_name
                }
                processor.guild_members[player_name] = [profession]
                updated_count += 1
                print(f"➕ 新增: {player_name} -> {profession}")
        
        print(f"\n总共更新了 {updated_count} 个职业映射")
        
        # 重新加载数据并分析
        print("\n重新分析数据...")
        raw_data = processor.load_all_data()
        df = processor.create_unified_dataframe()
        
        # 检查还有哪些职业不确定的玩家
        unknown_players = df[df['profession'].isin(['unknown', 'nil', 'nan', ''])]['name'].unique()
        
        print(f"\n剩余职业不确定的玩家: {len(unknown_players)}名")
        if len(unknown_players) > 0:
            print("仍需补充职业信息的玩家:")
            for i, player_name in enumerate(unknown_players, 1):
                player_records = df[df['name'] == player_name]
                avg_damage = player_records['player_damage'].mean()
                avg_healing = player_records['healing'].mean()
                avg_building_damage = player_records['building_damage'].mean()
                avg_damage_taken = player_records['damage_taken'].mean()
                
                print(f"{i:2d}. {player_name}")
                print(f"    平均对玩家伤害: {avg_damage:,.0f}")
                print(f"    平均治疗量: {avg_healing:,.0f}")
                print(f"    平均拆塔伤害: {avg_building_damage:,.0f}")
                print(f"    平均承受伤害: {avg_damage_taken:,.0f}")
                
                # 职业推测
                if avg_healing > 50000000:
                    suggested = "素问/九灵"
                elif avg_damage > 100000000:
                    suggested = "龙吟/铁衣/潮光/血河"
                elif avg_damage_taken > 80000000:
                    suggested = "铁衣/碎梦"
                else:
                    suggested = "神相/紫霞/其他"
                
                print(f"    建议职业: {suggested}")
                print()
        else:
            print("🎉 所有玩家的职业信息都已完整！")
        
        # 显示更新后的统计
        print("\n更新后的职业分布:")
        profession_dist = df['profession'].value_counts()
        for profession, count in profession_dist.items():
            if profession not in ['unknown', 'nil', 'nan', '']:
                print(f"  {profession}: {count}人次")
        
        return True
        
    except Exception as e:
        print(f"更新失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    update_profession_mapping()
