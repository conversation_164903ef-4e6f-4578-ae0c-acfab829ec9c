#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import random

def fix_member_data():
    """修复成员数据，添加main_group、sub_team字段，并合理分配队伍"""
    
    # 读取当前数据
    with open('data/guild_members.json', 'r', encoding='utf-8') as f:
        members = json.load(f)
    
    print(f"当前成员总数: {len(members)}")
    
    # 统计各团人数
    team_counts = {}
    for member in members:
        team = member.get('team', '未分配')
        team_counts[team] = team_counts.get(team, 0) + 1
    
    print("当前团队分布:")
    for team, count in team_counts.items():
        print(f"  {team}: {count}人")
    
    # 修复数据结构
    fixed_members = []
    
    # 分配计数器
    team_squad_counters = {
        '1团': {'current_squad': 1, 'current_count': 0},
        '2团': {'current_squad': 1, 'current_count': 0},
        '3团': {'current_squad': 1, 'current_count': 0},
        '4团': {'current_squad': 1, 'current_count': 0}  # 4团改为防守团
    }
    
    for member in members:
        # 复制基本信息
        fixed_member = {
            'name': member['name'],
            'profession': member['profession'],
            'team': member['team'],
            'position': member['position'],
            'status': member['status']
        }
        
        # 根据原team分配新的结构
        original_team = member['team']
        
        if original_team in ['1团', '2团', '3团']:
            # 进攻团
            fixed_member['main_group'] = '进攻团'
            fixed_member['sub_team'] = original_team
            
            # 分配到队伍（每队最多6人）
            counter = team_squad_counters[original_team]
            if counter['current_count'] >= 6:
                counter['current_squad'] += 1
                counter['current_count'] = 0
                if counter['current_squad'] > 5:
                    counter['current_squad'] = 5  # 最多5队
            
            fixed_member['squad'] = f"{counter['current_squad']}队"
            counter['current_count'] += 1
            
        elif original_team == '4团':
            # 防守团
            fixed_member['main_group'] = '防守团'
            fixed_member['sub_team'] = '防守团'
            fixed_member['team'] = '防守团'  # 更新team字段
            
            # 分配到队伍
            counter = team_squad_counters['4团']
            if counter['current_count'] >= 6:
                counter['current_squad'] += 1
                counter['current_count'] = 0
                if counter['current_squad'] > 5:
                    counter['current_squad'] = 5
            
            fixed_member['squad'] = f"{counter['current_squad']}队"
            counter['current_count'] += 1
            
        else:
            # 其他情况
            fixed_member['main_group'] = '其他团'
            fixed_member['sub_team'] = '一团'
            fixed_member['squad'] = '1队'
        
        # 保留原有的squad信息（如果有的话）
        if 'squad' in member and member['squad'] and member['squad'] not in ['医疗小队', '输出小队', '拆塔小队', '前排小队']:
            # 如果原来就有合理的squad，保持不变
            pass
        
        fixed_members.append(fixed_member)
    
    # 备份原文件
    with open('data/guild_members.json.compact_backup', 'w', encoding='utf-8') as f:
        json.dump(members, f, ensure_ascii=False, indent=2)
    
    # 保存修复后的数据
    with open('data/guild_members.json', 'w', encoding='utf-8') as f:
        json.dump(fixed_members, f, ensure_ascii=False, indent=2)
    
    print("\n修复完成！")
    print("数据结构已更新:")
    print("- 添加了main_group、sub_team字段")
    print("- 4团已改为防守团")
    print("- 成员已按每队最多6人重新分配")
    print("- 原数据已备份为guild_members.json.compact_backup")
    
    # 统计修复后的分布
    print("\n修复后的分布:")
    main_group_counts = {}
    for member in fixed_members:
        main_group = member['main_group']
        sub_team = member['sub_team']
        squad = member['squad']
        key = f"{main_group}-{sub_team}-{squad}"
        main_group_counts[key] = main_group_counts.get(key, 0) + 1
    
    for key, count in sorted(main_group_counts.items()):
        print(f"  {key}: {count}人")

if __name__ == '__main__':
    fix_member_data()
