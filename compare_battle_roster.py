#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对照CSV战斗数据，查找缺失的出战成员
"""

import json
import pandas as pd

def extract_battle_participants():
    """从CSV战斗数据中提取纸落云烟的出战成员"""
    print("=" * 60)
    print("📋 从CSV战斗数据中提取纸落云烟出战成员")
    print("=" * 60)
    
    csv_file = "20250531_203611_纸落云烟_初影未来.csv"
    
    try:
        # 读取CSV文件
        with open(csv_file, 'r', encoding='utf-8-sig') as f:
            lines = f.readlines()
    except UnicodeDecodeError:
        with open(csv_file, 'r', encoding='gbk') as f:
            lines = f.readlines()
    
    battle_members = []
    current_guild = None
    header_found = False
    
    for line_num, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue
            
        # 分割CSV行
        parts = [part.strip().replace('"', '') for part in line.split(',')]
        
        # 检查是否是帮会名称行
        if len(parts) >= 1 and parts[0] == '纸落云烟':
            current_guild = '纸落云烟'
            header_found = False
            print(f"找到纸落云烟帮会数据，行号: {line_num}")
            continue
        elif len(parts) >= 1 and parts[0] == '初影未来':
            # 遇到对手帮会，停止处理
            print(f"遇到对手帮会，停止处理，行号: {line_num}")
            break
            
        # 检查是否是表头行
        if current_guild == '纸落云烟' and not header_found:
            if len(parts) >= 2 and parts[0] == '玩家名字':
                header_found = True
                print(f"找到表头行，行号: {line_num}")
                continue
        
        # 提取成员数据
        if current_guild == '纸落云烟' and header_found:
            if len(parts) >= 2 and parts[0] and parts[1]:
                name = parts[0]
                profession = parts[1]
                
                # 跳过空行和无效数据
                if name and profession and name != '玩家名字':
                    battle_members.append({
                        'name': name,
                        'profession': profession,
                        'source': 'CSV战斗数据'
                    })
                    print(f"添加出战成员: {name} - {profession}")
    
    print(f"\n从CSV中提取到 {len(battle_members)} 名出战成员")
    return battle_members

def compare_with_guild_roster():
    """对比战斗数据和帮会名单"""
    print("\n" + "=" * 60)
    print("🔍 对比战斗数据与帮会名单")
    print("=" * 60)
    
    # 提取战斗数据中的成员
    battle_members = extract_battle_participants()
    
    # 读取帮会名单
    with open('final_guild_members.json', 'r', encoding='utf-8') as f:
        guild_members = json.load(f)
    
    # 创建名字集合
    battle_names = set(member['name'] for member in battle_members)
    guild_names = set(member['name'] for member in guild_members)
    
    print(f"战斗数据中的成员数: {len(battle_names)}")
    print(f"帮会名单中的成员数: {len(guild_names)}")
    
    # 找出差异
    missing_in_guild = battle_names - guild_names  # 在战斗数据中但不在帮会名单中
    missing_in_battle = guild_names - battle_names  # 在帮会名单中但不在战斗数据中
    common_members = battle_names & guild_names  # 共同成员
    
    print(f"\n📊 对比结果:")
    print(f"共同成员: {len(common_members)}人")
    print(f"只在战斗数据中: {len(missing_in_guild)}人")
    print(f"只在帮会名单中: {len(missing_in_battle)}人")
    
    if missing_in_guild:
        print(f"\n❗ 缺失成员（在战斗数据中但不在帮会名单中）:")
        for name in sorted(missing_in_guild):
            # 找到该成员的职业信息
            member_info = next((m for m in battle_members if m['name'] == name), None)
            if member_info:
                print(f"  - {name} ({member_info['profession']}) ⚠️ 需要添加到帮会名单")
    
    if missing_in_battle:
        print(f"\n📝 未参战成员（在帮会名单中但不在战斗数据中）:")
        for name in sorted(missing_in_battle):
            # 找到该成员的信息
            member_info = next((m for m in guild_members if m['name'] == name), None)
            if member_info:
                print(f"  - {name} ({member_info['profession']}) - {member_info['team']}")
    
    # 名字相似性检查（可能是名字有细微差异）
    print(f"\n🔍 检查可能的名字差异:")
    for battle_name in missing_in_guild:
        for guild_name in missing_in_battle:
            # 简单的相似性检查
            if len(battle_name) == len(guild_name):
                diff_count = sum(c1 != c2 for c1, c2 in zip(battle_name, guild_name))
                if diff_count <= 2:  # 最多2个字符不同
                    print(f"  可能匹配: '{battle_name}' (战斗) ↔ '{guild_name}' (名单)")
    
    return missing_in_guild, missing_in_battle, battle_members

def suggest_additions(missing_members, battle_members):
    """建议添加缺失成员"""
    if missing_members:
        print(f"\n" + "=" * 60)
        print("💡 建议添加的成员")
        print("=" * 60)
        
        for name in missing_members:
            member_info = next((m for m in battle_members if m['name'] == name), None)
            if member_info:
                print(f"建议添加成员:")
                print(f"  姓名: {member_info['name']}")
                print(f"  职业: {member_info['profession']}")
                print(f"  建议团队: 待分配")
                print(f"  建议位置: 根据职业分配")
                print()

def main():
    """主函数"""
    print("🔍 开始对比战斗数据与帮会名单...")
    
    # 对比数据
    missing_in_guild, missing_in_battle, battle_members = compare_with_guild_roster()
    
    # 建议添加
    suggest_additions(missing_in_guild, battle_members)
    
    if missing_in_guild:
        print(f"🎯 结论: 发现 {len(missing_in_guild)} 名成员需要添加到帮会名单中")
    else:
        print(f"✅ 结论: 所有出战成员都已在帮会名单中")

if __name__ == "__main__":
    main()
