#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细分析Excel文件，准确提取替补成员信息
"""

import pandas as pd
import json
import numpy as np

def analyze_excel_structure_detailed(excel_file):
    """详细分析Excel文件结构，找到替补成员位置"""
    print("=" * 60)
    print("详细分析Excel文件结构")
    print("=" * 60)
    
    try:
        # 读取团队管理表
        df = pd.read_excel(excel_file, sheet_name='团队管理表', header=None)
        
        print(f"Excel表格大小: {df.shape[0]}行 x {df.shape[1]}列")
        print()
        
        # 查找职业名称的位置
        professions = ['九灵', '素问', '神相', '玄机', '碎梦', '血河', '龙吟', '铁衣', '潮光']
        profession_positions = {}
        
        for row_idx in range(df.shape[0]):
            for col_idx in range(df.shape[1]):
                cell_value = df.iloc[row_idx, col_idx]
                if pd.notna(cell_value) and str(cell_value).strip() in professions:
                    profession = str(cell_value).strip()
                    profession_positions[profession] = (row_idx, col_idx)
                    print(f"找到职业 '{profession}' 在位置: 行{row_idx+1}, 列{col_idx+1}")
        
        print()
        
        # 对每个职业，查看其右侧的单元格内容（替补成员）
        substitutes = []
        
        for profession, (row, col) in profession_positions.items():
            print(f"\n--- 分析 {profession} 的替补成员 ---")
            
            # 查看该职业右侧的几列
            for check_col in range(col + 1, min(col + 6, df.shape[1])):
                cell_value = df.iloc[row, check_col]
                if pd.notna(cell_value):
                    cell_text = str(cell_value).strip()
                    
                    # 跳过明显不是玩家名字的内容
                    if (cell_text and 
                        len(cell_text) > 1 and
                        cell_text not in ['备用指挥', '资源'] and
                        '备用指挥' not in cell_text and
                        '资源' not in cell_text and
                        not cell_text.isdigit()):
                        
                        print(f"  列{check_col+1}: '{cell_text}'")
                        
                        # 判断是否是替补成员（不包括请假的）
                        if '请假' not in cell_text:
                            # 清理名字（去掉括号内容）
                            clean_name = cell_text.split('（')[0].split('(')[0].strip()
                            if clean_name and len(clean_name) >= 2:
                                substitutes.append({
                                    'name': clean_name,
                                    'profession': profession,
                                    'original_text': cell_text,
                                    'position': f"行{row+1}列{check_col+1}"
                                })
                                print(f"    -> 添加替补: {clean_name}")
        
        print(f"\n总共找到 {len(substitutes)} 名替补成员:")
        for sub in substitutes:
            print(f"  - {sub['name']} ({sub['profession']}) [原文: {sub['original_text']}]")
        
        return substitutes
        
    except Exception as e:
        print(f"分析Excel文件失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def show_excel_content_around_professions(excel_file):
    """显示职业周围的内容，帮助理解结构"""
    print("\n" + "=" * 60)
    print("显示职业周围的详细内容")
    print("=" * 60)
    
    try:
        df = pd.read_excel(excel_file, sheet_name='团队管理表', header=None)
        professions = ['九灵', '素问', '神相', '玄机', '碎梦', '血河', '龙吟', '铁衣', '潮光']
        
        for row_idx in range(df.shape[0]):
            for col_idx in range(df.shape[1]):
                cell_value = df.iloc[row_idx, col_idx]
                if pd.notna(cell_value) and str(cell_value).strip() in professions:
                    profession = str(cell_value).strip()
                    print(f"\n--- {profession} 周围内容 (行{row_idx+1}) ---")
                    
                    # 显示该行的所有内容
                    for c in range(max(0, col_idx-1), min(df.shape[1], col_idx+8)):
                        cell = df.iloc[row_idx, c]
                        if pd.notna(cell):
                            marker = " <-- 职业" if c == col_idx else ""
                            print(f"  列{c+1}: '{cell}'{marker}")
                        else:
                            print(f"  列{c+1}: [空]")
    
    except Exception as e:
        print(f"显示内容失败: {e}")

def main():
    """主函数"""
    excel_file = "纸落弈酒.xlsx"
    
    # 显示职业周围的详细内容
    show_excel_content_around_professions(excel_file)
    
    # 分析并提取替补成员
    substitutes = analyze_excel_structure_detailed(excel_file)
    
    # 保存结果
    if substitutes:
        with open('real_substitutes.json', 'w', encoding='utf-8') as f:
            json.dump(substitutes, f, ensure_ascii=False, indent=2)
        print(f"\n替补成员信息已保存到: real_substitutes.json")

if __name__ == "__main__":
    main()
