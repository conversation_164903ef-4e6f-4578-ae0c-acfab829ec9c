# 测试完整的分析系统
import sys
import os
sys.path.append('nishuihan_analyzer/src')

from data_processor import DataProcessor
from analyzer import NishuihanPlayerAnalyzer

# 测试完整分析流程
try:
    print("=" * 60)
    print("测试纸落云烟数据分析系统")
    print("=" * 60)

    # 1. 数据处理
    print("\n1. 数据处理...")
    processor = DataProcessor("nishuihan_analyzer/data", "nishuihan_analyzer/纸落弈酒.xlsx")
    print(f"  - 加载了 {len([k for k in processor.guild_members.keys() if '_' in k])} 个玩家-职业组合")

    raw_data = processor.load_all_data()
    print(f"  - 成功加载 {len(raw_data)} 场战斗")

    df = processor.create_unified_dataframe()
    print(f"  - 纸落云烟玩家记录: {len(df)}")

    # 2. 数据分析
    print("\n2. 数据分析...")
    analyzer = NishuihanPlayerAnalyzer(df)

    # 计算玩家评分
    player_stats = analyzer.calculate_player_scores()
    print(f"  - 计算了 {len(player_stats)} 名玩家的综合评分")

    # 时间序列分析
    time_series_results = analyzer.analyze_time_series()
    print(f"  - 完成了 {len(time_series_results)} 名玩家的时间序列分析")

    # 位置适合性分析
    position_analysis = analyzer.analyze_position_suitability()
    print(f"  - 完成了 {len(position_analysis)} 名玩家的位置适合性分析")

    # 3. 结果展示
    print("\n3. 分析结果:")

    # 显示前5名玩家
    print("\n战力排行榜前5名:")
    top_5 = player_stats.nlargest(5, 'combat_score')
    for i, (unique_id, data) in enumerate(top_5.iterrows(), 1):
        print(f"  {i}. {data['name_first']} ({data['profession_first']}) - 战力: {data['combat_score']:.1f}")

    # 显示进步最快的5名玩家
    print("\n进步最快的5名玩家:")
    progress_list = []
    for unique_id, analysis in time_series_results.items():
        progress_list.append({
            'name': analysis['name'],
            'profession': analysis['profession'],
            'progress_score': analysis['progress_assessment']['progress_score'],
            'progress_level': analysis['progress_assessment']['progress_level']
        })

    progress_list.sort(key=lambda x: x['progress_score'], reverse=True)
    for i, player in enumerate(progress_list[:5], 1):
        print(f"  {i}. {player['name']} ({player['profession']}) - {player['progress_level']} ({player['progress_score']:+.2f})")

    # 显示各位置最佳人选
    print("\n各位置最佳人选:")
    position_names = {
        'damage_dealer': '输出位',
        'tank': '坦克位',
        'support': '辅助位',
        'demolisher': '拆塔位'
    }

    for position, name in position_names.items():
        rankings = analyzer.get_position_rankings(position, 3)
        print(f"  {name}:")
        for i, player in enumerate(rankings, 1):
            best_mark = " ★" if player['is_best_position'] else ""
            print(f"    {i}. {player['name']} ({player['profession']}) - {player['score']:.1f}{best_mark}")

    print("\n=" * 60)
    print("测试完成！系统运行正常。")
    print("=" * 60)

except Exception as e:
    print(f"测试时出错: {e}")
    import traceback
    traceback.print_exc()
