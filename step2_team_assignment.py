#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第二步：为主力成员分配团队和位置
"""

import json

def load_main_members():
    """加载主力成员"""
    with open('guild_members.json', 'r', encoding='utf-8') as f:
        members = json.load(f)
    
    # 只保留主力成员（从CSV中提取的60人）
    main_members = []
    for member in members:
        main_members.append({
            'name': member['name'],
            'profession': member['profession'],
            'team': '',  # 待分配：1团、2团、3团、防守团
            'position': '',  # 待分配：输出、坦克、辅助、拆塔等
            'status': '主力'
        })
    
    return main_members

def suggest_team_positions():
    """根据职业特点建议团队分配和位置"""
    
    # 职业特点和建议位置
    profession_roles = {
        '素问': {
            'primary_role': '治疗',
            'secondary_role': '辅助',
            'team_preference': ['1团', '2团', '3团', '防守团'],  # 每个团都需要
            'description': '主要治疗职业，每个团队都需要'
        },
        '潮光': {
            'primary_role': '输出',
            'secondary_role': '辅助',
            'team_preference': ['1团', '2团', '3团'],
            'description': '输出职业，适合进攻团'
        },
        '九灵': {
            'primary_role': '输出',
            'secondary_role': '拆塔',
            'team_preference': ['1团', '2团', '3团'],
            'description': '高输出职业，适合进攻团'
        },
        '铁衣': {
            'primary_role': '坦克',
            'secondary_role': '前排',
            'team_preference': ['1团', '2团', '3团', '防守团'],
            'description': '坦克职业，每个团队都需要'
        },
        '玄机': {
            'primary_role': '拆塔',
            'secondary_role': '输出',
            'team_preference': ['1团', '2团', '3团'],
            'description': '拆塔专业，适合进攻团'
        },
        '龙吟': {
            'primary_role': '输出',
            'secondary_role': '拆塔',
            'team_preference': ['1团', '2团', '3团'],
            'description': '输出职业，适合进攻团'
        },
        '血河': {
            'primary_role': '输出',
            'secondary_role': '爆发',
            'team_preference': ['1团', '2团', '3团', '防守团'],
            'description': '高爆发输出，进攻防守都适合'
        },
        '神相': {
            'primary_role': '拆塔',
            'secondary_role': '辅助',
            'team_preference': ['1团', '2团', '3团'],
            'description': '拆塔辅助，适合进攻团'
        },
        '碎梦': {
            'primary_role': '输出',
            'secondary_role': '控制',
            'team_preference': ['防守团', '1团', '2团', '3团'],
            'description': '控制输出，防守团优先'
        }
    }
    
    return profession_roles

def auto_assign_teams(members):
    """自动分配团队（基础建议）"""
    
    # 团队配置目标
    team_config = {
        '1团': {'target_size': 15, 'members': []},
        '2团': {'target_size': 15, 'members': []},
        '3团': {'target_size': 15, 'members': []},
        '防守团': {'target_size': 15, 'members': []}
    }
    
    # 按职业分组
    profession_groups = {}
    for member in members:
        prof = member['profession']
        if prof not in profession_groups:
            profession_groups[prof] = []
        profession_groups[prof].append(member)
    
    # 职业分配策略
    profession_roles = suggest_team_positions()
    
    print("=" * 60)
    print("自动团队分配建议")
    print("=" * 60)
    
    # 为每个职业建议分配
    for profession, members_list in profession_groups.items():
        role_info = profession_roles.get(profession, {})
        primary_role = role_info.get('primary_role', '输出')
        
        print(f"\n【{profession}】({len(members_list)}人) - 主要角色：{primary_role}")
        print(f"建议分配：{role_info.get('description', '')}")
        
        # 平均分配到各团队
        teams = role_info.get('team_preference', ['1团', '2团', '3团', '防守团'])
        team_count = len(teams)
        
        for i, member in enumerate(members_list):
            assigned_team = teams[i % team_count]
            member['team'] = assigned_team
            member['position'] = primary_role
            team_config[assigned_team]['members'].append(member)
            print(f"  {member['name']} -> {assigned_team} ({primary_role})")
    
    # 显示团队配置结果
    print(f"\n" + "=" * 60)
    print("团队配置结果")
    print("=" * 60)
    
    for team_name, config in team_config.items():
        members_list = config['members']
        print(f"\n【{team_name}】({len(members_list)}人)：")
        
        # 按职业统计
        team_prof_count = {}
        for member in members_list:
            prof = member['profession']
            team_prof_count[prof] = team_prof_count.get(prof, 0) + 1
        
        for prof, count in sorted(team_prof_count.items()):
            prof_members = [m['name'] for m in members_list if m['profession'] == prof]
            print(f"  {prof}({count}): {', '.join(prof_members)}")
    
    return members

def create_editable_template(members):
    """创建可编辑的团队分配模板"""
    
    template = {
        'guild_name': '纸落云烟',
        'total_members': len(members),
        'teams': {
            '1团': [],
            '2团': [],
            '3团': [],
            '防守团': []
        },
        'members': members,
        'profession_roles': suggest_team_positions()
    }
    
    # 按团队分组
    for member in members:
        team = member.get('team', '未分配')
        if team in template['teams']:
            template['teams'][team].append({
                'name': member['name'],
                'profession': member['profession'],
                'position': member['position']
            })
    
    return template

def main():
    """主函数"""
    print("=" * 80)
    print("第二步：纸落云烟帮会团队分配")
    print("=" * 80)
    
    # 加载主力成员
    main_members = load_main_members()
    print(f"加载主力成员：{len(main_members)}人")
    
    # 显示职业建议
    profession_roles = suggest_team_positions()
    print(f"\n职业角色建议：")
    for prof, info in profession_roles.items():
        print(f"  {prof}: {info['primary_role']} - {info['description']}")
    
    # 自动分配团队
    assigned_members = auto_assign_teams(main_members)
    
    # 创建可编辑模板
    template = create_editable_template(assigned_members)
    
    # 保存结果
    with open('team_assignment.json', 'w', encoding='utf-8') as f:
        json.dump(template, f, ensure_ascii=False, indent=2)
    
    with open('members_with_teams.json', 'w', encoding='utf-8') as f:
        json.dump(assigned_members, f, ensure_ascii=False, indent=2)
    
    print(f"\n" + "=" * 80)
    print("✅ 第二步完成！")
    print("📋 生成文件：")
    print("   - team_assignment.json (团队分配模板)")
    print("   - members_with_teams.json (带团队信息的成员列表)")
    print()
    print("🎯 团队分配说明：")
    print("   - 这是基于职业特点的自动分配建议")
    print("   - 您可以根据实际情况调整团队分配")
    print("   - 每个团队都配置了坦克、治疗、输出的基本组合")
    print()
    print("📝 下一步：创建Web管理界面进行编辑")
    print("=" * 80)

if __name__ == "__main__":
    main()
