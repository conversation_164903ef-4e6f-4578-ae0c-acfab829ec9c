#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从Excel团队管理表中提取正确的团队分配信息
"""

import pandas as pd
import json

def analyze_team_management_sheet(excel_file):
    """分析团队管理表的结构"""
    print("=" * 60)
    print("分析Excel团队管理表结构")
    print("=" * 60)
    
    try:
        # 读取团队管理表
        df = pd.read_excel(excel_file, sheet_name='团队管理表', header=None)
        
        print(f"团队管理表大小: {df.shape[0]}行 x {df.shape[1]}列")
        
        # 查找团队标识
        team_keywords = ['一团', '二团', '三团', '1团', '2团', '3团', '防守团', '进攻团']
        team_positions = {}
        
        print("\n查找团队标识...")
        for row_idx in range(df.shape[0]):
            for col_idx in range(df.shape[1]):
                cell_value = df.iloc[row_idx, col_idx]
                if pd.notna(cell_value):
                    cell_text = str(cell_value).strip()
                    for keyword in team_keywords:
                        if keyword in cell_text:
                            print(f"找到团队标识 '{cell_text}' 在位置: 行{row_idx+1}, 列{col_idx+1}")
                            team_positions[cell_text] = (row_idx, col_idx)
        
        # 显示每个团队周围的内容
        print(f"\n显示团队周围的详细内容...")
        for team_name, (row, col) in team_positions.items():
            print(f"\n--- {team_name} 周围内容 (行{row+1}) ---")
            
            # 显示该行及周围几行的内容
            for check_row in range(max(0, row-2), min(df.shape[0], row+10)):
                row_content = []
                for check_col in range(max(0, col-2), min(df.shape[1], col+8)):
                    cell = df.iloc[check_row, check_col]
                    if pd.notna(cell):
                        marker = " <-- 团队" if check_row == row and check_col == col else ""
                        row_content.append(f"列{check_col+1}:'{cell}'{marker}")
                
                if row_content:
                    print(f"  行{check_row+1}: {' | '.join(row_content)}")
        
        return team_positions
        
    except Exception as e:
        print(f"分析团队管理表失败: {e}")
        import traceback
        traceback.print_exc()
        return {}

def extract_team_assignments(excel_file):
    """提取正确的团队分配"""
    print("\n" + "=" * 60)
    print("提取团队分配信息")
    print("=" * 60)
    
    try:
        df = pd.read_excel(excel_file, sheet_name='团队管理表', header=None)
        
        # 查找成员和其对应的团队信息
        team_assignments = {}
        
        # 加载现有成员列表
        with open('guild_members.json', 'r', encoding='utf-8') as f:
            guild_members = json.load(f)
        
        member_names = [m['name'] for m in guild_members]
        
        print(f"查找 {len(member_names)} 名成员的团队分配...")
        
        # 遍历表格查找成员名字和团队信息
        for row_idx in range(df.shape[0]):
            for col_idx in range(df.shape[1]):
                cell_value = df.iloc[row_idx, col_idx]
                if pd.notna(cell_value):
                    cell_text = str(cell_value).strip()
                    
                    # 检查是否是成员名字
                    if cell_text in member_names:
                        print(f"\n找到成员 '{cell_text}' 在位置: 行{row_idx+1}, 列{col_idx+1}")
                        
                        # 查找该成员的团队信息（在同行或附近查找团队标识）
                        team_info = None
                        
                        # 在同行查找团队信息
                        for check_col in range(max(0, col_idx-10), min(df.shape[1], col_idx+10)):
                            check_cell = df.iloc[row_idx, check_col]
                            if pd.notna(check_cell):
                                check_text = str(check_cell).strip()
                                if any(keyword in check_text for keyword in ['一团', '二团', '三团', '1团', '2团', '3团', '防守团']):
                                    team_info = check_text
                                    print(f"  -> 在同行找到团队信息: {team_info}")
                                    break
                        
                        # 如果同行没找到，在附近行查找
                        if not team_info:
                            for check_row in range(max(0, row_idx-5), min(df.shape[0], row_idx+5)):
                                for check_col in range(max(0, col_idx-5), min(df.shape[1], col_idx+5)):
                                    check_cell = df.iloc[check_row, check_col]
                                    if pd.notna(check_cell):
                                        check_text = str(check_cell).strip()
                                        if any(keyword in check_text for keyword in ['一团', '二团', '三团', '1团', '2团', '3团', '防守团']):
                                            team_info = check_text
                                            print(f"  -> 在附近找到团队信息: {team_info} (行{check_row+1})")
                                            break
                                if team_info:
                                    break
                        
                        if team_info:
                            team_assignments[cell_text] = team_info
                        else:
                            print(f"  -> 未找到团队信息")
        
        print(f"\n成功提取 {len(team_assignments)} 名成员的团队分配:")
        for member, team in team_assignments.items():
            print(f"  {member} -> {team}")
        
        return team_assignments
        
    except Exception as e:
        print(f"提取团队分配失败: {e}")
        import traceback
        traceback.print_exc()
        return {}

def update_members_with_correct_teams():
    """用正确的团队信息更新成员数据"""
    print("\n" + "=" * 60)
    print("更新成员团队信息")
    print("=" * 60)
    
    # 提取团队分配
    team_assignments = extract_team_assignments("纸落弈酒.xlsx")
    
    # 加载现有成员数据
    with open('guild_members.json', 'r', encoding='utf-8') as f:
        members = json.load(f)
    
    # 团队名称标准化
    team_mapping = {
        '一团': '1团',
        '二团': '2团', 
        '三团': '3团',
        '1团': '1团',
        '2团': '2团',
        '3团': '3团',
        '防守团': '防守团'
    }
    
    # 更新成员团队信息
    updated_count = 0
    for member in members:
        member_name = member['name']
        if member_name in team_assignments:
            original_team = team_assignments[member_name]
            # 标准化团队名称
            for key, value in team_mapping.items():
                if key in original_team:
                    member['team'] = value
                    member['status'] = '主力'
                    updated_count += 1
                    print(f"更新 {member_name}: {original_team} -> {value}")
                    break
        else:
            member['team'] = '未分配'
            member['status'] = '主力'
    
    # 保存更新后的数据
    with open('members_with_correct_teams.json', 'w', encoding='utf-8') as f:
        json.dump(members, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 成功更新 {updated_count} 名成员的团队信息")
    print(f"📁 保存到: members_with_correct_teams.json")
    
    # 统计团队分布
    team_stats = {}
    for member in members:
        team = member.get('team', '未分配')
        team_stats[team] = team_stats.get(team, 0) + 1
    
    print(f"\n团队分布统计:")
    for team, count in sorted(team_stats.items()):
        print(f"  {team}: {count}人")
    
    return members

def main():
    """主函数"""
    excel_file = "纸落弈酒.xlsx"
    
    # 分析团队管理表结构
    team_positions = analyze_team_management_sheet(excel_file)
    
    # 更新成员团队信息
    updated_members = update_members_with_correct_teams()
    
    print(f"\n" + "=" * 60)
    print("✅ 团队分配修正完成！")
    print("现在使用Excel中的正确团队分配信息")
    print("=" * 60)

if __name__ == "__main__":
    main()
