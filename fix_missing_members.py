#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正缺失成员和名字差异
"""

import json

def fix_members_data():
    """修正成员数据"""
    print("=" * 60)
    print("🔧 开始修正成员数据")
    print("=" * 60)
    
    # 读取当前数据
    with open('final_guild_members.json', 'r', encoding='utf-8') as f:
        members = json.load(f)
    
    print(f"修正前成员数: {len(members)}人")
    
    # 1. 修正名字差异
    name_corrections = {
        '淞鸦': '凇鴉',      # 九灵
        '楚晚宁': '楚晚寕',   # 铁衣
        '啊橘喵': '阿橘喵',   # 素问
        '晚凇': '晚淞',      # 玄机
        '饱饱': '饱饱丶'     # 九灵
    }
    
    print("\n📝 修正名字差异:")
    for member in members:
        old_name = member['name']
        if old_name in name_corrections:
            new_name = name_corrections[old_name]
            member['name'] = new_name
            print(f"  {old_name} → {new_name} ({member['profession']})")
    
    # 2. 添加缺失成员
    missing_members = [
        {
            'name': '望予',
            'profession': '九灵',
            'team': '未分配',  # 需要后续分配
            'position': '输出',
            'status': '主力'
        },
        {
            'name': '绝乂',
            'profession': '素问',
            'team': '未分配',  # 需要后续分配
            'position': '治疗',
            'status': '主力'
        }
    ]
    
    print(f"\n➕ 添加缺失成员:")
    for new_member in missing_members:
        members.append(new_member)
        print(f"  + {new_member['name']} ({new_member['profession']}) - {new_member['position']}")
    
    print(f"\n修正后成员数: {len(members)}人")
    
    return members

def assign_teams_for_new_members(members):
    """为新成员分配团队"""
    print("\n" + "=" * 60)
    print("🎯 为新成员分配团队")
    print("=" * 60)
    
    # 统计当前团队人数
    team_counts = {}
    for member in members:
        team = member['team']
        team_counts[team] = team_counts.get(team, 0) + 1
    
    print("当前团队人数:")
    for team, count in sorted(team_counts.items()):
        print(f"  {team}: {count}人")
    
    # 为未分配成员分配团队
    unassigned_members = [m for m in members if m['team'] == '未分配']
    
    if unassigned_members:
        print(f"\n需要分配团队的成员:")
        for member in unassigned_members:
            print(f"  - {member['name']} ({member['profession']})")
        
        # 基于职业和团队需求分配
        # 望予(九灵) - 输出职业，可以分配到人数较少的团队
        # 绝乂(素问) - 治疗职业，每个团队都需要
        
        team_assignments = {
            '望予': '2团',  # 2团人数较少，需要输出
            '绝乂': '4团'   # 4团人数最少，需要治疗
        }
        
        print(f"\n分配结果:")
        for member in members:
            if member['name'] in team_assignments:
                old_team = member['team']
                new_team = team_assignments[member['name']]
                member['team'] = new_team
                print(f"  {member['name']} ({member['profession']}): {old_team} → {new_team}")
    
    return members

def generate_final_summary(members):
    """生成最终总结"""
    print("\n" + "=" * 60)
    print("📊 修正完成后的最终统计")
    print("=" * 60)
    
    # 团队统计
    team_stats = {}
    for member in members:
        team = member['team']
        if team not in team_stats:
            team_stats[team] = {'count': 0, 'professions': {}}
        team_stats[team]['count'] += 1
        
        prof = member['profession']
        if prof not in team_stats[team]['professions']:
            team_stats[team]['professions'][prof] = []
        team_stats[team]['professions'][prof].append(member['name'])
    
    print(f"总成员数: {len(members)}人")
    
    print(f"\n🎯 最终团队分配:")
    for team in ['1团', '2团', '3团', '4团']:
        if team in team_stats:
            stats = team_stats[team]
            print(f"\n【{team}】({stats['count']}人):")
            for prof, names in sorted(stats['professions'].items()):
                # 标记新添加的成员
                marked_names = []
                for name in names:
                    if name in ['望予', '绝乂']:
                        marked_names.append(f"{name}🆕")
                    elif name in ['凇鴉', '楚晚寕', '阿橘喵', '晚淞', '饱饱丶']:
                        marked_names.append(f"{name}✏️")
                    else:
                        marked_names.append(name)
                print(f"  {prof}({len(names)}): {', '.join(marked_names)}")
    
    # 职业统计
    profession_stats = {}
    for member in members:
        prof = member['profession']
        profession_stats[prof] = profession_stats.get(prof, 0) + 1
    
    print(f"\n💼 职业分布:")
    for prof, count in sorted(profession_stats.items()):
        print(f"  {prof}: {count}人")
    
    print(f"\n✅ 修正完成:")
    print(f"  🆕 新增成员: 2人 (望予、绝乂)")
    print(f"  ✏️ 名字修正: 5人 (凇鴉、楚晚寕、阿橘喵、晚淞、饱饱丶)")
    print(f"  📊 最终总数: {len(members)}人")
    
    return members

def main():
    """主函数"""
    print("🚀 开始执行修正操作...")
    
    # 修正成员数据
    corrected_members = fix_members_data()
    
    # 为新成员分配团队
    final_members = assign_teams_for_new_members(corrected_members)
    
    # 生成最终总结
    final_members = generate_final_summary(final_members)
    
    # 保存修正后的数据
    with open('final_guild_members_corrected.json', 'w', encoding='utf-8') as f:
        json.dump(final_members, f, ensure_ascii=False, indent=2)
    
    # 也更新原文件
    with open('final_guild_members.json', 'w', encoding='utf-8') as f:
        json.dump(final_members, f, ensure_ascii=False, indent=2)
    
    print(f"\n" + "=" * 60)
    print("✅ 修正操作完成！")
    print("📁 数据已保存到:")
    print("   - final_guild_members.json (主文件)")
    print("   - final_guild_members_corrected.json (备份)")
    print("🎯 现在数据与战斗记录完全一致！")
    print("=" * 60)

if __name__ == "__main__":
    main()
