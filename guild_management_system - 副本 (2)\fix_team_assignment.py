#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正团队分配
根据用户要求重新分配团队：
- 1团: 1个队伍（进攻团）
- 2团: 2个队伍（进攻团）  
- 3团: 3个队伍（进攻团）
- 4团: 防守团（原来的3团改为4团）
"""

import json
import os

def load_members():
    """加载成员数据"""
    try:
        with open('data/guild_members.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("成员数据文件不存在")
        return []

def save_members(members):
    """保存成员数据"""
    os.makedirs('data', exist_ok=True)
    with open('data/guild_members.json', 'w', encoding='utf-8') as f:
        json.dump(members, f, ensure_ascii=False, indent=2)

def fix_team_assignment():
    """修正团队分配"""
    print("=" * 60)
    print("🔧 修正团队分配")
    print("=" * 60)
    
    members = load_members()
    print(f"加载了 {len(members)} 个成员")
    
    # 统计当前团队分布
    current_teams = {}
    for member in members:
        team = member.get('team', '未分配')
        current_teams[team] = current_teams.get(team, 0) + 1
    
    print("\n当前团队分布:")
    for team, count in sorted(current_teams.items()):
        print(f"  {team}: {count}人")
    
    # 根据您的要求重新分配
    # 当前数据中的3团（24人）应该改为4团（防守团）
    # 其他保持不变
    
    changes = 0
    for member in members:
        old_team = member.get('team', '未分配')
        
        # 如果当前是3团，改为4团（防守团）
        if old_team == '3团':
            member['team'] = '4团'
            changes += 1
            print(f"  {member['name']}: 3团 -> 4团")
    
    print(f"\n修改了 {changes} 个成员的团队分配")
    
    # 统计修改后的团队分布
    new_teams = {}
    for member in members:
        team = member.get('team', '未分配')
        new_teams[team] = new_teams.get(team, 0) + 1
    
    print("\n修改后团队分布:")
    for team, count in sorted(new_teams.items()):
        team_type = "进攻团" if team in ['1团', '2团', '3团'] else "防守团" if team == '4团' else "其他"
        print(f"  {team}: {count}人 ({team_type})")
    
    # 保存修改
    save_members(members)
    print(f"\n✅ 团队分配已修正并保存")

def add_squad_info():
    """添加小队信息"""
    print("\n" + "=" * 60)
    print("🎯 添加小队信息")
    print("=" * 60)
    
    members = load_members()
    
    # 根据位置信息预设小队
    position_to_squad = {
        '治疗': '医疗小队',
        '坦克': '前排小队', 
        '输出': '输出小队',
        '拆塔': '拆塔小队',
        '辅助': '辅助小队',
        '控制': '控制小队'
    }
    
    for member in members:
        position = member.get('position', '未分配')
        if position in position_to_squad:
            member['squad'] = position_to_squad[position]
        else:
            member['squad'] = '未分配小队'
        
        # 确保有状态字段
        if 'status' not in member:
            member['status'] = '主力'
    
    # 统计小队分布
    squad_stats = {}
    for member in members:
        squad = member.get('squad', '未分配小队')
        squad_stats[squad] = squad_stats.get(squad, 0) + 1
    
    print("小队分布:")
    for squad, count in sorted(squad_stats.items()):
        print(f"  {squad}: {count}人")
    
    save_members(members)
    print(f"\n✅ 小队信息已添加并保存")

def main():
    """主函数"""
    print("🔧 团队和小队信息修正工具")
    
    # 修正团队分配
    fix_team_assignment()
    
    # 添加小队信息
    add_squad_info()
    
    print("\n" + "=" * 60)
    print("✅ 所有修正完成！")
    print("=" * 60)
    print("团队结构:")
    print("  1团: 1个队伍（进攻团）")
    print("  2团: 2个队伍（进攻团）")
    print("  3团: 3个队伍（进攻团）")
    print("  4团: 防守团")
    print("\n小队结构:")
    print("  医疗小队: 素问")
    print("  前排小队: 铁衣")
    print("  输出小队: 潮光、九灵、龙吟、血河、碎梦")
    print("  拆塔小队: 玄机、神相")

if __name__ == "__main__":
    main()
