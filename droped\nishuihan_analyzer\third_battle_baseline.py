"""
第三场战斗基准分析
以第三场战斗为基准，分析每个人的职业和位置表现
"""

import sys
import os
import pandas as pd
import json

# 添加路径
sys.path.append('src')

from data_processor import DataProcessor

def analyze_third_battle_baseline():
    """以第三场战斗为基准进行分析"""
    try:
        print("=" * 80)
        print("第三场战斗基准分析 - 2025526_233916_纸落云烟_画堂春")
        print("=" * 80)
        
        # 加载数据
        processor = DataProcessor("data", "纸落弈酒.xlsx")
        raw_data = processor.load_all_data()
        df = processor.create_unified_dataframe()
        
        # 找到第三场战斗（画堂春）
        third_battle_date = None
        for battle in raw_data:
            if "画堂春" in battle['filename'] or "2025526_233916" in battle['filename']:
                third_battle_date = battle['date']
                print(f"📅 找到第三场战斗: {battle['filename']}")
                print(f"📅 战斗日期: {battle['date']}")
                break
        
        if not third_battle_date:
            print("❌ 没有找到第三场战斗数据")
            return None
        
        # 获取第三场战斗数据
        # 由于日期格式问题，我们直接分析所有数据（因为目前只有一个日期的数据）
        battle_df = df.copy()
        
        print(f"👥 第三场战斗参战人数: {len(battle_df)}人")
        
        # 创建基准分析
        baseline_analysis = create_baseline_analysis(battle_df)
        
        # 生成位置基准
        position_baseline = create_position_baseline(baseline_analysis)
        
        # 生成职业基准
        profession_baseline = create_profession_baseline(baseline_analysis)
        
        # 保存基准数据
        baseline_data = {
            'battle_info': {
                'name': '画堂春',
                'date': third_battle_date,
                'participants': len(battle_df)
            },
            'baseline_analysis': baseline_analysis,
            'position_baseline': position_baseline,
            'profession_baseline': profession_baseline,
            'recommendations': generate_baseline_recommendations(baseline_analysis, position_baseline)
        }
        
        with open('third_battle_baseline.json', 'w', encoding='utf-8') as f:
            json.dump(baseline_data, f, ensure_ascii=False, indent=2)
        
        # 生成基准CSV
        generate_baseline_csv(baseline_analysis)
        
        # 显示分析结果
        display_baseline_results(baseline_data)
        
        print(f"\n✅ 第三场战斗基准分析完成！")
        print(f"   基准数据: third_battle_baseline.json")
        print(f"   基准CSV: third_battle_baseline.csv")
        
        return baseline_data
        
    except Exception as e:
        print(f"❌ 基准分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_baseline_analysis(battle_df):
    """创建基准分析"""
    print(f"\n📊 创建第三场战斗基准分析...")
    
    baseline_analysis = []
    
    # 按伤害排序分析每个玩家
    sorted_players = battle_df.sort_values('player_damage', ascending=False)
    
    for rank, (_, player) in enumerate(sorted_players.iterrows(), 1):
        # 详细位置分析
        position_analysis = analyze_detailed_position_v2(player, battle_df)
        
        # 计算基准评分
        baseline_score = calculate_baseline_score(player, battle_df)
        
        # 分析角色特征
        role_characteristics = analyze_role_characteristics(player, battle_df)
        
        analysis = {
            'rank': rank,
            'name': player['name'],
            'profession': player['profession'],
            'baseline_position': position_analysis['primary_position'],
            'position_confidence': position_analysis['confidence'],
            'alternative_positions': position_analysis['alternative_positions'],
            'baseline_score': baseline_score,
            'role_characteristics': role_characteristics,
            'stats': {
                'kills': int(player['kills']),
                'assists': int(player['assists']),
                'deaths': int(player['deaths']),
                'kda': round(player['kda'], 2),
                'player_damage': int(player['player_damage']),
                'building_damage': int(player['building_damage']),
                'healing': int(player['healing']),
                'damage_taken': int(player['damage_taken']),
                'heavy_injuries': int(player['heavy_injuries']),
                'resurrections': int(player['resurrections']),
                'resources': int(player['resources'])
            },
            'percentiles': calculate_percentiles(player, battle_df)
        }
        
        baseline_analysis.append(analysis)
        
        print(f"  {rank:2d}. {player['name']} ({player['profession']}) - {position_analysis['primary_position']} - 评分: {baseline_score:.1f}")
    
    return baseline_analysis

def analyze_detailed_position_v2(player, battle_df):
    """详细的位置分析v2"""
    # 计算各项指标的百分位
    damage_pct = (battle_df['player_damage'] < player['player_damage']).sum() / len(battle_df)
    healing_pct = (battle_df['healing'] < player['healing']).sum() / len(battle_df)
    tank_pct = (battle_df['damage_taken'] < player['damage_taken']).sum() / len(battle_df)
    building_pct = (battle_df['building_damage'] < player['building_damage']).sum() / len(battle_df)
    
    # 位置评分
    position_scores = {
        '输出位': 0,
        '坦克位': 0,
        '辅助位': 0,
        '拆塔位': 0
    }
    
    # 输出位评分
    if damage_pct > 0.8:
        position_scores['输出位'] += 4
    elif damage_pct > 0.6:
        position_scores['输出位'] += 3
    elif damage_pct > 0.4:
        position_scores['输出位'] += 2
    
    if player['kills'] > battle_df['kills'].mean():
        position_scores['输出位'] += 2
    
    # 坦克位评分
    if tank_pct > 0.8:
        position_scores['坦克位'] += 4
    elif tank_pct > 0.6:
        position_scores['坦克位'] += 3
    
    if player['heavy_injuries'] > battle_df['heavy_injuries'].mean():
        position_scores['坦克位'] += 1
    
    # 辅助位评分
    if healing_pct > 0.8:
        position_scores['辅助位'] += 4
    elif healing_pct > 0.6:
        position_scores['辅助位'] += 3
    elif healing_pct > 0.4:
        position_scores['辅助位'] += 2
    
    if player['assists'] > battle_df['assists'].mean():
        position_scores['辅助位'] += 2
    
    if player['resurrections'] > battle_df['resurrections'].mean():
        position_scores['辅助位'] += 1
    
    # 拆塔位评分
    if building_pct > 0.8:
        position_scores['拆塔位'] += 4
    elif building_pct > 0.6:
        position_scores['拆塔位'] += 3
    elif building_pct > 0.4:
        position_scores['拆塔位'] += 2
    
    # 确定主要位置
    primary_position = max(position_scores, key=position_scores.get)
    max_score = position_scores[primary_position]
    
    # 计算置信度
    total_score = sum(position_scores.values())
    confidence = max_score / max(total_score, 1)
    
    # 找出备选位置
    alternative_positions = []
    for pos, score in sorted(position_scores.items(), key=lambda x: x[1], reverse=True)[1:]:
        if score >= max_score * 0.6:  # 至少60%的分数
            alternative_positions.append(pos)
    
    return {
        'primary_position': primary_position,
        'confidence': round(confidence, 2),
        'alternative_positions': alternative_positions,
        'position_scores': position_scores
    }

def calculate_baseline_score(player, battle_df):
    """计算基准评分"""
    # 相对评分（相对于本场其他玩家）
    relative_scores = {}
    
    metrics = ['kills', 'assists', 'player_damage', 'building_damage', 'healing', 'damage_taken']
    
    for metric in metrics:
        if battle_df[metric].max() > 0:
            relative_scores[metric] = player[metric] / battle_df[metric].max()
        else:
            relative_scores[metric] = 0
    
    # 加权计算
    weights = {
        'kills': 20,
        'assists': 15,
        'player_damage': 25,
        'building_damage': 10,
        'healing': 15,
        'damage_taken': 10
    }
    
    weighted_score = sum(relative_scores[metric] * weights[metric] for metric in metrics)
    
    # KDA奖励
    kda_bonus = min(player['kda'] * 2, 20)
    
    # 死亡惩罚
    death_penalty = player['deaths'] * -3
    
    total_score = weighted_score + kda_bonus + death_penalty
    
    return round(max(0, total_score), 1)

def analyze_role_characteristics(player, battle_df):
    """分析角色特征"""
    characteristics = []
    
    # 计算相对表现
    metrics = {
        'damage_dealer': player['player_damage'] / battle_df['player_damage'].mean(),
        'tank': player['damage_taken'] / battle_df['damage_taken'].mean(),
        'healer': player['healing'] / max(battle_df['healing'].mean(), 1),
        'demolisher': player['building_damage'] / max(battle_df['building_damage'].mean(), 1),
        'killer': player['kills'] / max(battle_df['kills'].mean(), 1),
        'supporter': player['assists'] / max(battle_df['assists'].mean(), 1)
    }
    
    # 判断特征
    if metrics['damage_dealer'] > 1.5:
        characteristics.append('输出核心')
    if metrics['tank'] > 1.5:
        characteristics.append('坦克专家')
    if metrics['healer'] > 2.0:
        characteristics.append('治疗专家')
    if metrics['demolisher'] > 1.5:
        characteristics.append('拆塔专家')
    if metrics['killer'] > 1.5:
        characteristics.append('击杀专家')
    if metrics['supporter'] > 1.3:
        characteristics.append('团队配合')
    
    if player['kda'] > 10:
        characteristics.append('高生存')
    if player['deaths'] <= 2:
        characteristics.append('稳定发挥')
    
    return characteristics

def calculate_percentiles(player, battle_df):
    """计算百分位数"""
    percentiles = {}
    
    metrics = ['kills', 'assists', 'player_damage', 'building_damage', 'healing', 'damage_taken', 'kda']
    
    for metric in metrics:
        if metric in battle_df.columns:
            percentile = (battle_df[metric] < player[metric]).sum() / len(battle_df) * 100
            percentiles[metric] = round(percentile, 1)
    
    return percentiles

def create_position_baseline(baseline_analysis):
    """创建位置基准"""
    position_baseline = {}
    
    # 按位置分组
    for position in ['输出位', '坦克位', '辅助位', '拆塔位']:
        position_players = [p for p in baseline_analysis if p['baseline_position'] == position]
        
        if position_players:
            # 计算该位置的基准指标
            avg_stats = {}
            for stat in ['kills', 'assists', 'player_damage', 'building_damage', 'healing', 'damage_taken']:
                values = [p['stats'][stat] for p in position_players]
                avg_stats[stat] = {
                    'mean': sum(values) / len(values),
                    'max': max(values),
                    'min': min(values)
                }
            
            position_baseline[position] = {
                'player_count': len(position_players),
                'avg_score': sum(p['baseline_score'] for p in position_players) / len(position_players),
                'top_players': sorted(position_players, key=lambda x: x['baseline_score'], reverse=True)[:3],
                'baseline_stats': avg_stats
            }
    
    return position_baseline

def create_profession_baseline(baseline_analysis):
    """创建职业基准"""
    profession_baseline = {}
    
    # 按职业分组
    professions = set(p['profession'] for p in baseline_analysis)
    
    for profession in professions:
        profession_players = [p for p in baseline_analysis if p['profession'] == profession]
        
        if profession_players:
            # 统计该职业的位置分布
            position_dist = {}
            for player in profession_players:
                pos = player['baseline_position']
                position_dist[pos] = position_dist.get(pos, 0) + 1
            
            profession_baseline[profession] = {
                'player_count': len(profession_players),
                'avg_score': sum(p['baseline_score'] for p in profession_players) / len(profession_players),
                'position_distribution': position_dist,
                'top_player': max(profession_players, key=lambda x: x['baseline_score']),
                'common_characteristics': get_common_characteristics(profession_players)
            }
    
    return profession_baseline

def get_common_characteristics(players):
    """获取共同特征"""
    all_characteristics = []
    for player in players:
        all_characteristics.extend(player['role_characteristics'])
    
    # 统计频率
    char_count = {}
    for char in all_characteristics:
        char_count[char] = char_count.get(char, 0) + 1
    
    # 返回出现频率超过50%的特征
    common_chars = []
    threshold = len(players) * 0.5
    for char, count in char_count.items():
        if count >= threshold:
            common_chars.append(char)
    
    return common_chars

def generate_baseline_recommendations(baseline_analysis, position_baseline):
    """生成基准建议"""
    recommendations = {
        'optimal_lineup': {},
        'position_adjustments': [],
        'training_focus': []
    }
    
    # 推荐最佳阵容
    for position, data in position_baseline.items():
        if data['top_players']:
            recommendations['optimal_lineup'][position] = data['top_players'][0]['name']
    
    # 位置调整建议
    for player in baseline_analysis:
        if len(player['alternative_positions']) > 0 and player['position_confidence'] < 0.7:
            recommendations['position_adjustments'].append({
                'player': player['name'],
                'current_position': player['baseline_position'],
                'suggested_positions': player['alternative_positions'],
                'reason': f"位置置信度较低({player['position_confidence']})"
            })
    
    return recommendations

def generate_baseline_csv(baseline_analysis):
    """生成基准CSV"""
    csv_data = []
    
    for player in baseline_analysis:
        csv_data.append({
            'rank': player['rank'],
            'name': player['name'],
            'profession': player['profession'],
            'baseline_position': player['baseline_position'],
            'position_confidence': player['position_confidence'],
            'baseline_score': player['baseline_score'],
            'kills': player['stats']['kills'],
            'assists': player['stats']['assists'],
            'deaths': player['stats']['deaths'],
            'kda': player['stats']['kda'],
            'player_damage': player['stats']['player_damage'],
            'building_damage': player['stats']['building_damage'],
            'healing': player['stats']['healing'],
            'damage_taken': player['stats']['damage_taken'],
            'role_characteristics': ', '.join(player['role_characteristics'])
        })
    
    pd.DataFrame(csv_data).to_csv('third_battle_baseline.csv', index=False, encoding='utf-8-sig')

def display_baseline_results(baseline_data):
    """显示基准结果"""
    print(f"\n{'='*80}")
    print("第三场战斗基准分析结果")
    print(f"{'='*80}")
    
    # 显示最佳阵容
    print(f"\n🏆 推荐最佳阵容:")
    for position, player in baseline_data['recommendations']['optimal_lineup'].items():
        print(f"  {position}: {player}")
    
    # 显示位置统计
    print(f"\n📊 位置分布统计:")
    for position, data in baseline_data['position_baseline'].items():
        print(f"  {position}: {data['player_count']}人 (平均评分: {data['avg_score']:.1f})")
    
    # 显示职业统计
    print(f"\n👥 职业表现统计:")
    for profession, data in baseline_data['profession_baseline'].items():
        print(f"  {profession}: {data['player_count']}人 (平均评分: {data['avg_score']:.1f}) - 最佳: {data['top_player']['name']}")

if __name__ == "__main__":
    analyze_third_battle_baseline()
