"""
最终综合评分系统
结合所有分析结果，生成最终的玩家评分和排名
"""

import sys
import os
import pandas as pd
import json
import numpy as np

# 添加路径
sys.path.append('src')

from data_processor import DataProcessor

def create_final_scoring_system():
    """创建最终综合评分系统"""
    try:
        print("=" * 80)
        print("最终综合评分系统")
        print("=" * 80)
        
        # 加载数据
        processor = DataProcessor("data", "纸落弈酒.xlsx")
        raw_data = processor.load_all_data()
        df = processor.create_unified_dataframe()
        
        print(f"📊 数据概况: {len(raw_data)}场战斗, {len(df)}条记录")
        
        # 加载已有的分析结果
        baseline_data = load_baseline_data()
        roster_data = load_roster_data()
        
        # 创建综合评分
        final_scores = calculate_comprehensive_final_scores(df, baseline_data, roster_data)
        
        # 生成最终排名
        final_ranking = create_final_ranking(final_scores)
        
        # 生成位置推荐
        position_recommendations = create_position_recommendations(final_scores, baseline_data)
        
        # 生成战队配置建议
        team_configurations = generate_team_configurations(final_scores, position_recommendations)
        
        # 保存最终结果
        final_results = {
            'final_scores': final_scores,
            'final_ranking': final_ranking,
            'position_recommendations': position_recommendations,
            'team_configurations': team_configurations,
            'analysis_summary': generate_analysis_summary(final_scores)
        }
        
        # 转换numpy类型为Python原生类型
        final_results_serializable = convert_numpy_types(final_results)

        with open('final_scoring_results.json', 'w', encoding='utf-8') as f:
            json.dump(final_results_serializable, f, ensure_ascii=False, indent=2)
        
        # 生成最终CSV报告
        generate_final_csv_reports(final_results)
        
        # 显示结果
        display_final_results(final_results)
        
        print(f"\n✅ 最终综合评分系统完成！")
        print(f"   最终结果: final_scoring_results.json")
        print(f"   CSV报告: final_ranking.csv, team_recommendations.csv")
        
        return final_results
        
    except Exception as e:
        print(f"❌ 最终评分失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def load_baseline_data():
    """加载基准数据"""
    try:
        with open('third_battle_baseline.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("⚠️ 未找到基准数据，将使用默认分析")
        return None

def load_roster_data():
    """加载名单数据"""
    try:
        if os.path.exists('player_roster.csv'):
            return pd.read_csv('player_roster.csv', encoding='utf-8-sig')
        else:
            return None
    except Exception:
        return None

def calculate_comprehensive_final_scores(df, baseline_data, roster_data):
    """计算综合最终评分"""
    print(f"\n📊 计算综合最终评分...")
    
    final_scores = []
    
    # 按玩家分组
    for unique_id in df['unique_id'].unique():
        player_data = df[df['unique_id'] == unique_id]
        player_name = player_data['name'].iloc[0]
        player_profession = player_data['profession'].iloc[0]
        
        # 计算各项评分
        scores = calculate_multi_dimensional_scores(player_data, baseline_data, roster_data)
        
        # 综合评分
        final_score = calculate_weighted_final_score(scores)
        
        # 确定最佳位置
        best_position = determine_best_position(scores, baseline_data, player_name)
        
        # 计算稳定性
        stability = calculate_stability(player_data)
        
        # 计算潜力
        potential = calculate_potential(scores, player_data)
        
        final_scores.append({
            'unique_id': unique_id,
            'name': player_name,
            'profession': player_profession,
            'battles_count': len(player_data),
            'final_score': final_score,
            'best_position': best_position,
            'stability': stability,
            'potential': potential,
            'detailed_scores': scores,
            'recommendations': generate_player_recommendations(scores, stability, potential)
        })
        
        print(f"  {player_name} ({player_profession}) - 最终评分: {final_score:.1f} - 最佳位置: {best_position}")
    
    return final_scores

def calculate_multi_dimensional_scores(player_data, baseline_data, roster_data):
    """计算多维度评分"""
    # 基础统计评分
    basic_stats = calculate_basic_stats_score(player_data)
    
    # 战斗表现评分
    combat_performance = calculate_combat_performance_score(player_data)
    
    # 团队贡献评分
    team_contribution = calculate_team_contribution_score(player_data)
    
    # 位置适合度评分
    position_fitness = calculate_position_fitness_score(player_data)
    
    # 特殊技能评分
    special_skills = calculate_special_skills_score(player_data)
    
    return {
        'basic_stats': basic_stats,
        'combat_performance': combat_performance,
        'team_contribution': team_contribution,
        'position_fitness': position_fitness,
        'special_skills': special_skills
    }

def calculate_basic_stats_score(player_data):
    """计算基础统计评分"""
    avg_stats = {
        'kills': player_data['kills'].mean(),
        'assists': player_data['assists'].mean(),
        'deaths': player_data['deaths'].mean(),
        'kda': player_data['kda'].mean(),
        'player_damage': player_data['player_damage'].mean(),
        'healing': player_data['healing'].mean()
    }
    
    # 标准化评分
    kills_score = min(avg_stats['kills'] * 10, 100)
    assists_score = min(avg_stats['assists'] * 2, 100)
    kda_score = min(avg_stats['kda'] * 10, 100)
    damage_score = min(avg_stats['player_damage'] / 2000000, 100)
    healing_score = min(avg_stats['healing'] / 3000000, 100)
    death_penalty = max(0, 100 - avg_stats['deaths'] * 10)
    
    basic_score = (kills_score + assists_score + kda_score + damage_score + healing_score + death_penalty) / 6
    
    return {
        'score': round(basic_score, 1),
        'components': {
            'kills': round(kills_score, 1),
            'assists': round(assists_score, 1),
            'kda': round(kda_score, 1),
            'damage': round(damage_score, 1),
            'healing': round(healing_score, 1),
            'survival': round(death_penalty, 1)
        }
    }

def calculate_combat_performance_score(player_data):
    """计算战斗表现评分"""
    # 伤害效率
    damage_efficiency = player_data['player_damage'].sum() / max(player_data['deaths'].sum(), 1)
    
    # 击杀参与率
    kill_participation = (player_data['kills'].sum() + player_data['assists'].sum()) / len(player_data)
    
    # 生存能力
    survival_rate = (len(player_data) - player_data['deaths'].sum()) / len(player_data)
    
    # 综合战斗表现
    efficiency_score = min(damage_efficiency / 50000000, 100)
    participation_score = min(kill_participation * 5, 100)
    survival_score = max(0, survival_rate * 100)
    
    combat_score = (efficiency_score + participation_score + survival_score) / 3
    
    return {
        'score': round(combat_score, 1),
        'components': {
            'efficiency': round(efficiency_score, 1),
            'participation': round(participation_score, 1),
            'survival': round(survival_score, 1)
        }
    }

def calculate_team_contribution_score(player_data):
    """计算团队贡献评分"""
    # 助攻贡献
    assist_contribution = player_data['assists'].mean() * 3
    
    # 治疗贡献
    healing_contribution = player_data['healing'].mean() / 5000000
    
    # 拆塔贡献
    building_contribution = player_data['building_damage'].mean() / 10000000
    
    # 控制贡献
    control_contribution = player_data['heavy_injuries'].mean() * 5
    
    # 救援贡献
    rescue_contribution = player_data['resurrections'].mean() * 10
    
    team_score = min(assist_contribution + healing_contribution + building_contribution + 
                    control_contribution + rescue_contribution, 100)
    
    return {
        'score': round(team_score, 1),
        'components': {
            'assists': round(min(assist_contribution, 100), 1),
            'healing': round(min(healing_contribution, 100), 1),
            'building': round(min(building_contribution, 100), 1),
            'control': round(min(control_contribution, 100), 1),
            'rescue': round(min(rescue_contribution, 100), 1)
        }
    }

def calculate_position_fitness_score(player_data):
    """计算位置适合度评分"""
    # 分析各位置的适合度
    positions = {
        '输出位': calculate_damage_dealer_fitness(player_data),
        '坦克位': calculate_tank_fitness(player_data),
        '辅助位': calculate_support_fitness(player_data),
        '拆塔位': calculate_demolisher_fitness(player_data)
    }
    
    best_position = max(positions, key=positions.get)
    best_score = positions[best_position]
    
    return {
        'score': round(best_score, 1),
        'best_position': best_position,
        'position_scores': {pos: round(score, 1) for pos, score in positions.items()}
    }

def calculate_damage_dealer_fitness(player_data):
    """计算输出位适合度"""
    damage_score = min(player_data['player_damage'].mean() / 2000000, 100)
    kills_score = min(player_data['kills'].mean() * 15, 100)
    kda_score = min(player_data['kda'].mean() * 8, 100)
    
    return (damage_score * 0.5 + kills_score * 0.3 + kda_score * 0.2)

def calculate_tank_fitness(player_data):
    """计算坦克位适合度"""
    tank_score = min(player_data['damage_taken'].mean() / 3000000, 100)
    control_score = min(player_data['heavy_injuries'].mean() * 20, 100)
    survival_score = max(0, 100 - player_data['deaths'].mean() * 15)
    
    return (tank_score * 0.5 + control_score * 0.3 + survival_score * 0.2)

def calculate_support_fitness(player_data):
    """计算辅助位适合度"""
    healing_score = min(player_data['healing'].mean() / 2000000, 100)
    assist_score = min(player_data['assists'].mean() * 3, 100)
    rescue_score = min(player_data['resurrections'].mean() * 25, 100)
    
    return (healing_score * 0.5 + assist_score * 0.3 + rescue_score * 0.2)

def calculate_demolisher_fitness(player_data):
    """计算拆塔位适合度"""
    building_score = min(player_data['building_damage'].mean() / 5000000, 100)
    resource_score = min(player_data['resources'].mean() * 10, 100)
    efficiency_score = min((player_data['building_damage'].mean() / max(player_data['deaths'].mean(), 1)) / 20000000, 100)
    
    return (building_score * 0.6 + resource_score * 0.2 + efficiency_score * 0.2)

def calculate_special_skills_score(player_data):
    """计算特殊技能评分"""
    # MVP表现次数（高KDA场次）
    mvp_performances = (player_data['kda'] > 10).sum()
    
    # 关键时刻表现（高伤害低死亡）
    clutch_performances = ((player_data['player_damage'] > player_data['player_damage'].mean()) & 
                          (player_data['deaths'] <= 2)).sum()
    
    # 多面手能力（各项指标均衡）
    versatility = calculate_versatility(player_data)
    
    special_score = (mvp_performances * 20 + clutch_performances * 15 + versatility) / 3
    
    return {
        'score': round(min(special_score, 100), 1),
        'components': {
            'mvp_performances': mvp_performances,
            'clutch_performances': clutch_performances,
            'versatility': round(versatility, 1)
        }
    }

def calculate_versatility(player_data):
    """计算多面手能力"""
    # 计算各项指标的变异系数
    metrics = ['kills', 'assists', 'player_damage', 'healing', 'building_damage']
    cv_scores = []
    
    for metric in metrics:
        if player_data[metric].std() > 0:
            cv = player_data[metric].std() / player_data[metric].mean()
            cv_scores.append(1 / (1 + cv))  # 变异系数越小，多面手能力越强
    
    return sum(cv_scores) / len(cv_scores) * 100 if cv_scores else 0

def calculate_weighted_final_score(scores):
    """计算加权最终评分"""
    weights = {
        'basic_stats': 0.3,
        'combat_performance': 0.25,
        'team_contribution': 0.2,
        'position_fitness': 0.15,
        'special_skills': 0.1
    }
    
    final_score = sum(scores[category]['score'] * weights[category] for category in weights)
    
    return round(final_score, 1)

def determine_best_position(scores, baseline_data, player_name):
    """确定最佳位置"""
    if 'position_fitness' in scores:
        return scores['position_fitness']['best_position']
    else:
        return '输出位'  # 默认位置

def calculate_stability(player_data):
    """计算稳定性"""
    if len(player_data) < 2:
        return 50  # 数据不足，给中等稳定性
    
    # 计算关键指标的变异系数
    key_metrics = ['kda', 'player_damage', 'kills']
    stability_scores = []
    
    for metric in key_metrics:
        if player_data[metric].std() > 0:
            cv = player_data[metric].std() / player_data[metric].mean()
            stability_score = max(0, 100 - cv * 50)  # 变异系数越小，稳定性越高
            stability_scores.append(stability_score)
    
    return round(sum(stability_scores) / len(stability_scores) if stability_scores else 50, 1)

def calculate_potential(scores, player_data):
    """计算潜力"""
    # 基于特殊技能和改进空间
    special_skills_score = scores['special_skills']['score']
    
    # 改进空间（基于当前表现与理论最大值的差距）
    improvement_space = 100 - scores['basic_stats']['score']
    
    # 学习能力（如果有多场比赛，看趋势）
    learning_ability = 50  # 默认值
    if len(player_data) > 1:
        # 简单的趋势分析
        recent_performance = player_data.tail(1)['kda'].iloc[0]
        early_performance = player_data.head(1)['kda'].iloc[0]
        if recent_performance > early_performance:
            learning_ability = 70
        elif recent_performance < early_performance:
            learning_ability = 30
    
    potential = (special_skills_score * 0.4 + improvement_space * 0.3 + learning_ability * 0.3)
    
    return round(potential, 1)

def generate_player_recommendations(scores, stability, potential):
    """生成玩家建议"""
    recommendations = []
    
    # 基于评分给出建议
    if scores['basic_stats']['score'] < 60:
        recommendations.append("建议加强基础技能训练")
    
    if scores['combat_performance']['score'] < 60:
        recommendations.append("建议提高战斗意识和操作")
    
    if scores['team_contribution']['score'] < 60:
        recommendations.append("建议增强团队配合意识")
    
    if stability < 60:
        recommendations.append("建议提高发挥稳定性")
    
    if potential > 70:
        recommendations.append("具有很高的成长潜力，值得重点培养")
    
    return recommendations

def create_final_ranking(final_scores):
    """创建最终排名"""
    ranking = sorted(final_scores, key=lambda x: x['final_score'], reverse=True)
    
    for i, player in enumerate(ranking, 1):
        player['rank'] = i
    
    return ranking

def create_position_recommendations(final_scores, baseline_data):
    """创建位置推荐"""
    position_recommendations = {}
    
    # 按位置分组
    for position in ['输出位', '坦克位', '辅助位', '拆塔位']:
        position_players = [p for p in final_scores if p['best_position'] == position]
        position_players.sort(key=lambda x: x['final_score'], reverse=True)
        
        position_recommendations[position] = {
            'recommended_players': position_players[:5],  # 前5名推荐
            'backup_players': position_players[5:10] if len(position_players) > 5 else [],
            'total_candidates': len(position_players)
        }
    
    return position_recommendations

def generate_team_configurations(final_scores, position_recommendations):
    """生成战队配置建议"""
    configurations = []
    
    # 主力阵容
    main_lineup = {}
    for position, data in position_recommendations.items():
        if data['recommended_players']:
            main_lineup[position] = data['recommended_players'][0]['name']
    
    configurations.append({
        'name': '主力阵容',
        'lineup': main_lineup,
        'avg_score': sum(p['final_score'] for p in [
            next((player for player in final_scores if player['name'] == name), None)
            for name in main_lineup.values()
        ] if p) / len(main_lineup)
    })
    
    # 替补阵容
    backup_lineup = {}
    for position, data in position_recommendations.items():
        if len(data['recommended_players']) > 1:
            backup_lineup[position] = data['recommended_players'][1]['name']
        elif data['backup_players']:
            backup_lineup[position] = data['backup_players'][0]['name']
    
    if backup_lineup:
        configurations.append({
            'name': '替补阵容',
            'lineup': backup_lineup,
            'avg_score': sum(p['final_score'] for p in [
                next((player for player in final_scores if player['name'] == name), None)
                for name in backup_lineup.values()
            ] if p) / len(backup_lineup)
        })
    
    return configurations

def generate_analysis_summary(final_scores):
    """生成分析总结"""
    total_players = len(final_scores)
    avg_score = sum(p['final_score'] for p in final_scores) / total_players
    
    # 评分分布
    score_ranges = {
        '优秀 (80+)': len([p for p in final_scores if p['final_score'] >= 80]),
        '良好 (60-79)': len([p for p in final_scores if 60 <= p['final_score'] < 80]),
        '一般 (40-59)': len([p for p in final_scores if 40 <= p['final_score'] < 60]),
        '待提高 (<40)': len([p for p in final_scores if p['final_score'] < 40])
    }
    
    return {
        'total_players': total_players,
        'average_score': round(avg_score, 1),
        'score_distribution': score_ranges,
        'top_performer': max(final_scores, key=lambda x: x['final_score']),
        'most_stable': max(final_scores, key=lambda x: x['stability']),
        'highest_potential': max(final_scores, key=lambda x: x['potential'])
    }

def generate_final_csv_reports(final_results):
    """生成最终CSV报告"""
    # 最终排名CSV
    ranking_data = []
    for player in final_results['final_ranking']:
        ranking_data.append({
            'rank': player['rank'],
            'name': player['name'],
            'profession': player['profession'],
            'final_score': player['final_score'],
            'best_position': player['best_position'],
            'stability': player['stability'],
            'potential': player['potential'],
            'battles_count': player['battles_count'],
            'recommendations': '; '.join(player['recommendations'])
        })
    
    pd.DataFrame(ranking_data).to_csv('final_ranking.csv', index=False, encoding='utf-8-sig')
    
    # 战队推荐CSV
    team_data = []
    for config in final_results['team_configurations']:
        for position, player in config['lineup'].items():
            team_data.append({
                'configuration': config['name'],
                'position': position,
                'player': player,
                'avg_team_score': config['avg_score']
            })
    
    pd.DataFrame(team_data).to_csv('team_recommendations.csv', index=False, encoding='utf-8-sig')

def convert_numpy_types(obj):
    """转换numpy类型为Python原生类型"""
    if isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    else:
        return obj

def display_final_results(final_results):
    """显示最终结果"""
    print(f"\n{'='*80}")
    print("最终综合评分结果")
    print(f"{'='*80}")
    
    # 显示前10名
    print(f"\n🏆 最终排名前10名:")
    for player in final_results['final_ranking'][:10]:
        print(f"  {player['rank']:2d}. {player['name']} ({player['profession']}) - 评分: {player['final_score']:.1f} - 位置: {player['best_position']}")
    
    # 显示推荐阵容
    print(f"\n⚡ 推荐战队配置:")
    for config in final_results['team_configurations']:
        print(f"  {config['name']} (平均评分: {config['avg_score']:.1f}):")
        for position, player in config['lineup'].items():
            print(f"    {position}: {player}")
    
    # 显示分析总结
    summary = final_results['analysis_summary']
    print(f"\n📊 分析总结:")
    print(f"  总玩家数: {summary['total_players']}")
    print(f"  平均评分: {summary['average_score']}")
    print(f"  最佳表现: {summary['top_performer']['name']} ({summary['top_performer']['final_score']:.1f})")
    print(f"  最稳定: {summary['most_stable']['name']} ({summary['most_stable']['stability']:.1f})")
    print(f"  最具潜力: {summary['highest_potential']['name']} ({summary['highest_potential']['potential']:.1f})")

if __name__ == "__main__":
    create_final_scoring_system()
