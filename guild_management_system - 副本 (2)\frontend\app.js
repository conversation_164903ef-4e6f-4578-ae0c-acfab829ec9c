// 主应用逻辑
class GuildApp {
    constructor() {
        this.currentAuthType = 'simple_password';
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.checkLoginStatus();
    }
    
    bindEvents() {
        // 认证方式切换
        document.querySelectorAll('.auth-type-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchAuthType(e.target.dataset.type);
            });
        });
        
        // 登录表单提交
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });
    }
    
    switchAuthType(type) {
        this.currentAuthType = type;
        
        // 更新按钮状态
        document.querySelectorAll('.auth-type-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.type === type);
        });
        
        // 显示/隐藏相应的输入框
        const nameGroup = document.getElementById('nameGroup');
        const passwordGroup = document.getElementById('passwordGroup');
        const inviteCodeGroup = document.getElementById('inviteCodeGroup');
        
        if (type === 'simple_password') {
            nameGroup.style.display = 'block';
            passwordGroup.style.display = 'block';
            inviteCodeGroup.style.display = 'none';
        } else if (type === 'invite_code') {
            nameGroup.style.display = 'none';
            passwordGroup.style.display = 'none';
            inviteCodeGroup.style.display = 'block';
        }
    }
    
    async handleLogin() {
        const loginBtn = document.getElementById('loginBtn');
        loginBtn.disabled = true;
        loginBtn.textContent = '登录中...';
        
        try {
            let loginData = {
                auth_type: this.currentAuthType
            };
            
            if (this.currentAuthType === 'simple_password') {
                loginData.name = document.getElementById('name').value.trim();
                loginData.password = document.getElementById('password').value;
                
                if (!loginData.name || !loginData.password) {
                    this.showError('请填写完整信息');
                    return;
                }
            } else if (this.currentAuthType === 'invite_code') {
                loginData.invite_code = document.getElementById('inviteCode').value.trim();
                
                if (!loginData.invite_code) {
                    this.showError('请输入邀请码');
                    return;
                }
            }
            
            const result = await API.post(CONFIG.ENDPOINTS.LOGIN, loginData);
            
            if (result.success) {
                Auth.saveLogin(result.token, result.user);
                this.showSuccess(result.message);
                setTimeout(() => {
                    this.showDashboard();
                }, 1000);
            } else {
                this.showError(result.error);
            }
            
        } catch (error) {
            this.showError('登录失败: ' + error.message);
        } finally {
            loginBtn.disabled = false;
            loginBtn.textContent = '登录';
        }
    }
    
    checkLoginStatus() {
        if (Auth.isLoggedIn()) {
            this.showDashboard();
        }
    }
    
    async showDashboard() {
        const user = Auth.getCurrentUser();
        if (!user) {
            this.showLogin();
            return;
        }
        
        // 显示用户信息
        document.getElementById('userName').textContent = user.name;
        document.getElementById('userRole').textContent = this.getRoleDisplayName(user.role);
        
        // 加载统计数据
        await this.loadStats();
        
        // 切换到主界面
        document.getElementById('loginPage').style.display = 'none';
        document.getElementById('dashboardPage').style.display = 'block';
    }
    
    showLogin() {
        document.getElementById('loginPage').style.display = 'block';
        document.getElementById('dashboardPage').style.display = 'none';
        this.clearMessages();
    }
    
    async loadStats() {
        try {
            const result = await API.get(CONFIG.ENDPOINTS.OVERVIEW_STATS);
            
            if (result.success) {
                this.renderStats(result.data);
            } else {
                console.error('加载统计数据失败:', result.error);
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }
    
    renderStats(stats) {
        const statsGrid = document.getElementById('statsGrid');
        
        const statsItems = [
            { label: '帮会成员', value: stats.total_members, color: '#667eea' },
            { label: '帮外人员', value: stats.external_members, color: '#f093fb' },
            { label: '进攻团', value: stats.attack_members, color: '#ff6b6b' },
            { label: '防守团', value: stats.defense_members, color: '#4ecdc4' },
            { label: '其他团', value: stats.other_members, color: '#45b7d1' },
            { label: '主力成员', value: stats.active_members, color: '#96ceb4' },
            { label: '替补成员', value: stats.substitute_members, color: '#feca57' }
        ];
        
        statsGrid.innerHTML = statsItems.map(item => `
            <div class="stat-card">
                <div class="stat-number" style="color: ${item.color}">${item.value}</div>
                <div class="stat-label">${item.label}</div>
            </div>
        `).join('');
    }
    
    getRoleDisplayName(role) {
        const roleNames = {
            'admin': '管理员',
            'officer': '干部',
            'member': '成员'
        };
        return roleNames[role] || role;
    }
    
    showError(message) {
        const errorEl = document.getElementById('errorMessage');
        errorEl.textContent = message;
        errorEl.style.display = 'block';
        
        setTimeout(() => {
            errorEl.style.display = 'none';
        }, 5000);
    }
    
    showSuccess(message) {
        const successEl = document.getElementById('successMessage');
        successEl.textContent = message;
        successEl.style.display = 'block';
        
        setTimeout(() => {
            successEl.style.display = 'none';
        }, 3000);
    }
    
    clearMessages() {
        document.getElementById('errorMessage').style.display = 'none';
        document.getElementById('successMessage').style.display = 'none';
    }
}

// 全局函数
function logout() {
    Auth.logout();
    app.showLogin();
}

// 初始化应用
const app = new GuildApp();
