#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析纸落弈酒.xlsx文件，提取所有帮会成员信息（包括替补）
"""

import pandas as pd
import json
import numpy as np

def analyze_excel_structure(excel_file):
    """分析Excel文件结构"""
    print("=" * 60)
    print("分析Excel文件结构")
    print("=" * 60)
    
    # 读取所有工作表
    excel_file_obj = pd.ExcelFile(excel_file)
    sheet_names = excel_file_obj.sheet_names
    
    print(f"工作表列表: {sheet_names}")
    print()
    
    for sheet_name in sheet_names:
        print(f"--- 工作表: {sheet_name} ---")
        try:
            df = pd.read_excel(excel_file, sheet_name=sheet_name)
            print(f"行数: {len(df)}, 列数: {len(df.columns)}")
            
            # 显示前几行的非空数据
            print("前10行非空数据:")
            for i in range(min(10, len(df))):
                row_data = []
                for col in df.columns:
                    if pd.notna(df.iloc[i][col]) and str(df.iloc[i][col]).strip():
                        row_data.append(f"{col}: {df.iloc[i][col]}")
                if row_data:
                    print(f"  行{i}: {', '.join(row_data[:5])}")  # 只显示前5个非空值
            print()
        except Exception as e:
            print(f"读取失败: {e}")
            print()

def extract_team_management_data(excel_file):
    """提取团队管理表数据"""
    print("=" * 60)
    print("提取团队管理表数据")
    print("=" * 60)
    
    try:
        # 读取团队管理表
        df = pd.read_excel(excel_file, sheet_name='团队管理表')
        
        # 查找包含成员信息的区域
        members = []
        
        # 遍历所有行和列，寻找可能的成员名字
        for row_idx in range(len(df)):
            for col_idx in range(len(df.columns)):
                cell_value = df.iloc[row_idx, col_idx]
                
                if pd.notna(cell_value) and isinstance(cell_value, str):
                    cell_value = str(cell_value).strip()
                    
                    # 跳过明显不是玩家名字的内容
                    if (len(cell_value) > 1 and 
                        not cell_value.isdigit() and 
                        '团' not in cell_value and
                        '队' not in cell_value and
                        '表' not in cell_value and
                        '管理' not in cell_value and
                        '数据' not in cell_value and
                        '修改' not in cell_value and
                        '联系' not in cell_value and
                        '问题' not in cell_value and
                        '传回' not in cell_value and
                        '输出' != cell_value and
                        '资源' != cell_value and
                        '辅助' != cell_value and
                        '坦克' != cell_value and
                        '治疗' != cell_value and
                        len(cell_value) <= 15):  # 玩家名字通常不会太长
                        
                        # 尝试确定这是否是玩家名字
                        if any(char in cell_value for char in '丶灬'):  # 常见的游戏名字符号
                            members.append({
                                'name': cell_value,
                                'row': row_idx,
                                'col': col_idx,
                                'position': f"行{row_idx+1}列{col_idx+1}"
                            })
                        elif len(cell_value) >= 2 and len(cell_value) <= 10:
                            # 可能的玩家名字
                            members.append({
                                'name': cell_value,
                                'row': row_idx,
                                'col': col_idx,
                                'position': f"行{row_idx+1}列{col_idx+1}"
                            })
        
        # 去重
        unique_members = []
        seen_names = set()
        for member in members:
            if member['name'] not in seen_names:
                unique_members.append(member)
                seen_names.add(member['name'])
        
        print(f"在团队管理表中找到 {len(unique_members)} 个可能的成员名字:")
        for member in unique_members:
            print(f"  {member['name']} (位置: {member['position']})")
        
        return unique_members
        
    except Exception as e:
        print(f"提取团队管理表数据失败: {e}")
        return []

def extract_all_sheets_data(excel_file):
    """提取所有工作表中的可能成员数据"""
    print("=" * 60)
    print("提取所有工作表中的成员数据")
    print("=" * 60)
    
    all_members = []
    excel_file_obj = pd.ExcelFile(excel_file)
    
    for sheet_name in excel_file_obj.sheet_names:
        if sheet_name in ['WpsReserved_CellImgList']:  # 跳过系统工作表
            continue
            
        print(f"\n--- 分析工作表: {sheet_name} ---")
        try:
            df = pd.read_excel(excel_file, sheet_name=sheet_name)
            sheet_members = []
            
            # 遍历所有单元格
            for row_idx in range(len(df)):
                for col_idx in range(len(df.columns)):
                    cell_value = df.iloc[row_idx, col_idx]
                    
                    if pd.notna(cell_value) and isinstance(cell_value, str):
                        cell_value = str(cell_value).strip()
                        
                        # 判断是否可能是玩家名字
                        if (2 <= len(cell_value) <= 15 and 
                            not cell_value.isdigit() and
                            not any(keyword in cell_value for keyword in 
                                   ['团', '队', '表', '管理', '数据', '修改', '联系', '问题', '传回', 
                                    '外战', '请假', '鸽子', '下拉', '选项', '总表', '不修改']) and
                            cell_value not in ['输出', '资源', '辅助', '坦克', '治疗', '拆塔', '控制']):
                            
                            sheet_members.append({
                                'name': cell_value,
                                'sheet': sheet_name,
                                'row': row_idx + 1,
                                'col': col_idx + 1
                            })
            
            # 去重
            unique_sheet_members = []
            seen_names = set()
            for member in sheet_members:
                if member['name'] not in seen_names:
                    unique_sheet_members.append(member)
                    seen_names.add(member['name'])
            
            print(f"找到 {len(unique_sheet_members)} 个可能的成员:")
            for member in unique_sheet_members[:10]:  # 只显示前10个
                print(f"  {member['name']}")
            if len(unique_sheet_members) > 10:
                print(f"  ... 还有 {len(unique_sheet_members) - 10} 个")
            
            all_members.extend(unique_sheet_members)
            
        except Exception as e:
            print(f"分析工作表 {sheet_name} 失败: {e}")
    
    return all_members

def main():
    """主函数"""
    excel_file = "纸落弈酒.xlsx"
    
    # 分析Excel文件结构
    analyze_excel_structure(excel_file)
    
    # 提取团队管理表数据
    team_members = extract_team_management_data(excel_file)
    
    # 提取所有工作表数据
    all_members = extract_all_sheets_data(excel_file)
    
    # 合并和去重
    all_unique_members = []
    seen_names = set()
    
    for member in all_members:
        if member['name'] not in seen_names:
            all_unique_members.append(member)
            seen_names.add(member['name'])
    
    print("\n" + "=" * 60)
    print("汇总结果")
    print("=" * 60)
    print(f"Excel文件中总共找到 {len(all_unique_members)} 个不重复的可能成员名字")
    
    # 保存结果
    with open('excel_members.json', 'w', encoding='utf-8') as f:
        json.dump(all_unique_members, f, ensure_ascii=False, indent=2)
    
    print(f"详细信息已保存到: excel_members.json")

if __name__ == "__main__":
    main()
