"""
数据分析模块
专门针对纸落云烟公会的数据分析
包含时间序列分析、对手强度调整、位置适合性分析等功能
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')


class NishuihanPlayerAnalyzer:
    """纸落云烟玩家数据分析器"""

    def __init__(self, data: pd.DataFrame):
        """
        初始化分析器

        Args:
            data: 处理后的玩家数据DataFrame
        """
        self.data = data
        self.player_stats = None
        self.profession_stats = None
        self.time_series_analysis = None
        self.position_analysis = None

        # 位置评估权重配置
        self.position_weights = {
            'damage_dealer': {  # 输出位
                'player_damage': 0.4,
                'kills': 0.3,
                'building_damage': 0.2,
                'kda': 0.1
            },
            'tank': {  # 坦克位
                'damage_taken': 0.4,
                'heavy_injuries': 0.3,
                'assists': 0.2,
                'resurrections': 0.1
            },
            'support': {  # 辅助位
                'healing': 0.4,
                'assists': 0.3,
                'resurrections': 0.2,
                'resources': 0.1
            },
            'demolisher': {  # 拆塔位
                'building_damage': 0.5,
                'resources': 0.2,
                'player_damage': 0.2,
                'assists': 0.1
            }
        }
        
    def calculate_player_scores(self) -> pd.DataFrame:
        """
        计算玩家综合评分，考虑对手强度调整

        Returns:
            包含评分的DataFrame
        """
        # 按玩家unique_id聚合数据（处理转职情况）
        player_agg = self.data.groupby('unique_id').agg({
            'name': 'first',
            'profession': 'first',
            'kills': ['sum', 'mean'],
            'assists': ['sum', 'mean'],
            'player_damage': ['sum', 'mean'],
            'building_damage': ['sum', 'mean'],
            'healing': ['sum', 'mean'],
            'damage_taken': ['sum', 'mean'],
            'deaths': ['sum', 'mean'],
            'heavy_injuries': ['sum', 'mean'],
            'resurrections': ['sum', 'mean'],
            'resources': ['sum', 'mean'],
            'enemy_strength': 'mean',  # 平均对手强度
            'battle_date': 'count'  # 参战次数
        }).round(2)

        # 扁平化列名
        player_agg.columns = ['_'.join(col).strip() for col in player_agg.columns]
        player_agg = player_agg.rename(columns={'battle_date_count': 'battles_count'})

        # 计算基础指标
        player_agg['kda'] = (player_agg['kills_sum'] + player_agg['assists_sum']) / np.maximum(player_agg['deaths_sum'], 1)
        player_agg['damage_per_death'] = player_agg['player_damage_sum'] / np.maximum(player_agg['deaths_sum'], 1)
        player_agg['total_damage'] = player_agg['player_damage_sum'] + player_agg['building_damage_sum']
        player_agg['survival_rate'] = 1 - (player_agg['deaths_sum'] / np.maximum(player_agg['battles_count'], 1))

        # 对手强度调整系数
        player_agg['strength_adjustment'] = player_agg['enemy_strength_mean'] / 50.0  # 50为基准强度

        # 计算调整后的指标
        player_agg['adjusted_kills'] = player_agg['kills_mean'] / player_agg['strength_adjustment']
        player_agg['adjusted_damage'] = player_agg['player_damage_mean'] / player_agg['strength_adjustment']
        player_agg['adjusted_kda'] = player_agg['kda'] / player_agg['strength_adjustment']

        # 计算综合战力评分
        player_agg['combat_score'] = self._calculate_combat_score(player_agg)

        # 计算强弱等级
        player_agg['strength_level'] = self._classify_strength(player_agg['combat_score'])

        self.player_stats = player_agg
        return player_agg
    
    def _calculate_combat_score(self, df: pd.DataFrame) -> pd.Series:
        """
        计算综合战力评分，使用调整后的指标

        Args:
            df: 玩家统计数据

        Returns:
            战力评分Series
        """
        # 标准化各项指标
        scaler = StandardScaler()

        # 选择关键指标（使用调整后的指标）
        features = [
            'adjusted_kills', 'assists_mean', 'adjusted_damage',
            'building_damage_mean', 'healing_mean', 'adjusted_kda',
            'heavy_injuries_mean', 'resurrections_mean', 'survival_rate'
        ]

        # 处理缺失值
        feature_data = df[features].fillna(0)

        # 标准化
        normalized_features = scaler.fit_transform(feature_data)

        # 权重设置（根据逆水寒特点调整）
        weights = {
            'adjusted_kills': 0.2,      # 击杀能力
            'assists_mean': 0.15,       # 团队配合
            'adjusted_damage': 0.25,    # 输出能力
            'building_damage_mean': 0.1, # 拆塔能力
            'healing_mean': 0.1,        # 治疗能力
            'adjusted_kda': 0.1,        # 综合表现
            'heavy_injuries_mean': 0.05, # 重伤控制
            'resurrections_mean': 0.03,  # 救援能力
            'survival_rate': 0.02       # 生存能力
        }

        # 计算加权评分
        weighted_scores = np.zeros(len(df))
        for i, feature in enumerate(features):
            weighted_scores += normalized_features[:, i] * weights[feature]

        # 转换为0-100分制
        min_score = weighted_scores.min()
        max_score = weighted_scores.max()
        if max_score > min_score:
            combat_scores = 100 * (weighted_scores - min_score) / (max_score - min_score)
        else:
            combat_scores = np.full(len(weighted_scores), 50)

        return pd.Series(combat_scores, index=df.index)
    
    def _classify_strength(self, scores: pd.Series) -> pd.Series:
        """
        根据评分分类强弱等级
        
        Args:
            scores: 战力评分
            
        Returns:
            强弱等级Series
        """
        def get_level(score):
            if score >= 90:
                return "超强"
            elif score >= 80:
                return "很强"
            elif score >= 70:
                return "强"
            elif score >= 60:
                return "中等偏强"
            elif score >= 50:
                return "中等"
            elif score >= 40:
                return "中等偏弱"
            elif score >= 30:
                return "弱"
            else:
                return "很弱"
        
        return scores.apply(get_level)

    def analyze_time_series(self) -> Dict:
        """
        分析玩家时间序列表现，检测进步/倒退趋势

        Returns:
            时间序列分析结果字典
        """
        time_series_results = {}

        # 按玩家和日期分组
        for unique_id in self.data['unique_id'].unique():
            player_data = self.data[self.data['unique_id'] == unique_id].copy()

            if len(player_data) < 2:  # 至少需要2场比赛才能分析趋势
                continue

            # 按日期排序
            player_data = player_data.sort_values('battle_date')

            # 计算关键指标的趋势
            trends = self._calculate_trends(player_data)

            # 计算对手强度调整后的表现
            adjusted_performance = self._calculate_adjusted_performance(player_data)

            # 综合评估进步/倒退
            progress_assessment = self._assess_progress(trends, adjusted_performance)

            time_series_results[unique_id] = {
                'name': player_data['name'].iloc[0],
                'profession': player_data['profession'].iloc[0],
                'battle_count': len(player_data),
                'date_range': {
                    'start': player_data['battle_date'].min(),
                    'end': player_data['battle_date'].max()
                },
                'trends': trends,
                'adjusted_performance': adjusted_performance,
                'progress_assessment': progress_assessment
            }

        self.time_series_analysis = time_series_results
        return time_series_results

    def _calculate_trends(self, player_data: pd.DataFrame) -> Dict:
        """
        计算玩家各项指标的趋势

        Args:
            player_data: 单个玩家的时间序列数据

        Returns:
            趋势分析结果
        """
        trends = {}

        # 创建时间序列索引
        dates = pd.to_datetime(player_data['battle_date'])
        time_index = np.arange(len(dates))

        # 分析关键指标的趋势
        key_metrics = ['kills', 'assists', 'player_damage', 'building_damage',
                      'healing', 'deaths', 'heavy_injuries', 'resurrections', 'kda']

        for metric in key_metrics:
            if metric in player_data.columns:
                values = player_data[metric].values

                # 线性回归分析趋势
                if len(values) > 1:
                    lr = LinearRegression()
                    lr.fit(time_index.reshape(-1, 1), values)

                    slope = lr.coef_[0]
                    r_squared = lr.score(time_index.reshape(-1, 1), values)

                    # 判断趋势方向
                    if abs(slope) < 0.01:  # 基本无变化
                        trend_direction = "稳定"
                    elif slope > 0:
                        trend_direction = "上升"
                    else:
                        trend_direction = "下降"

                    trends[metric] = {
                        'slope': slope,
                        'r_squared': r_squared,
                        'direction': trend_direction,
                        'confidence': 'high' if r_squared > 0.7 else 'medium' if r_squared > 0.3 else 'low'
                    }

        return trends

    def _calculate_adjusted_performance(self, player_data: pd.DataFrame) -> Dict:
        """
        计算对手强度调整后的表现

        Args:
            player_data: 单个玩家的时间序列数据

        Returns:
            调整后的表现数据
        """
        # 计算每场比赛的调整系数
        player_data['strength_adj'] = player_data['enemy_strength'] / 50.0

        # 计算调整后的指标
        adjusted_metrics = {}
        base_metrics = ['kills', 'assists', 'player_damage', 'building_damage', 'healing']

        for metric in base_metrics:
            if metric in player_data.columns:
                adjusted_values = player_data[metric] / player_data['strength_adj']
                adjusted_metrics[f'adjusted_{metric}'] = {
                    'values': adjusted_values.tolist(),
                    'mean': adjusted_values.mean(),
                    'std': adjusted_values.std(),
                    'trend': 'improving' if adjusted_values.iloc[-1] > adjusted_values.iloc[0] else 'declining'
                }

        return adjusted_metrics

    def _assess_progress(self, trends: Dict, adjusted_performance: Dict) -> Dict:
        """
        综合评估玩家进步情况

        Args:
            trends: 趋势分析结果
            adjusted_performance: 调整后表现数据

        Returns:
            进步评估结果
        """
        # 关键指标权重
        metric_weights = {
            'kills': 0.25,
            'player_damage': 0.3,
            'assists': 0.2,
            'building_damage': 0.15,
            'healing': 0.1
        }

        progress_score = 0
        total_weight = 0

        # 基于趋势计算进步分数
        for metric, weight in metric_weights.items():
            if metric in trends:
                trend_data = trends[metric]

                # 根据趋势方向和置信度计算分数
                if trend_data['direction'] == '上升':
                    score = 1.0
                elif trend_data['direction'] == '下降':
                    score = -1.0
                else:
                    score = 0.0

                # 根据置信度调整分数
                confidence_multiplier = {
                    'high': 1.0,
                    'medium': 0.7,
                    'low': 0.3
                }.get(trend_data['confidence'], 0.3)

                progress_score += score * weight * confidence_multiplier
                total_weight += weight

        # 标准化进步分数
        if total_weight > 0:
            progress_score = progress_score / total_weight

        # 分类进步等级
        if progress_score > 0.3:
            progress_level = "明显进步"
        elif progress_score > 0.1:
            progress_level = "轻微进步"
        elif progress_score > -0.1:
            progress_level = "基本稳定"
        elif progress_score > -0.3:
            progress_level = "轻微倒退"
        else:
            progress_level = "明显倒退"

        return {
            'progress_score': progress_score,
            'progress_level': progress_level,
            'summary': self._generate_progress_summary(trends, progress_level)
        }

    def _generate_progress_summary(self, trends: Dict, progress_level: str) -> str:
        """
        生成进步总结

        Args:
            trends: 趋势数据
            progress_level: 进步等级

        Returns:
            进步总结文本
        """
        improving_metrics = []
        declining_metrics = []

        for metric, trend_data in trends.items():
            if trend_data['direction'] == '上升' and trend_data['confidence'] in ['high', 'medium']:
                improving_metrics.append(metric)
            elif trend_data['direction'] == '下降' and trend_data['confidence'] in ['high', 'medium']:
                declining_metrics.append(metric)

        summary = f"总体评估: {progress_level}。"

        if improving_metrics:
            summary += f" 进步明显的指标: {', '.join(improving_metrics)}。"

        if declining_metrics:
            summary += f" 需要改进的指标: {', '.join(declining_metrics)}。"

        return summary

    def analyze_position_suitability(self) -> Dict:
        """
        分析每个玩家的位置适合性

        Returns:
            位置适合性分析结果
        """
        if self.player_stats is None:
            self.calculate_player_scores()

        position_analysis = {}

        for unique_id, player_data in self.player_stats.iterrows():
            # 计算每个位置的适合度评分
            position_scores = {}

            for position, weights in self.position_weights.items():
                score = 0
                total_weight = 0

                for metric, weight in weights.items():
                    # 映射指标名称到实际列名
                    actual_metric = self._map_metric_name(metric)

                    if actual_metric in player_data:
                        # 标准化指标值（相对于所有玩家）
                        metric_values = self.player_stats[actual_metric]
                        if metric_values.max() > metric_values.min():
                            normalized_value = (player_data[actual_metric] - metric_values.min()) / (metric_values.max() - metric_values.min())
                        else:
                            normalized_value = 0.5  # 如果所有值相同，给中等分数

                        score += normalized_value * weight
                        total_weight += weight

                if total_weight > 0:
                    position_scores[position] = (score / total_weight) * 100
                else:
                    position_scores[position] = 0

            # 找出最适合的位置
            best_position = max(position_scores, key=position_scores.get)
            best_score = position_scores[best_position]

            # 计算位置特长
            specialties = self._analyze_player_specialties(player_data)

            # 生成建议
            recommendations = self._generate_position_recommendations(position_scores, specialties)

            position_analysis[unique_id] = {
                'name': player_data['name_first'],
                'profession': player_data['profession_first'],
                'position_scores': position_scores,
                'best_position': best_position,
                'best_score': best_score,
                'specialties': specialties,
                'recommendations': recommendations
            }

        self.position_analysis = position_analysis
        return position_analysis

    def _map_metric_name(self, metric: str) -> str:
        """
        映射位置权重中的指标名称到实际的DataFrame列名

        Args:
            metric: 位置权重中的指标名称

        Returns:
            实际的DataFrame列名
        """
        metric_mapping = {
            'player_damage': 'player_damage_mean',
            'building_damage': 'building_damage_mean',
            'healing': 'healing_mean',
            'damage_taken': 'damage_taken_mean',
            'kills': 'kills_mean',
            'assists': 'assists_mean',
            'heavy_injuries': 'heavy_injuries_mean',
            'resurrections': 'resurrections_mean',
            'resources': 'resources_mean',
            'kda': 'kda'
        }

        return metric_mapping.get(metric, metric)

    def _analyze_player_specialties(self, player_data: pd.Series) -> Dict:
        """
        分析玩家的特长领域

        Args:
            player_data: 玩家数据

        Returns:
            特长分析结果
        """
        specialties = {}

        # 定义特长指标和阈值（相对于平均值的倍数）
        specialty_metrics = {
            'kills_mean': {'name': '击杀专家', 'threshold': 1.5},
            'assists_mean': {'name': '团队配合', 'threshold': 1.3},
            'player_damage_mean': {'name': '输出核心', 'threshold': 1.4},
            'building_damage_mean': {'name': '拆塔专家', 'threshold': 1.6},
            'healing_mean': {'name': '治疗专家', 'threshold': 1.5},
            'damage_taken_mean': {'name': '前排坦克', 'threshold': 1.4},
            'heavy_injuries_mean': {'name': '控制专家', 'threshold': 1.3},
            'resurrections_mean': {'name': '救援专家', 'threshold': 1.5},
            'survival_rate': {'name': '生存专家', 'threshold': 1.2}
        }

        for metric, config in specialty_metrics.items():
            if metric in player_data and metric in self.player_stats.columns:
                avg_value = self.player_stats[metric].mean()
                player_value = player_data[metric]

                if avg_value > 0 and player_value / avg_value >= config['threshold']:
                    specialties[config['name']] = {
                        'value': player_value,
                        'ratio': player_value / avg_value,
                        'rank': (self.player_stats[metric] < player_value).sum() + 1
                    }

        return specialties

    def _generate_position_recommendations(self, position_scores: Dict, specialties: Dict) -> List[str]:
        """
        生成位置建议

        Args:
            position_scores: 位置评分
            specialties: 特长分析

        Returns:
            建议列表
        """
        recommendations = []

        # 根据位置评分给出建议
        sorted_positions = sorted(position_scores.items(), key=lambda x: x[1], reverse=True)

        best_position, best_score = sorted_positions[0]
        second_position, second_score = sorted_positions[1] if len(sorted_positions) > 1 else (None, 0)

        # 主要位置建议
        position_names = {
            'damage_dealer': '输出位',
            'tank': '坦克位',
            'support': '辅助位',
            'demolisher': '拆塔位'
        }

        recommendations.append(f"最适合位置: {position_names.get(best_position, best_position)} (评分: {best_score:.1f})")

        if second_score > 60:  # 如果第二位置评分也不错
            recommendations.append(f"次选位置: {position_names.get(second_position, second_position)} (评分: {second_score:.1f})")

        # 基于特长的建议
        if specialties:
            top_specialties = sorted(specialties.items(), key=lambda x: x[1]['ratio'], reverse=True)[:3]
            specialty_text = "、".join([spec[0] for spec in top_specialties])
            recommendations.append(f"突出特长: {specialty_text}")

        # 具体建议
        if best_position == 'damage_dealer':
            recommendations.append("建议重点发展输出能力，提高击杀效率")
        elif best_position == 'tank':
            recommendations.append("建议重点发展承伤和控制能力，保护队友")
        elif best_position == 'support':
            recommendations.append("建议重点发展治疗和辅助能力，支援团队")
        elif best_position == 'demolisher':
            recommendations.append("建议重点发展拆塔能力，快速推进")

        return recommendations

    def get_position_rankings(self, position: str, top_n: int = 10) -> List[Dict]:
        """
        获取特定位置的玩家排行榜

        Args:
            position: 位置名称
            top_n: 返回前n名

        Returns:
            排行榜列表
        """
        if self.position_analysis is None:
            self.analyze_position_suitability()

        # 按指定位置的评分排序
        rankings = []
        for unique_id, analysis in self.position_analysis.items():
            if position in analysis['position_scores']:
                rankings.append({
                    'unique_id': unique_id,
                    'name': analysis['name'],
                    'profession': analysis['profession'],
                    'score': analysis['position_scores'][position],
                    'is_best_position': analysis['best_position'] == position
                })

        # 按评分排序
        rankings.sort(key=lambda x: x['score'], reverse=True)

        return rankings[:top_n]

    def analyze_professions(self) -> pd.DataFrame:
        """
        分析职业特点
        
        Returns:
            职业统计DataFrame
        """
        if self.player_stats is None:
            self.calculate_player_scores()
        
        # 按职业聚合
        profession_stats = self.player_stats.groupby('profession_first').agg({
            'kills_mean': 'mean',
            'assists_mean': 'mean',
            'player_damage_mean': 'mean',
            'building_damage_mean': 'mean',
            'healing_mean': 'mean',
            'damage_taken_mean': 'mean',
            'deaths_mean': 'mean',
            'kda': 'mean',
            'combat_score': 'mean',
            'battles_count': 'sum'
        }).round(2)
        
        # 计算职业特色指标
        profession_stats['damage_ratio'] = profession_stats['player_damage_mean'] / (
            profession_stats['player_damage_mean'] + profession_stats['healing_mean'] + 1
        )
        
        profession_stats['support_ratio'] = profession_stats['healing_mean'] / (
            profession_stats['player_damage_mean'] + profession_stats['healing_mean'] + 1
        )
        
        # 职业角色分类
        profession_stats['role_type'] = profession_stats.apply(self._classify_role, axis=1)
        
        self.profession_stats = profession_stats
        return profession_stats
    
    def _classify_role(self, row) -> str:
        """
        根据数据特征分类职业角色
        
        Args:
            row: 职业统计行
            
        Returns:
            角色类型
        """
        if row['healing_mean'] > 50000000:  # 5千万治疗量
            return "治疗"
        elif row['player_damage_mean'] > 50000000:  # 5千万伤害
            return "输出"
        elif row['damage_taken_mean'] > 50000000:  # 承受5千万伤害
            return "坦克"
        else:
            return "辅助"
    
    def get_top_players(self, n: int = 10, metric: str = 'combat_score') -> pd.DataFrame:
        """
        获取排行榜
        
        Args:
            n: 返回前n名
            metric: 排序指标
            
        Returns:
            排行榜DataFrame
        """
        if self.player_stats is None:
            self.calculate_player_scores()
        
        return self.player_stats.nlargest(n, metric)[
            ['profession_first', 'battles_count', 'kills_sum', 'assists_sum', 
             'player_damage_sum', 'healing_sum', 'kda', 'combat_score', 'strength_level']
        ]
    
    def analyze_team_performance(self) -> pd.DataFrame:
        """
        分析团队表现
        
        Returns:
            团队统计DataFrame
        """
        team_stats = self.data.groupby(['team', 'battle_date']).agg({
            'kills': 'sum',
            'assists': 'sum',
            'player_damage': 'sum',
            'building_damage': 'sum',
            'healing': 'sum',
            'damage_taken': 'sum',
            'deaths': 'sum',
            'name': 'count'  # 团队人数
        }).round(2)
        
        team_stats.columns = ['total_kills', 'total_assists', 'total_player_damage',
                             'total_building_damage', 'total_healing', 'total_damage_taken',
                             'total_deaths', 'team_size']
        
        # 计算团队KDA
        team_stats['team_kda'] = (team_stats['total_kills'] + team_stats['total_assists']) / np.maximum(team_stats['total_deaths'], 1)
        
        # 计算平均每人数据
        for col in ['total_kills', 'total_assists', 'total_player_damage', 'total_building_damage', 'total_healing']:
            team_stats[f'avg_{col[6:]}'] = team_stats[col] / team_stats['team_size']
        
        return team_stats
    
    def find_similar_players(self, player_name: str, n: int = 5) -> List[str]:
        """
        找到相似的玩家
        
        Args:
            player_name: 目标玩家名
            n: 返回相似玩家数量
            
        Returns:
            相似玩家名列表
        """
        if self.player_stats is None:
            self.calculate_player_scores()
        
        if player_name not in self.player_stats.index:
            return []
        
        # 选择数值特征
        features = ['kills_mean', 'assists_mean', 'player_damage_mean', 
                   'healing_mean', 'kda', 'combat_score']
        
        feature_data = self.player_stats[features].fillna(0)
        
        # 标准化
        scaler = StandardScaler()
        normalized_data = scaler.fit_transform(feature_data)
        
        # 计算相似度 (欧几里得距离)
        target_idx = self.player_stats.index.get_loc(player_name)
        target_features = normalized_data[target_idx]
        
        distances = np.sqrt(np.sum((normalized_data - target_features) ** 2, axis=1))
        
        # 获取最相似的玩家 (排除自己)
        similar_indices = np.argsort(distances)[1:n+1]
        similar_players = [self.player_stats.index[i] for i in similar_indices]
        
        return similar_players
