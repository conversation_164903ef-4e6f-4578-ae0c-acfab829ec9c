"""
第三场战斗专门分析
重点分析第三场战斗中每个人的职业和位置表现
"""

import sys
import os
import pandas as pd
import json

# 添加路径
sys.path.append('src')

from data_processor import DataProcessor

def analyze_third_battle():
    """专门分析第三场战斗"""
    try:
        print("=" * 80)
        print("第三场战斗专门分析 - 2025526_233916_纸落云烟_画堂春")
        print("=" * 80)
        
        # 加载数据
        processor = DataProcessor("data", "纸落弈酒.xlsx")
        raw_data = processor.load_all_data()
        df = processor.create_unified_dataframe()
        
        # 找到第三场战斗
        third_battle = None
        for battle in raw_data:
            if "2025526_233916" in battle['filename'] or "画堂春" in battle['filename']:
                third_battle = battle
                break
        
        if not third_battle:
            print("❌ 没有找到第三场战斗数据")
            return None
        
        print(f"📅 战斗时间: {third_battle['date']}")
        print(f"📁 文件名: {third_battle['filename']}")
        
        # 获取第三场战斗的数据
        battle_df = df[df['battle_date'] == third_battle['date']]
        
        if len(battle_df) == 0:
            print("❌ 第三场战斗中没有纸落云烟的参战数据")
            return None
        
        print(f"👥 纸落云烟参战人数: {len(battle_df)}人")
        
        # 详细分析每个玩家
        players_analysis = []
        
        print(f"\n{'='*80}")
        print("详细玩家分析")
        print(f"{'='*80}")
        
        # 按伤害排序
        battle_df_sorted = battle_df.sort_values('player_damage', ascending=False)
        
        for rank, (_, player) in enumerate(battle_df_sorted.iterrows(), 1):
            # 分析位置
            position = analyze_detailed_position(player)
            
            # 计算各项指标
            damage_per_death = player['player_damage'] / max(player['deaths'], 1)
            healing_ratio = player['healing'] / max(player['player_damage'], 1)
            tank_ratio = player['damage_taken'] / max(player['player_damage'], 1)
            
            analysis = {
                'rank': rank,
                'name': player['name'],
                'profession': player['profession'],
                'inferred_position': position,
                'performance_rating': calculate_detailed_performance(player),
                'stats': {
                    'kills': int(player['kills']),
                    'assists': int(player['assists']),
                    'deaths': int(player['deaths']),
                    'kda': round(player['kda'], 2),
                    'player_damage': int(player['player_damage']),
                    'building_damage': int(player['building_damage']),
                    'healing': int(player['healing']),
                    'damage_taken': int(player['damage_taken']),
                    'heavy_injuries': int(player['heavy_injuries']),
                    'resurrections': int(player['resurrections']),
                    'resources': int(player['resources'])
                },
                'ratios': {
                    'damage_per_death': round(damage_per_death, 0),
                    'healing_ratio': round(healing_ratio * 100, 1),
                    'tank_ratio': round(tank_ratio * 100, 1)
                }
            }
            
            players_analysis.append(analysis)
            
            # 显示详细分析
            print(f"\n🏆 第{rank}名: {player['name']} ({player['profession']})")
            print(f"   📍 推测位置: {position}")
            print(f"   ⭐ 表现评分: {analysis['performance_rating']}")
            print(f"   ⚔️  战斗数据: 击败{analysis['stats']['kills']} | 助攻{analysis['stats']['assists']} | 死亡{analysis['stats']['deaths']} | KDA{analysis['stats']['kda']}")
            print(f"   💥 伤害数据: 对玩家{analysis['stats']['player_damage']:,} | 拆塔{analysis['stats']['building_damage']:,}")
            print(f"   🩺 支援数据: 治疗{analysis['stats']['healing']:,} | 承伤{analysis['stats']['damage_taken']:,}")
            print(f"   🎯 特殊数据: 重伤{analysis['stats']['heavy_injuries']} | 羽化/清泉{analysis['stats']['resurrections']} | 资源{analysis['stats']['resources']}")
            print(f"   📊 效率指标: 每死伤害{analysis['ratios']['damage_per_death']:,} | 治疗比{analysis['ratios']['healing_ratio']}% | 承伤比{analysis['ratios']['tank_ratio']}%")
        
        # 位置分布分析
        print(f"\n{'='*60}")
        print("位置分布分析")
        print(f"{'='*60}")
        
        position_groups = {}
        for player in players_analysis:
            pos = player['inferred_position']
            if pos not in position_groups:
                position_groups[pos] = []
            position_groups[pos].append(player)
        
        for position, players in position_groups.items():
            print(f"\n🎯 {position} ({len(players)}人):")
            for player in sorted(players, key=lambda x: x['performance_rating'], reverse=True):
                print(f"   {player['rank']:2d}. {player['name']} ({player['profession']}) - 评分: {player['performance_rating']}")
        
        # 职业表现分析
        print(f"\n{'='*60}")
        print("职业表现分析")
        print(f"{'='*60}")
        
        profession_groups = {}
        for player in players_analysis:
            prof = player['profession']
            if prof not in profession_groups:
                profession_groups[prof] = []
            profession_groups[prof].append(player)
        
        for profession, players in profession_groups.items():
            if len(players) > 0:
                avg_rating = sum([p['performance_rating'] for p in players]) / len(players)
                print(f"\n⚡ {profession} ({len(players)}人) - 平均评分: {avg_rating:.1f}")
                for player in sorted(players, key=lambda x: x['performance_rating'], reverse=True):
                    print(f"   {player['rank']:2d}. {player['name']} ({player['inferred_position']}) - 评分: {player['performance_rating']}")
        
        # 保存第三场战斗分析
        third_battle_data = {
            'battle_info': {
                'filename': third_battle['filename'],
                'date': third_battle['date'],
                'participants': len(battle_df)
            },
            'players_analysis': players_analysis,
            'position_distribution': {pos: len(players) for pos, players in position_groups.items()},
            'profession_distribution': {prof: len(players) for prof, players in profession_groups.items()}
        }
        
        with open("third_battle_analysis.json", 'w', encoding='utf-8') as f:
            json.dump(third_battle_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n{'='*80}")
        print("🎯 第三场战斗分析建议")
        print(f"{'='*80}")
        
        # 生成位置建议
        print("\n📋 位置优化建议:")
        for position, players in position_groups.items():
            best_player = max(players, key=lambda x: x['performance_rating'])
            print(f"  {position}最佳表现: {best_player['name']} ({best_player['profession']}) - 评分: {best_player['performance_rating']}")
        
        print(f"\n✅ 第三场战斗分析完成！结果已保存到 third_battle_analysis.json")
        return third_battle_data
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_detailed_position(player_data):
    """详细的位置分析"""
    damage = player_data['player_damage']
    healing = player_data['healing']
    damage_taken = player_data['damage_taken']
    building_damage = player_data['building_damage']
    heavy_injuries = player_data['heavy_injuries']
    assists = player_data['assists']
    kills = player_data['kills']
    
    # 更精确的位置判断
    scores = {
        '输出位': 0,
        '坦克位': 0,
        '辅助位': 0,
        '拆塔位': 0
    }
    
    # 输出位评分
    if damage > 100000000:  # 1亿伤害
        scores['输出位'] += 3
    elif damage > 50000000:  # 5千万伤害
        scores['输出位'] += 2
    elif damage > 20000000:  # 2千万伤害
        scores['输出位'] += 1
    
    if kills > 10:
        scores['输出位'] += 2
    elif kills > 5:
        scores['输出位'] += 1
    
    # 坦克位评分
    if damage_taken > 100000000:  # 1亿承伤
        scores['坦克位'] += 3
    elif damage_taken > 50000000:  # 5千万承伤
        scores['坦克位'] += 2
    elif damage_taken > 20000000:  # 2千万承伤
        scores['坦克位'] += 1
    
    if heavy_injuries > 5:
        scores['坦克位'] += 1
    
    # 辅助位评分
    if healing > 50000000:  # 5千万治疗
        scores['辅助位'] += 3
    elif healing > 20000000:  # 2千万治疗
        scores['辅助位'] += 2
    elif healing > 5000000:  # 500万治疗
        scores['辅助位'] += 1
    
    if assists > 50:
        scores['辅助位'] += 2
    elif assists > 30:
        scores['辅助位'] += 1
    
    # 拆塔位评分
    if building_damage > 30000000:  # 3千万拆塔
        scores['拆塔位'] += 3
    elif building_damage > 15000000:  # 1.5千万拆塔
        scores['拆塔位'] += 2
    elif building_damage > 5000000:  # 500万拆塔
        scores['拆塔位'] += 1
    
    if building_damage > damage * 0.5:  # 拆塔伤害占一半以上
        scores['拆塔位'] += 2
    
    # 返回得分最高的位置
    best_position = max(scores, key=scores.get)
    
    # 如果得分都很低，根据职业推测
    if scores[best_position] == 0:
        profession = player_data['profession']
        if profession in ['素问', '九灵']:
            return "辅助位"
        elif profession in ['铁衣', '碎梦']:
            return "坦克位"
        elif profession in ['神相']:
            return "拆塔位"
        else:
            return "输出位"
    
    return best_position

def calculate_detailed_performance(player_data):
    """计算详细的表现评分"""
    # 基础分数
    kills_score = player_data['kills'] * 15
    assists_score = player_data['assists'] * 8
    damage_score = player_data['player_damage'] / 2000000  # 200万伤害为1分
    healing_score = player_data['healing'] / 3000000  # 300万治疗为1分
    building_score = player_data['building_damage'] / 8000000  # 800万拆塔为1分
    
    # 死亡惩罚
    death_penalty = player_data['deaths'] * -10
    
    # KDA奖励
    kda_bonus = min(player_data['kda'] * 5, 50)  # 最高50分KDA奖励
    
    # 特殊贡献
    heavy_injuries_score = player_data['heavy_injuries'] * 3
    resurrections_score = player_data['resurrections'] * 5
    
    total_score = (kills_score + assists_score + damage_score + healing_score + 
                  building_score + death_penalty + kda_bonus + 
                  heavy_injuries_score + resurrections_score)
    
    return round(max(0, total_score), 1)

if __name__ == "__main__":
    analyze_third_battle()
