#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修正后的数据结构
"""

import json
import os

def load_members():
    """加载成员数据"""
    try:
        with open('data/guild_members.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("成员数据文件不存在")
        return []

def test_team_structure():
    """测试团队结构"""
    print("=" * 60)
    print("🧪 测试修正后的团队结构")
    print("=" * 60)
    
    members = load_members()
    print(f"总成员数: {len(members)}")
    
    # 统计团队分布
    team_stats = {}
    squad_stats = {}
    status_stats = {}
    
    for member in members:
        # 团队统计
        team = member.get('team', '未分配')
        team_stats[team] = team_stats.get(team, 0) + 1
        
        # 小队统计
        squad = member.get('squad', '未分配小队')
        squad_stats[squad] = squad_stats.get(squad, 0) + 1
        
        # 状态统计
        status = member.get('status', '主力')
        status_stats[status] = status_stats.get(status, 0) + 1
    
    print("\n📊 团队分布:")
    for team in ['1团', '2团', '3团', '4团']:
        count = team_stats.get(team, 0)
        team_type = "进攻团" if team in ['1团', '2团', '3团'] else "防守团"
        print(f"  {team}: {count}人 ({team_type})")
    
    print("\n🎯 小队分布:")
    for squad, count in sorted(squad_stats.items()):
        print(f"  {squad}: {count}人")
    
    print("\n👥 状态分布:")
    for status, count in sorted(status_stats.items()):
        print(f"  {status}: {count}人")

def test_battle_matching():
    """测试战斗数据匹配"""
    print("\n" + "=" * 60)
    print("🧪 测试战斗数据匹配")
    print("=" * 60)
    
    # 加载成员数据
    members = load_members()
    member_names = [m['name'] for m in members]
    print(f"成员数据中的姓名数量: {len(member_names)}")
    
    # 模拟加载战斗数据
    battle_file = 'data/20250531_203611_纸落云烟_初影未来.csv'
    
    if not os.path.exists(battle_file):
        print(f"战斗数据文件不存在: {battle_file}")
        return
    
    battle_names = []
    current_guild = None
    
    # 尝试不同的编码
    encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312']
    lines = []
    
    for encoding in encodings:
        try:
            with open(battle_file, 'r', encoding=encoding) as f:
                lines = f.readlines()
            print(f"成功使用编码 {encoding} 读取文件")
            break
        except UnicodeDecodeError:
            continue
    
    if not lines:
        print("无法读取CSV文件")
        return
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        parts = [part.strip().replace('"', '') for part in line.split(',')]
        
        # 检查帮会名称
        if len(parts) >= 1 and parts[0] in ['纸落云烟', '初影未来']:
            current_guild = parts[0]
            continue
        
        # 检查表头
        if len(parts) >= 2 and parts[0] == '玩家名字':
            continue
        
        # 处理成员数据
        if current_guild == '纸落云烟' and len(parts) >= 12:
            battle_names.append(parts[0])
    
    print(f"战斗数据中的姓名数量: {len(battle_names)}")
    
    # 匹配分析
    exact_matches = []
    fuzzy_matches = []
    unmatched_members = []
    unmatched_battles = []
    
    for member_name in member_names:
        if member_name in battle_names:
            exact_matches.append(member_name)
        else:
            # 尝试模糊匹配
            clean_member_name = member_name.replace('丶', '').replace('灬', '')
            found = False
            for battle_name in battle_names:
                clean_battle_name = battle_name.replace('丶', '').replace('灬', '')
                if clean_battle_name == clean_member_name:
                    fuzzy_matches.append((member_name, battle_name))
                    found = True
                    break
            if not found:
                unmatched_members.append(member_name)
    
    # 找出战斗数据中未匹配的成员
    matched_battle_names = set(exact_matches + [fm[1] for fm in fuzzy_matches])
    unmatched_battles = [name for name in battle_names if name not in matched_battle_names]
    
    print(f"\n📊 匹配结果:")
    print(f"  精确匹配: {len(exact_matches)}个")
    print(f"  模糊匹配: {len(fuzzy_matches)}个")
    print(f"  未匹配成员: {len(unmatched_members)}个")
    print(f"  未匹配战斗数据: {len(unmatched_battles)}个")
    
    if unmatched_members:
        print(f"\n❌ 未匹配的成员（前10个）:")
        for name in unmatched_members[:10]:
            print(f"    {name}")
    
    if unmatched_battles:
        print(f"\n❓ 未匹配的战斗数据（前10个）:")
        for name in unmatched_battles[:10]:
            print(f"    {name}")
    
    if fuzzy_matches:
        print(f"\n🔍 模糊匹配示例（前5个）:")
        for member_name, battle_name in fuzzy_matches[:5]:
            print(f"    {member_name} -> {battle_name}")

def test_scoring_system():
    """测试评分系统"""
    print("\n" + "=" * 60)
    print("🧪 测试评分系统")
    print("=" * 60)
    
    def get_team_type(team_name):
        """获取团队类型"""
        if team_name in ['1团', '2团', '3团']:
            return '进攻团'
        elif team_name in ['4团']:
            return '防守团'
        else:
            return '其他'
    
    # 测试用例
    test_cases = [
        {
            'name': '进攻团素问',
            'profession': '素问',
            'team': '1团',
            'battle_data': {
                'resurrections': 10,
                'healing': 50000000,
                'assists': 100,
                'heavy_injuries': 2,
                'kills': 3,
                'player_damage': 5000000,
                'building_damage': 2000000,
                'damage_taken': 20000000,
                'demolitions': 1,
                'qingquan': 0
            }
        },
        {
            'name': '防守团潮光',
            'profession': '潮光',
            'team': '4团',
            'battle_data': {
                'resurrections': 0,
                'healing': 500000,
                'assists': 80,
                'heavy_injuries': 5,
                'kills': 20,
                'player_damage': 40000000,
                'building_damage': 5000000,
                'damage_taken': 15000000,
                'demolitions': 2,
                'qingquan': 3
            }
        },
        {
            'name': '进攻团潮光',
            'profession': '潮光',
            'team': '2团',
            'battle_data': {
                'resurrections': 0,
                'healing': 500000,
                'assists': 60,
                'heavy_injuries': 4,
                'kills': 12,
                'player_damage': 25000000,
                'building_damage': 15000000,
                'damage_taken': 12000000,
                'demolitions': 6,
                'qingquan': 12
            }
        }
    ]
    
    for case in test_cases:
        team_type = get_team_type(case['team'])
        print(f"\n{case['name']} ({case['team']} - {team_type}):")
        print(f"  职业: {case['profession']}")
        print(f"  击杀: {case['battle_data']['kills']}")
        print(f"  对人伤害: {case['battle_data']['player_damage']/10000:.1f}万")
        print(f"  拆塔: {case['battle_data']['demolitions']}")
        if case['profession'] == '素问':
            print(f"  羽化: {case['battle_data']['resurrections']}")
            print(f"  重伤: {case['battle_data']['heavy_injuries']}")
        if case['profession'] == '潮光':
            print(f"  清泉: {case['battle_data']['qingquan']}")

def main():
    """主函数"""
    print("🔍 测试修正后的数据和功能")
    
    test_team_structure()
    test_battle_matching()
    test_scoring_system()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
