#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
根据帮战名单表.xlsx更新成员位置
"""

import pandas as pd
import json
import os

def read_excel_and_update_positions():
    """读取Excel文件并更新成员位置"""
    
    # Excel文件路径
    excel_file = '../帮战名单表.xlsx'
    members_file = 'data/guild_members.json'
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return
    
    if not os.path.exists(members_file):
        print(f"❌ 成员数据文件不存在: {members_file}")
        return
    
    print("📊 开始读取Excel文件...")
    
    try:
        # 读取Excel文件的所有工作表
        excel_data = pd.read_excel(excel_file, sheet_name=None)
        
        print(f"📋 发现工作表: {list(excel_data.keys())}")
        
        # 读取现有成员数据
        with open(members_file, 'r', encoding='utf-8') as f:
            members = json.load(f)
        
        print(f"👥 当前成员数: {len(members)}")
        
        # 创建成员名称到索引的映射
        member_index = {member['name']: i for i, member in enumerate(members)}
        
        updated_count = 0
        
        # 遍历所有工作表
        for sheet_name, df in excel_data.items():
            print(f"\n🔍 处理工作表: {sheet_name}")
            print(f"   列名: {list(df.columns)}")
            
            # 查找包含成员信息的列
            name_col = None
            team_col = None
            position_col = None
            main_group_col = None

            for col in df.columns:
                col_lower = str(col).lower()
                if '姓名' in col_lower or '名字' in col_lower or 'name' in col_lower or col == 'ID':
                    name_col = col
                elif '团号' in col_lower or '分团' in col_lower or '团' in col_lower or 'team' in col_lower:
                    if '分团' in col_lower:
                        main_group_col = col
                    else:
                        team_col = col
                elif '位置' in col_lower or '职位' in col_lower or 'position' in col_lower:
                    position_col = col
            
            if not name_col:
                print(f"   ⚠️ 未找到姓名列，跳过工作表 {sheet_name}")
                continue
            
            print(f"   📝 姓名列: {name_col}")
            if team_col:
                print(f"   🏢 团队列: {team_col}")
            if main_group_col:
                print(f"   🏢 主团列: {main_group_col}")
            if position_col:
                print(f"   📍 位置列: {position_col}")

            # 处理每一行数据
            for index, row in df.iterrows():
                name = str(row[name_col]).strip()

                # 跳过空值和无效数据
                if pd.isna(row[name_col]) or name == 'nan' or name == '':
                    continue

                # 查找成员
                if name in member_index:
                    member_idx = member_index[name]
                    member = members[member_idx]

                    # 从Excel数据中读取组织结构
                    main_group = '其他团'
                    sub_team = '一团'
                    squad = '1队'

                    # 从分团列读取主要团队
                    if main_group_col and not pd.isna(row[main_group_col]):
                        main_group_info = str(row[main_group_col]).strip()
                        if '进攻团' in main_group_info:
                            main_group = '进攻团'
                        elif '防守团' in main_group_info:
                            main_group = '防守团'

                    # 从团号列读取子团队
                    if team_col and not pd.isna(row[team_col]):
                        team_info = str(row[team_col]).strip()
                        if '一团' in team_info:
                            sub_team = '一团'
                        elif '二团' in team_info:
                            sub_team = '二团'
                        elif '三团' in team_info:
                            sub_team = '三团'
                        elif '防守' in team_info:
                            sub_team = '防守团'
                    
                    # 根据位置信息推断小队
                    if position_col and not pd.isna(row[position_col]):
                        pos_info = str(row[position_col]).strip()
                        # 解析位置信息，如 "1页1" 表示第1页第1个位置
                        if '页' in pos_info:
                            # 提取页码作为小队编号
                            try:
                                page_num = pos_info.split('页')[0]
                                if page_num.isdigit():
                                    squad_num = int(page_num)
                                    if 1 <= squad_num <= 5:
                                        squad = f'{squad_num}队'
                            except:
                                pass
                        elif '1队' in pos_info:
                            squad = '1队'
                        elif '2队' in pos_info:
                            squad = '2队'
                        elif '3队' in pos_info:
                            squad = '3队'
                        elif '4队' in pos_info:
                            squad = '4队'
                        elif '5队' in pos_info:
                            squad = '5队'
                    
                    # 更新成员信息
                    old_info = f"{member.get('main_group', '无')} -> {member.get('sub_team', '无')} -> {member.get('squad', '无')}"
                    
                    member['main_group'] = main_group
                    member['sub_team'] = sub_team
                    member['squad'] = squad
                    
                    # 更新兼容字段
                    if main_group == '进攻团':
                        if sub_team == '一团':
                            member['team'] = '1团'
                        elif sub_team == '二团':
                            member['team'] = '2团'
                        elif sub_team == '三团':
                            member['team'] = '3团'
                    elif main_group == '防守团':
                        member['team'] = '4团'
                    else:
                        member['team'] = '其他团'
                    
                    new_info = f"{main_group} -> {sub_team} -> {squad}"
                    print(f"   ✅ {name}: {old_info} → {new_info}")
                    updated_count += 1
                else:
                    print(f"   ⚠️ 未找到成员: {name}")
        
        # 备份并保存更新后的数据
        backup_file = members_file + '.excel_update_backup'
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(members, f, ensure_ascii=False, indent=2)
        
        with open(members_file, 'w', encoding='utf-8') as f:
            json.dump(members, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 更新完成！")
        print(f"   - 更新成员数: {updated_count}")
        print(f"   - 备份文件: {backup_file}")
        
        # 显示更新后的统计
        print("\n📊 更新后统计:")
        main_groups = {}
        for member in members:
            main_group = member.get('main_group', '其他团')
            sub_team = member.get('sub_team', '一团')
            squad = member.get('squad', '1队')
            
            if main_group not in main_groups:
                main_groups[main_group] = {}
            if sub_team not in main_groups[main_group]:
                main_groups[main_group][sub_team] = {}
            if squad not in main_groups[main_group][sub_team]:
                main_groups[main_group][sub_team][squad] = 0
            
            main_groups[main_group][sub_team][squad] += 1
        
        for main_group, sub_teams in main_groups.items():
            total_in_main = sum(sum(squads.values()) for squads in sub_teams.values())
            print(f"\n{main_group} ({total_in_main}人):")
            for sub_team, squads in sub_teams.items():
                total_in_sub = sum(squads.values())
                print(f"  {sub_team} ({total_in_sub}人):")
                for squad, count in squads.items():
                    print(f"    {squad}: {count}人")
        
    except Exception as e:
        print(f"❌ 处理Excel文件时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    read_excel_and_update_positions()
