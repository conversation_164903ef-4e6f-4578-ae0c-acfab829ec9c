# 检查职业不确定的玩家
import sys
import os
sys.path.append('nishuihan_analyzer/src')

from data_processor import DataProcessor

try:
    print("=" * 60)
    print("检查职业不确定的玩家")
    print("=" * 60)
    
    # 加载数据
    processor = DataProcessor("nishuihan_analyzer/data", "nishuihan_analyzer/纸落弈酒.xlsx")
    raw_data = processor.load_all_data()
    df = processor.create_unified_dataframe()
    
    # 找出职业不确定的玩家
    unknown_players = df[df['profession'].isin(['unknown', 'nil', 'nan', ''])]['name'].unique()
    
    print(f"发现 {len(unknown_players)} 名职业不确定的玩家：")
    print("-" * 40)
    
    for i, player_name in enumerate(unknown_players, 1):
        # 获取该玩家的所有记录
        player_records = df[df['name'] == player_name]
        
        print(f"{i:2d}. {player_name}")
        print(f"    参战次数: {len(player_records)}")
        print(f"    CSV中的职业: {player_records['profession'].unique()}")
        
        # 显示该玩家的数据特征，帮助判断职业
        avg_damage = player_records['player_damage'].mean()
        avg_healing = player_records['healing'].mean()
        avg_building_damage = player_records['building_damage'].mean()
        avg_damage_taken = player_records['damage_taken'].mean()
        
        print(f"    平均对玩家伤害: {avg_damage:,.0f}")
        print(f"    平均治疗量: {avg_healing:,.0f}")
        print(f"    平均拆塔伤害: {avg_building_damage:,.0f}")
        print(f"    平均承受伤害: {avg_damage_taken:,.0f}")
        
        # 简单的职业推测
        if avg_healing > 50000000:  # 5千万治疗
            suggested_role = "治疗职业 (素问/九灵)"
        elif avg_damage > 100000000:  # 1亿伤害
            suggested_role = "输出职业 (龙吟/铁衣/潮光等)"
        elif avg_damage_taken > 80000000:  # 8千万承伤
            suggested_role = "坦克职业 (铁衣/碎梦等)"
        else:
            suggested_role = "辅助/平衡职业"
        
        print(f"    建议职业类型: {suggested_role}")
        print()
    
    print("=" * 60)
    print("请根据以上信息补充职业数据到纸落弈酒.xlsx文件中")
    print("=" * 60)

except Exception as e:
    print(f"检查失败: {e}")
    import traceback
    traceback.print_exc()
