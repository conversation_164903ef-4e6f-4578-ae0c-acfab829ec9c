// 纸落云烟数据分析系统 - 前端JavaScript

let allPlayers = [];
let currentTab = 'players';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadOverview();
    loadPlayers();
    setupEventListeners();
});

// 设置事件监听器
function setupEventListeners() {
    // 搜索框
    document.getElementById('playerSearch').addEventListener('input', filterPlayers);
    
    // 模态框关闭
    document.querySelector('.close').addEventListener('click', closeModal);
    window.addEventListener('click', function(event) {
        const modal = document.getElementById('playerModal');
        if (event.target === modal) {
            closeModal();
        }
    });
}

// 加载数据概览
async function loadOverview() {
    try {
        const response = await axios.get('/api/overview');
        const data = response.data;
        
        // 更新总玩家数
        document.getElementById('totalPlayers').textContent = data.total_players;
        
        // 创建职业分布图表
        createProfessionChart(data.profession_distribution);
        
        // 创建强弱分布图表
        createStrengthChart(data.strength_distribution);
        
    } catch (error) {
        console.error('加载概览数据失败:', error);
    }
}

// 创建职业分布图表
function createProfessionChart(data) {
    const ctx = document.getElementById('professionChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: Object.keys(data),
            datasets: [{
                data: Object.values(data),
                backgroundColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        font: {
                            size: 10
                        }
                    }
                }
            }
        }
    });
}

// 创建强弱分布图表
function createStrengthChart(data) {
    const ctx = document.getElementById('strengthChart').getContext('2d');
    
    const strengthOrder = ['很弱', '弱', '中等偏弱', '中等', '中等偏强', '强', '很强', '超强'];
    const orderedData = strengthOrder.map(level => data[level] || 0);
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: strengthOrder,
            datasets: [{
                data: orderedData,
                backgroundColor: [
                    '#90a4ae', '#78909c', '#ab47bc', '#42a5f5',
                    '#66bb6a', '#ffa726', '#ff8e53', '#ff6b6b'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
}

// 加载玩家数据
async function loadPlayers() {
    try {
        const response = await axios.get('/api/players');
        allPlayers = response.data;
        displayPlayers(allPlayers);
    } catch (error) {
        console.error('加载玩家数据失败:', error);
        document.getElementById('playerGrid').innerHTML = '<div class="loading">加载失败，请刷新页面重试</div>';
    }
}

// 显示玩家列表
function displayPlayers(players) {
    const grid = document.getElementById('playerGrid');
    
    if (players.length === 0) {
        grid.innerHTML = '<div class="loading">没有找到匹配的玩家</div>';
        return;
    }
    
    const html = players.map(player => `
        <div class="player-card" onclick="showPlayerDetail('${player.unique_id}')">
            <div class="player-name">${player.name}</div>
            <div class="player-profession">${player.profession}</div>
            <div class="player-stats">
                <div class="stat-item">
                    <span>参战:</span>
                    <span>${player.battles_count}场</span>
                </div>
                <div class="stat-item">
                    <span>击败:</span>
                    <span>${player.kills_total}</span>
                </div>
                <div class="stat-item">
                    <span>助攻:</span>
                    <span>${player.assists_total}</span>
                </div>
                <div class="stat-item">
                    <span>KDA:</span>
                    <span>${player.kda}</span>
                </div>
                <div class="stat-item">
                    <span>战力:</span>
                    <span>${player.combat_score}</span>
                </div>
                <div class="stat-item">
                    <span>进步:</span>
                    <span>${player.progress_level}</span>
                </div>
            </div>
            <div class="strength-badge strength-${player.strength_level}">${player.strength_level}</div>
        </div>
    `).join('');
    
    grid.innerHTML = html;
}

// 过滤玩家
function filterPlayers() {
    const searchTerm = document.getElementById('playerSearch').value.toLowerCase();
    const filteredPlayers = allPlayers.filter(player => 
        player.name.toLowerCase().includes(searchTerm) ||
        player.profession.toLowerCase().includes(searchTerm)
    );
    displayPlayers(filteredPlayers);
}

// 显示玩家详情
async function showPlayerDetail(uniqueId) {
    const modal = document.getElementById('playerModal');
    const detailDiv = document.getElementById('playerDetail');
    
    modal.style.display = 'block';
    detailDiv.innerHTML = '<div class="loading">正在加载玩家详情...</div>';
    
    try {
        const response = await axios.get(`/api/player/${uniqueId}`);
        const player = response.data;
        
        const html = `
            <h2>${player.name} (${player.profession})</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 20px;">
                <div>
                    <h3>基础数据</h3>
                    <p><strong>参战次数:</strong> ${player.battles_count}场</p>
                    <p><strong>战力评分:</strong> ${player.combat_score}</p>
                    <p><strong>强弱等级:</strong> ${player.strength_level}</p>
                    <p><strong>平均对手强度:</strong> ${player.stats.enemy_strength}</p>
                    
                    <h3 style="margin-top: 20px;">战斗统计</h3>
                    <p><strong>击败:</strong> ${player.stats.kills.total} (平均: ${player.stats.kills.average})</p>
                    <p><strong>助攻:</strong> ${player.stats.assists.total} (平均: ${player.stats.assists.average})</p>
                    <p><strong>死亡:</strong> ${player.stats.deaths.total} (平均: ${player.stats.deaths.average})</p>
                    <p><strong>KDA:</strong> ${player.stats.kda}</p>
                </div>
                <div>
                    <h3>伤害数据</h3>
                    <p><strong>对玩家伤害:</strong> ${player.stats.player_damage.total.toLocaleString()} (平均: ${player.stats.player_damage.average.toLocaleString()})</p>
                    <p><strong>拆塔伤害:</strong> ${player.stats.building_damage.total.toLocaleString()} (平均: ${player.stats.building_damage.average.toLocaleString()})</p>
                    <p><strong>治疗量:</strong> ${player.stats.healing.total.toLocaleString()} (平均: ${player.stats.healing.average.toLocaleString()})</p>
                    <p><strong>承受伤害:</strong> ${player.stats.damage_taken.total.toLocaleString()} (平均: ${player.stats.damage_taken.average.toLocaleString()})</p>
                    
                    <h3 style="margin-top: 20px;">特殊数据</h3>
                    <p><strong>重伤:</strong> ${player.stats.heavy_injuries.total} (平均: ${player.stats.heavy_injuries.average})</p>
                    <p><strong>羽化/清泉:</strong> ${player.stats.resurrections.total} (平均: ${player.stats.resurrections.average})</p>
                </div>
            </div>
        `;
        
        // 添加位置分析
        if (player.position_analysis) {
            const positionHtml = `
                <div style="margin-top: 20px;">
                    <h3>位置适合性分析</h3>
                    <p><strong>最适合位置:</strong> ${getPositionName(player.position_analysis.best_position)} (评分: ${player.position_analysis.best_score})</p>
                    <div style="margin-top: 10px;">
                        <h4>各位置评分:</h4>
                        ${Object.entries(player.position_analysis.position_scores).map(([pos, score]) => 
                            `<p>${getPositionName(pos)}: ${score.toFixed(1)}分</p>`
                        ).join('')}
                    </div>
                    <div style="margin-top: 10px;">
                        <h4>建议:</h4>
                        ${player.position_analysis.recommendations.map(rec => `<p>• ${rec}</p>`).join('')}
                    </div>
                </div>
            `;
            detailDiv.innerHTML = html + positionHtml;
        } else {
            detailDiv.innerHTML = html;
        }
        
    } catch (error) {
        console.error('加载玩家详情失败:', error);
        detailDiv.innerHTML = '<div class="loading">加载失败，请重试</div>';
    }
}

// 获取位置中文名称
function getPositionName(position) {
    const names = {
        'damage_dealer': '输出位',
        'tank': '坦克位',
        'support': '辅助位',
        'demolisher': '拆塔位'
    };
    return names[position] || position;
}

// 关闭模态框
function closeModal() {
    document.getElementById('playerModal').style.display = 'none';
}

// 切换标签页
function showTab(tabName) {
    // 更新标签按钮状态
    document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
    event.target.classList.add('active');
    
    // 隐藏所有标签内容
    document.querySelectorAll('.tab-content').forEach(content => content.style.display = 'none');
    
    // 显示选中的标签内容
    document.getElementById(tabName + 'Tab').style.display = 'block';
    
    currentTab = tabName;
    
    // 根据标签页加载相应数据
    if (tabName === 'rankings') {
        loadRankings();
    } else if (tabName === 'unknown') {
        loadUnknownProfessions();
    }
}

// 加载位置排行榜
async function loadRankings() {
    const container = document.getElementById('positionRankings');
    container.innerHTML = '<div class="loading">正在加载排行榜数据...</div>';
    
    const positions = [
        { key: 'damage_dealer', name: '输出位' },
        { key: 'tank', name: '坦克位' },
        { key: 'support', name: '辅助位' },
        { key: 'demolisher', name: '拆塔位' }
    ];
    
    try {
        const rankingPromises = positions.map(pos => 
            axios.get(`/api/rankings/${pos.key}`).then(response => ({
                position: pos,
                data: response.data
            }))
        );
        
        const results = await Promise.all(rankingPromises);
        
        const html = results.map(result => `
            <div class="ranking-card">
                <div class="ranking-title">${result.position.name}排行榜</div>
                ${result.data.slice(0, 10).map((player, index) => `
                    <div class="ranking-item">
                        <div>
                            <span class="rank-number">${index + 1}.</span>
                            ${player.name} (${player.profession})
                            ${player.is_best_position ? ' ⭐' : ''}
                        </div>
                        <div>${player.score.toFixed(1)}</div>
                    </div>
                `).join('')}
            </div>
        `).join('');
        
        container.innerHTML = html;
        
    } catch (error) {
        console.error('加载排行榜失败:', error);
        container.innerHTML = '<div class="loading">加载失败，请重试</div>';
    }
}

// 加载职业不确定的玩家
async function loadUnknownProfessions() {
    const container = document.getElementById('unknownPlayers');
    container.innerHTML = '<div class="loading">正在加载数据...</div>';
    
    try {
        const response = await axios.get('/api/unknown_professions');
        const players = response.data;
        
        if (players.length === 0) {
            container.innerHTML = '<p style="color: #666;">太好了！所有玩家的职业信息都已完整。</p>';
            return;
        }
        
        const html = players.map(player => {
            // 根据数据推测职业
            let suggestedRole = '';
            if (player.healing_avg > 50000000) {
                suggestedRole = '治疗职业 (素问/九灵)';
            } else if (player.player_damage_avg > 100000000) {
                suggestedRole = '输出职业 (龙吟/铁衣/潮光等)';
            } else if (player.damage_taken_avg > 80000000) {
                suggestedRole = '坦克职业 (铁衣/碎梦等)';
            } else {
                suggestedRole = '辅助/平衡职业';
            }
            
            return `
                <div class="player-card" style="margin-bottom: 15px;">
                    <div class="player-name">${player.name}</div>
                    <div class="player-profession">当前职业: ${player.profession}</div>
                    <div class="player-stats">
                        <div class="stat-item">
                            <span>平均对玩家伤害:</span>
                            <span>${player.player_damage_avg.toLocaleString()}</span>
                        </div>
                        <div class="stat-item">
                            <span>平均治疗量:</span>
                            <span>${player.healing_avg.toLocaleString()}</span>
                        </div>
                        <div class="stat-item">
                            <span>平均拆塔伤害:</span>
                            <span>${player.building_damage_avg.toLocaleString()}</span>
                        </div>
                        <div class="stat-item">
                            <span>平均承受伤害:</span>
                            <span>${player.damage_taken_avg.toLocaleString()}</span>
                        </div>
                    </div>
                    <div style="margin-top: 10px; padding: 8px; background: #f0f8ff; border-radius: 5px;">
                        <strong>建议职业:</strong> ${suggestedRole}
                    </div>
                </div>
            `;
        }).join('');
        
        container.innerHTML = html;
        
    } catch (error) {
        console.error('加载职业不确定玩家失败:', error);
        container.innerHTML = '<div class="loading">加载失败，请重试</div>';
    }
}
