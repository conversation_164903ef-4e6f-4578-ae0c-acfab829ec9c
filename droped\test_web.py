# 测试Web应用
import sys
import os

# 切换到正确目录
os.chdir('nishuihan_analyzer')
sys.path.append('src')

try:
    from flask import Flask
    print("✅ Flask导入成功")
    
    from data_processor import DataProcessor
    print("✅ DataProcessor导入成功")
    
    from analyzer import NishuihanPlayerAnalyzer
    print("✅ NishuihanPlayerAnalyzer导入成功")
    
    # 测试数据加载
    print("\n测试数据加载...")
    processor = DataProcessor("data", "纸落弈酒.xlsx")
    raw_data = processor.load_all_data()
    df = processor.create_unified_dataframe()
    print(f"✅ 数据加载成功: {len(df)} 条记录")
    
    # 测试分析器
    print("\n测试分析器...")
    analyzer = NishuihanPlayerAnalyzer(df)
    player_stats = analyzer.calculate_player_scores()
    print(f"✅ 玩家评分计算成功: {len(player_stats)} 名玩家")
    
    time_series_analysis = analyzer.analyze_time_series()
    print(f"✅ 时间序列分析成功: {len(time_series_analysis)} 名玩家")
    
    position_analysis = analyzer.analyze_position_suitability()
    print(f"✅ 位置分析成功: {len(position_analysis)} 名玩家")
    
    print("\n🎉 所有组件测试通过！Web应用应该可以正常运行。")
    
    # 创建简化的Web应用测试
    app = Flask(__name__)
    
    @app.route('/')
    def test_route():
        return "纸落云烟数据分析系统测试页面 - 运行正常！"
    
    @app.route('/api/test')
    def api_test():
        return {
            'status': 'success',
            'total_players': len(player_stats),
            'message': '数据加载成功'
        }
    
    print("\n启动测试Web服务器...")
    print("访问地址: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)

except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
