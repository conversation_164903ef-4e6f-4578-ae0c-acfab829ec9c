{% extends "base.html" %}

{% block title %}编辑成员 - {{ member.name }} - 纸落云烟帮会管理系统{% endblock %}

{% block content %}
<div style="max-width: 600px; margin: 0 auto;">
    <!-- 当前信息 -->
    <div class="stat-card" style="margin-bottom: 20px;">
        <h3 style="color: #667eea; margin-bottom: 15px;">编辑成员: {{ member.name }}</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-bottom: 20px;">
            <div>
                <strong>当前职业:</strong><br>
                <span class="profession-badge profession-{{ member.profession }}">{{ member.profession }}</span>
            </div>
            <div>
                <strong>当前团队:</strong><br>
                <span class="profession-badge" style="background: #667eea; color: white;">{{ member.team }}</span>
            </div>
            <div>
                <strong>当前小队:</strong><br>
                <span class="profession-badge" style="background: #28a745; color: white;">{{ member.squad or '未分配小队' }}</span>
            </div>
            <div>
                <strong>当前状态:</strong><br>
                <span class="profession-badge" style="background: {% if member.status == '主力' %}#007bff{% elif member.status == '替补' %}#ffc107{% else %}#6c757d{% endif %}; color: white;">{{ member.status or '主力' }}</span>
            </div>
        </div>
    </div>

    <!-- 编辑表单 -->
    <div class="stat-card">
        <form method="POST" action="{{ url_for('update_member') }}" style="display: grid; gap: 20px;">
            <input type="hidden" name="name" value="{{ member.name }}">

            <!-- 职业选择 -->
            <div>
                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #555;">职业</label>
                <select name="profession" required style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px;">
                    {% for prof in professions %}
                    <option value="{{ prof }}" {% if prof == member.profession %}selected{% endif %}>{{ prof }}</option>
                    {% endfor %}
                </select>
            </div>

            <!-- 团队选择 -->
            <div>
                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #555;">团队</label>
                <select name="team" id="teamSelect" required style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px;">
                    {% for team in teams %}
                    <option value="{{ team }}" {% if team == member.team %}selected{% endif %}>{{ team }}</option>
                    {% endfor %}
                </select>
                <input type="text" name="new_team_name" id="newTeamInput" placeholder="输入新团队名称" style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px; margin-top: 8px; display: none;">
            </div>

            <!-- 小队选择 -->
            <div>
                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #555;">小队</label>
                <select name="squad" id="squadSelect" style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px;">
                    {% for squad in squads %}
                    <option value="{{ squad }}" {% if squad == member.squad %}selected{% endif %}>{{ squad }}</option>
                    {% endfor %}
                </select>
                <input type="text" name="new_squad_name" id="newSquadInput" placeholder="输入新小队名称" style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px; margin-top: 8px; display: none;">
            </div>

            <!-- 位置选择 -->
            <div>
                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #555;">位置</label>
                <select name="position" required style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px;">
                    {% for pos in positions %}
                    <option value="{{ pos }}" {% if pos == member.position %}selected{% endif %}>{{ pos }}</option>
                    {% endfor %}
                </select>
            </div>

            <!-- 状态选择 -->
            <div>
                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #555;">状态</label>
                <select name="status" required style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px;">
                    {% for status in statuses %}
                    <option value="{{ status }}" {% if status == (member.status or '主力') %}selected{% endif %}>{{ status }}</option>
                    {% endfor %}
                </select>
            </div>

            <!-- 按钮 -->
            <div style="display: flex; gap: 10px; justify-content: flex-end;">
                <button type="button" onclick="history.back()" style="padding: 12px 24px; background: #ccc; color: #333; border: none; border-radius: 8px; cursor: pointer;">取消</button>
                <button type="submit" style="padding: 12px 24px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer;">保存修改</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 处理新建团队
document.getElementById('teamSelect').addEventListener('change', function() {
    const newTeamInput = document.getElementById('newTeamInput');
    if (this.value === '新建团队') {
        newTeamInput.style.display = 'block';
        newTeamInput.required = true;
    } else {
        newTeamInput.style.display = 'none';
        newTeamInput.required = false;
    }
});

// 处理新建小队
document.getElementById('squadSelect').addEventListener('change', function() {
    const newSquadInput = document.getElementById('newSquadInput');
    if (this.value === '新建小队') {
        newSquadInput.style.display = 'block';
        newSquadInput.required = true;
    } else {
        newSquadInput.style.display = 'none';
        newSquadInput.required = false;
    }
});

// 根据职业自动建议位置
document.querySelector('select[name="profession"]').addEventListener('change', function() {
    const profession = this.value;
    const positionSelect = document.querySelector('select[name="position"]');

    // 职业对应的建议位置
    const professionPositions = {
        '素问': '治疗',
        '铁衣': '坦克',
        '潮光': '输出',
        '九灵': '输出',
        '龙吟': '输出',
        '血河': '输出',
        '碎梦': '输出',
        '玄机': '拆塔',
        '神相': '拆塔'
    };

    const suggestedPosition = professionPositions[profession];
    if (suggestedPosition) {
        // 选择建议的位置
        for (let option of positionSelect.options) {
            if (option.value === suggestedPosition) {
                option.selected = true;
                break;
            }
        }
    }
});
</script>
{% endblock %}
