{% extends "base.html" %}

{% block title %}成员详情 - 纸落云烟{% endblock %}

{% block content %}

<!-- 搜索和筛选栏 -->
<div style="display: flex; gap: 15px; margin-bottom: 20px; flex-wrap: wrap;">
    <input type="text" class="search-box" id="searchInput" placeholder="🔍 搜索成员姓名、职业或团队..." style="flex: 1; min-width: 300px;">

    <select id="mainGroupFilter" style="padding: 15px; border: none; border-radius: 10px; background: rgba(255, 255, 255, 0.9); font-size: 16px; min-width: 120px;">
        <option value="all">全部主团</option>
        <option value="进攻团">进攻团</option>
        <option value="防守团">防守团</option>
        <option value="其他团">其他团</option>
    </select>

    <select id="subTeamFilter" style="padding: 15px; border: none; border-radius: 10px; background: rgba(255, 255, 255, 0.9); font-size: 16px; min-width: 120px;">
        <option value="all">全部子团</option>
        <option value="一团">一团</option>
        <option value="二团">二团</option>
        <option value="三团">三团</option>
        <option value="防守团">防守团</option>
    </select>

    <select id="squadFilter" style="padding: 15px; border: none; border-radius: 10px; background: rgba(255, 255, 255, 0.9); font-size: 16px; min-width: 120px;">
        <option value="all">全部小队</option>
        <option value="1队">1队</option>
        <option value="2队">2队</option>
        <option value="3队">3队</option>
        <option value="4队">4队</option>
        <option value="5队">5队</option>
    </select>

    <select id="professionFilter" style="padding: 15px; border: none; border-radius: 10px; background: rgba(255, 255, 255, 0.9); font-size: 16px; min-width: 120px;">
        <option value="all">全部职业</option>
        <option value="素问">素问</option>
        <option value="铁衣">铁衣</option>
        <option value="潮光">潮光</option>
        <option value="九灵">九灵</option>
        <option value="龙吟">龙吟</option>
        <option value="血河">血河</option>
        <option value="碎梦">碎梦</option>
        <option value="玄机">玄机</option>
        <option value="神相">神相</option>
    </select>
</div>

<!-- 成员列表表格 -->
<div class="stat-card">
    <div style="overflow-x: auto;">
        <table style="width: 100%; border-collapse: collapse;">
            <thead>
                <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">序号</th>
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">成员姓名</th>
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">职业</th>
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">主团</th>
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">子团</th>
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">小队</th>
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">位置</th>
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">状态</th>
                    <th style="padding: 15px; text-align: center; font-weight: bold; color: #495057;">详细信息</th>
                </tr>
            </thead>
            <tbody id="memberTableBody">
                {% for member in members|sort(attribute='name') %}
                <tr class="member-row"
                    data-name="{{ member.name }}"
                    data-profession="{{ member.profession }}"
                    data-main-group="{{ member.get('main_group', '其他团') }}"
                    data-sub-team="{{ member.get('sub_team', '一团') }}"
                    data-squad="{{ member.get('squad', '1队') }}"
                    style="border-bottom: 1px solid #dee2e6; transition: background-color 0.3s ease;">
                    <td style="padding: 15px; color: #6c757d;">{{ loop.index }}</td>
                    <td style="padding: 15px;">
                        <div style="font-weight: bold; color: #2c3e50; font-size: 16px; cursor: pointer;" onclick="viewMemberDetail('{{ member.name }}')">
                            {{ member.name }}
                            <span style="font-size: 12px; color: #007bff; margin-left: 5px;">📊</span>
                        </div>
                    </td>
                    <td style="padding: 15px;">
                        <span class="profession-badge profession-{{ member.profession }}">{{ member.profession }}</span>
                    </td>
                    <td style="padding: 15px;">
                        <span class="profession-badge" style="background: {% if member.get('main_group') == '进攻团' %}#28a745{% elif member.get('main_group') == '防守团' %}#dc3545{% else %}#6c757d{% endif %}; color: white;">{{ member.get('main_group', '其他团') }}</span>
                    </td>
                    <td style="padding: 15px;">
                        <span class="profession-badge" style="background: #667eea; color: white;">{% set sub_team = member.get('sub_team', '一团') %}{% if sub_team == '防守团' %}一团{% elif sub_team == '防守二团' %}二团{% elif sub_team == '防守三团' %}三团{% elif sub_team == '防守四团' %}四团{% else %}{{ sub_team }}{% endif %}</span>
                    </td>
                    <td style="padding: 15px;">
                        <span class="profession-badge" style="background: #17a2b8; color: white;">{{ member.get('squad', '1队') }}</span>
                    </td>
                    <td style="padding: 15px;">
                        <span class="profession-badge" style="background: #f0f0f0; color: #666;">{{ member.position }}</span>
                    </td>
                    <td style="padding: 15px;">
                        <span class="profession-badge" style="background: {% if member.status == '主力' %}#007bff{% elif member.status == '替补' %}#ffc107{% else %}#6c757d{% endif %}; color: white;">{{ member.status or '主力' }}</span>
                    </td>
                    <td style="padding: 15px; text-align: center;">
                        <button class="detail-btn" onclick="viewMemberDetail('{{ member.name }}')" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; font-size: 14px;">
                            查看详情
                        </button>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- 统计信息 -->
    <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #dee2e6;">
        <h3 style="margin-bottom: 20px; color: #2c3e50;">📊 组织架构统计</h3>

        <!-- 总体统计 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; text-align: center; margin-bottom: 30px;">
            <div>
                <div style="font-size: 24px; font-weight: bold; color: #667eea;">{{ members|length }}</div>
                <div style="color: #6c757d;">总成员数</div>
            </div>
            <div>
                <div style="font-size: 24px; font-weight: bold; color: #28a745;">{{ members|selectattr('main_group', 'equalto', '进攻团')|list|length }}</div>
                <div style="color: #6c757d;">进攻团</div>
            </div>
            <div>
                <div style="font-size: 24px; font-weight: bold; color: #dc3545;">{{ members|selectattr('main_group', 'equalto', '防守团')|list|length }}</div>
                <div style="color: #6c757d;">防守团</div>
            </div>
            <div>
                <div style="font-size: 24px; font-weight: bold; color: #6c757d;">{{ members|rejectattr('main_group', 'in', ['进攻团', '防守团'])|list|length }}</div>
                <div style="color: #6c757d;">其他团</div>
            </div>
        </div>

        <!-- 详细统计 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
            <!-- 进攻团统计 -->
            {% set attack_members = members|selectattr('main_group', 'equalto', '进攻团')|list %}
            {% if attack_members %}
            <div style="background: #f8f9fa; padding: 15px; border-radius: 10px;">
                <h4 style="margin: 0 0 10px 0; color: #28a745;">进攻团 ({{ attack_members|length }}人)</h4>
                {% for sub_team in ['一团', '二团', '三团'] %}
                    {% set sub_members = attack_members|selectattr('sub_team', 'equalto', sub_team)|list %}
                    {% if sub_members %}
                    <div style="margin: 5px 0; font-size: 14px;">
                        <strong>{{ sub_team }}</strong>: {{ sub_members|length }}人
                        <div style="margin-left: 10px; font-size: 12px; color: #6c757d;">
                            {% for squad in ['1队', '2队', '3队', '4队', '5队'] %}
                                {% set squad_members = sub_members|selectattr('squad', 'equalto', squad)|list %}
                                {% if squad_members %}{{ squad }}({{ squad_members|length }}){% if not loop.last %}, {% endif %}{% endif %}
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                {% endfor %}
            </div>
            {% endif %}

            <!-- 防守团统计 -->
            {% set defense_members = members|selectattr('main_group', 'equalto', '防守团')|list %}
            {% if defense_members %}
            <div style="background: #f8f9fa; padding: 15px; border-radius: 10px;">
                <h4 style="margin: 0 0 10px 0; color: #dc3545;">防守团 ({{ defense_members|length }}人)</h4>
                {% for sub_team in ['防守团', '防守二团', '防守三团', '防守四团'] %}
                    {% set sub_members = defense_members|selectattr('sub_team', 'equalto', sub_team)|list %}
                    {% if sub_members %}
                    <div style="margin: 5px 0; font-size: 14px;">
                        <strong>{% if sub_team == '防守团' %}一团{% elif sub_team == '防守二团' %}二团{% elif sub_team == '防守三团' %}三团{% elif sub_team == '防守四团' %}四团{% else %}{{ sub_team }}{% endif %}</strong>: {{ sub_members|length }}人
                        <div style="margin-left: 10px; font-size: 12px; color: #6c757d;">
                            {% for squad in ['1队', '2队', '3队', '4队', '5队'] %}
                                {% set squad_members = sub_members|selectattr('squad', 'equalto', squad)|list %}
                                {% if squad_members %}{{ squad }}({{ squad_members|length }}){% if not loop.last %}, {% endif %}{% endif %}
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                {% endfor %}
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 获取所有筛选元素
const searchInput = document.getElementById('searchInput');
const mainGroupFilter = document.getElementById('mainGroupFilter');
const subTeamFilter = document.getElementById('subTeamFilter');
const squadFilter = document.getElementById('squadFilter');
const professionFilter = document.getElementById('professionFilter');
const memberRows = document.querySelectorAll('.member-row');

// 筛选函数
function filterMembers() {
    const searchTerm = searchInput.value.toLowerCase();
    const selectedMainGroup = mainGroupFilter.value;
    const selectedSubTeam = subTeamFilter.value;
    const selectedSquad = squadFilter.value;
    const selectedProfession = professionFilter.value;

    let visibleCount = 0;

    memberRows.forEach(function(row, index) {
        const name = row.dataset.name.toLowerCase();
        const profession = row.dataset.profession;
        const mainGroup = row.dataset.mainGroup;
        const subTeam = row.dataset.subTeam;
        const squad = row.dataset.squad;

        // 检查搜索条件
        const matchesSearch = name.includes(searchTerm) ||
                            profession.toLowerCase().includes(searchTerm) ||
                            mainGroup.toLowerCase().includes(searchTerm) ||
                            subTeam.toLowerCase().includes(searchTerm) ||
                            squad.toLowerCase().includes(searchTerm);

        // 检查各级筛选
        const matchesMainGroup = selectedMainGroup === 'all' || mainGroup === selectedMainGroup;
        const matchesSubTeam = selectedSubTeam === 'all' || subTeam === selectedSubTeam;
        const matchesSquad = selectedSquad === 'all' || squad === selectedSquad;
        const matchesProfession = selectedProfession === 'all' || profession === selectedProfession;

        // 显示或隐藏行
        if (matchesSearch && matchesMainGroup && matchesSubTeam && matchesSquad && matchesProfession) {
            row.style.display = '';
            visibleCount++;
            // 更新序号
            row.querySelector('td:first-child').textContent = visibleCount;
        } else {
            row.style.display = 'none';
        }
    });
}

// 添加事件监听器
searchInput.addEventListener('input', filterMembers);
mainGroupFilter.addEventListener('change', filterMembers);
subTeamFilter.addEventListener('change', filterMembers);
squadFilter.addEventListener('change', filterMembers);
professionFilter.addEventListener('change', filterMembers);

// 表格行悬停效果
memberRows.forEach(function(row) {
    row.addEventListener('mouseenter', function() {
        this.style.backgroundColor = '#f8f9fa';
    });

    row.addEventListener('mouseleave', function() {
        this.style.backgroundColor = '';
    });
});

// 查看成员详情
function viewMemberDetail(memberName) {
    window.location.href = '/member_detail/' + encodeURIComponent(memberName);
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('成员管理页面加载完成，共 ' + memberRows.length + ' 名成员');
});
</script>
{% endblock %}


