#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试战斗数据分析功能
"""

import json
import os

def load_battle_data():
    """加载战斗数据"""
    try:
        battle_file = 'data/20250531_203611_纸落云烟_初影未来.csv'
        
        if not os.path.exists(battle_file):
            print(f"战斗数据文件不存在: {battle_file}")
            return []
        
        battles = []
        current_guild = None
        
        # 尝试不同的编码
        encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312']
        lines = []
        
        for encoding in encodings:
            try:
                with open(battle_file, 'r', encoding=encoding) as f:
                    lines = f.readlines()
                print(f"成功使用编码 {encoding} 读取文件")
                break
            except UnicodeDecodeError:
                continue
        
        if not lines:
            print("无法读取CSV文件")
            return []
        
        print(f"文件总行数: {len(lines)}")
        
        for i, line in enumerate(lines[:20]):  # 只看前20行
            line = line.strip()
            if not line:
                continue
                
            parts = [part.strip().replace('"', '') for part in line.split(',')]
            print(f"行{i+1}: {parts[:5]}...")  # 显示前5列
            
            # 检查帮会名称
            if len(parts) >= 1 and parts[0] in ['纸落云烟', '初影未来']:
                current_guild = parts[0]
                print(f"  -> 找到帮会: {current_guild}")
                continue
            
            # 检查表头
            if len(parts) >= 2 and parts[0] == '玩家名字':
                print(f"  -> 找到表头")
                continue
            
            # 处理成员数据
            if current_guild == '纸落云烟' and len(parts) >= 12:
                try:
                    member_data = {
                        'guild': current_guild,
                        'name': parts[0],
                        'profession': parts[1],
                        'kills': int(parts[2]) if parts[2].isdigit() else 0,
                        'assists': int(parts[3]) if parts[3].isdigit() else 0,
                        'resources': int(parts[4]) if parts[4].isdigit() else 0,
                        'player_damage': int(parts[5]) if parts[5].isdigit() else 0,
                        'building_damage': int(parts[6]) if parts[6].isdigit() else 0,
                        'healing': int(parts[7]) if parts[7].isdigit() else 0,
                        'damage_taken': int(parts[8]) if parts[8].isdigit() else 0,
                        'heavy_injuries': int(parts[9]) if parts[9].isdigit() else 0,
                        'resurrections': int(parts[10]) if parts[10].isdigit() else 0,
                        'demolitions': int(parts[11]) if parts[11].isdigit() else 0
                    }
                    battles.append(member_data)
                    print(f"  -> 添加成员: {member_data['name']} ({member_data['profession']})")
                except (ValueError, IndexError) as e:
                    print(f"  -> 数据解析错误: {e}")
                    continue
        
        print(f"成功加载 {len(battles)} 条战斗数据")
        return battles
        
    except Exception as e:
        print(f"加载战斗数据失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def load_members():
    """加载成员数据"""
    try:
        with open('data/guild_members.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("成员数据文件不存在")
        return []

def calculate_member_score(battle_data, profession):
    """根据职业计算成员评分"""
    score = 0
    
    if profession == '素问':  # 治疗
        score += battle_data['healing'] / 1000000 * 10  # 治疗量权重
        score += battle_data['assists'] * 0.5  # 助攻权重
        score += battle_data['resurrections'] * 5  # 复活权重
        score -= battle_data['heavy_injuries'] * 2  # 重伤扣分
        
    elif profession == '铁衣':  # 坦克
        score += battle_data['damage_taken'] / 1000000 * 8  # 承伤权重
        score += battle_data['assists'] * 0.3
        score += battle_data['kills'] * 2
        score -= battle_data['heavy_injuries'] * 1
        
    elif profession in ['潮光', '九灵', '龙吟', '血河', '碎梦']:  # 输出
        score += battle_data['player_damage'] / 1000000 * 5  # 对玩家伤害权重
        score += battle_data['kills'] * 3  # 击杀权重
        score += battle_data['assists'] * 0.2
        score -= battle_data['heavy_injuries'] * 1.5
        
    elif profession in ['玄机', '神相']:  # 拆塔/辅助
        score += battle_data['building_damage'] / 1000000 * 6  # 对建筑伤害权重
        score += battle_data['demolitions'] * 8  # 拆塔权重
        score += battle_data['assists'] * 0.4
        score += battle_data['kills'] * 2
        
    return max(0, score)  # 确保分数不为负

def get_performance_rating(score):
    """根据分数获取评级"""
    if score >= 100:
        return 'S+'
    elif score >= 80:
        return 'S'
    elif score >= 60:
        return 'A'
    elif score >= 40:
        return 'B'
    elif score >= 20:
        return 'C'
    elif score > 0:
        return 'D'
    else:
        return '未参战'

def analyze_member_performance(battle_data, members):
    """分析成员战斗表现"""
    performance = {}
    
    # 获取纸落云烟的战斗数据
    guild_data = [b for b in battle_data if b['guild'] == '纸落云烟']
    print(f"纸落云烟战斗数据: {len(guild_data)} 条")
    
    for member in members:
        member_name = member['name']
        member_battle = next((b for b in guild_data if b['name'] == member_name), None)
        
        if member_battle:
            # 计算评分
            score = calculate_member_score(member_battle, member['profession'])
            
            performance[member_name] = {
                'battle_data': member_battle,
                'score': score,
                'rating': get_performance_rating(score),
                'profession': member['profession'],
                'team': member['team']
            }
            print(f"{member_name}: 评分 {score:.1f}, 评级 {get_performance_rating(score)}")
        else:
            # 未参战
            performance[member_name] = {
                'battle_data': None,
                'score': 0,
                'rating': '未参战',
                'profession': member['profession'],
                'team': member['team']
            }
            print(f"{member_name}: 未参战")
    
    return performance

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 测试战斗数据分析功能")
    print("=" * 60)
    
    # 加载数据
    print("\n1. 加载成员数据...")
    members = load_members()
    print(f"成员数量: {len(members)}")
    
    print("\n2. 加载战斗数据...")
    battle_data = load_battle_data()
    print(f"战斗数据条数: {len(battle_data)}")
    
    if battle_data:
        print("\n3. 分析成员表现...")
        performance = analyze_member_performance(battle_data, members)
        
        print(f"\n4. 统计结果:")
        participated = sum(1 for p in performance.values() if p['rating'] != '未参战')
        print(f"参战成员: {participated}/{len(members)}")
        
        # 按评分排序显示前10名
        sorted_performance = sorted(performance.items(), key=lambda x: x[1]['score'], reverse=True)
        print(f"\n5. 前10名表现:")
        for i, (name, perf) in enumerate(sorted_performance[:10]):
            if perf['rating'] != '未参战':
                print(f"  {i+1}. {name} ({perf['profession']}) - {perf['rating']} ({perf['score']:.1f}分)")

if __name__ == "__main__":
    main()
