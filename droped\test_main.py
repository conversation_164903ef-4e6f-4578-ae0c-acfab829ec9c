# 测试主程序功能
import sys
import os

# 添加路径
sys.path.append('nishuihan_analyzer')
sys.path.append('nishuihan_analyzer/src')

# 切换到正确的工作目录
original_dir = os.getcwd()
os.chdir('nishuihan_analyzer')

try:
    from main import NishuihanAnalyzer
except ImportError:
    # 如果导入失败，尝试直接执行
    exec(open('main.py').read())

# 测试主程序
try:
    print("=" * 60)
    print("测试纸落云烟数据分析主程序")
    print("=" * 60)
    
    # 创建分析器实例
    analyzer = NishuihanAnalyzer()
    
    # 运行分析（不包括交互式查询）
    print("\n开始运行分析...")
    
    # 1. 加载数据
    if analyzer.load_data():
        print("✅ 数据加载成功")
    else:
        print("❌ 数据加载失败")
        exit(1)
    
    # 2. 分析数据
    if analyzer.analyze_data():
        print("✅ 数据分析成功")
    else:
        print("❌ 数据分析失败")
        exit(1)
    
    # 3. 生成可视化
    if analyzer.generate_visualizations():
        print("✅ 可视化生成成功")
    else:
        print("❌ 可视化生成失败")
    
    # 4. 生成报告
    if analyzer.generate_reports():
        print("✅ 报告生成成功")
    else:
        print("❌ 报告生成失败")
    
    print("\n" + "=" * 60)
    print("主程序测试完成！所有功能正常运行。")
    print("=" * 60)
    
    # 显示一些关键结果
    if analyzer.player_stats is not None:
        print(f"\n📊 分析结果摘要:")
        print(f"  - 分析了 {len(analyzer.player_stats)} 名玩家")
        print(f"  - 战力最高: {analyzer.player_stats['name_first'].iloc[analyzer.player_stats['combat_score'].idxmax()]}")
        print(f"  - 最高战力评分: {analyzer.player_stats['combat_score'].max():.1f}")
        
        # 显示强弱分布
        strength_dist = analyzer.player_stats['strength_level'].value_counts()
        print(f"  - 强弱分布: {dict(strength_dist)}")

except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
