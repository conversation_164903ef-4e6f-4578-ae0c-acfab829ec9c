# 纸落云烟帮会管理系统

一个基于Flask的Web应用，用于管理逆水寒游戏中纸落云烟帮会的成员信息和团队配置。

## 功能特性

### 📊 数据统计
- 帮会成员总览
- 团队人数分布
- 职业分布统计
- 团队配置分析

### 👥 成员管理
- 查看所有成员信息
- 按团队筛选成员
- 搜索成员功能
- 编辑成员信息（职业、团队、位置）

### 🎯 团队管理
- 4个团队的详细配置
- 团队强度评估
- 职业配置检查
- 团队对比分析

### 📈 可视化图表
- 职业分布饼图
- 团队人数柱状图
- 团队配置对比图

## 技术栈

- **后端**: Python Flask
- **前端**: Bootstrap 5 + Chart.js
- **数据**: JSON文件存储
- **图标**: Bootstrap Icons

## 安装和运行

### 1. 环境要求
- Python 3.7+
- pip

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 准备数据
确保 `data/guild_members.json` 文件存在并包含正确的成员数据。

### 4. 启动应用
```bash
python run.py
```
或者直接运行：
```bash
python app.py
```

### 5. 访问应用
打开浏览器访问: http://localhost:5000

## 项目结构

```
guild_management_system/
├── app.py                 # Flask应用主文件
├── run.py                 # 启动脚本
├── requirements.txt       # 依赖包列表
├── README.md             # 项目说明
├── data/                 # 数据目录
│   └── guild_members.json # 成员数据文件
├── templates/            # HTML模板
│   ├── base.html         # 基础模板
│   ├── index.html        # 首页
│   ├── members.html      # 成员管理页面
│   ├── edit_member.html  # 编辑成员页面
│   └── teams.html        # 团队管理页面
└── static/               # 静态文件（CSS、JS、图片）
```

## 数据格式

成员数据格式 (`data/guild_members.json`):
```json
[
  {
    "name": "成员姓名",
    "profession": "职业",
    "team": "团队",
    "position": "位置",
    "status": "状态"
  }
]
```

### 支持的职业
- 素问（治疗）
- 铁衣（坦克）
- 潮光（输出）
- 九灵（输出）
- 龙吟（输出）
- 血河（输出）
- 碎梦（输出）
- 玄机（拆塔）
- 神相（拆塔）

### 团队分类
- 1团、2团、3团、4团

### 位置分类
- 输出、坦克、治疗、拆塔、辅助、控制

## 功能说明

### 首页
- 显示帮会总体统计信息
- 团队概览卡片
- 职业分布和团队人数图表
- 快速操作入口

### 成员管理
- 按团队分组显示所有成员
- 实时搜索功能
- 团队筛选功能
- 点击编辑按钮修改成员信息

### 团队管理
- 显示各团队详细配置
- 团队强度评估（人数、配置完整性）
- 职业分布统计
- 团队对比分析图表

### 编辑成员
- 修改成员职业、团队、位置
- 根据职业自动建议位置
- 表单验证和确认机制

## API接口

- `GET /api/members` - 获取所有成员数据
- `GET /api/team_stats` - 获取团队统计数据
- `GET /api/search_members?q=关键词` - 搜索成员
- `GET /export_data` - 导出数据（JSON格式）

## 开发说明

### 添加新功能
1. 在 `app.py` 中添加路由
2. 在 `templates/` 中创建对应模板
3. 更新导航菜单

### 修改样式
- 主要样式在 `templates/base.html` 的 `<style>` 标签中
- 使用Bootstrap 5类进行布局和样式

### 数据备份
- 定期备份 `data/guild_members.json` 文件
- 可通过 `/export_data` 接口导出数据

## 注意事项

1. **数据安全**: 定期备份成员数据文件
2. **权限控制**: 当前版本无用户认证，建议在内网使用
3. **性能**: 适用于中小型帮会（100人以内）
4. **浏览器兼容**: 建议使用现代浏览器（Chrome、Firefox、Edge）

## 更新日志

### v1.0.0 (2024-06-01)
- 初始版本发布
- 基础成员管理功能
- 团队配置管理
- 数据统计和可视化

## 联系方式

如有问题或建议，请联系开发者。

---

**纸落云烟帮会管理系统** - 让帮会管理更简单高效！
