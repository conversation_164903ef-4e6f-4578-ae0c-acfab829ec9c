# 测试职业修复结果
import sys
import os
sys.path.append('nishuihan_analyzer/src')

from data_processor import DataProcessor

try:
    print("=" * 60)
    print("测试职业修复结果")
    print("=" * 60)
    
    # 加载数据
    processor = DataProcessor("nishuihan_analyzer/data", "nishuihan_analyzer/纸落弈酒.xlsx")
    raw_data = processor.load_all_data()
    df = processor.create_unified_dataframe()
    
    # 检查特定玩家的职业
    test_players = ['小金丶', '祁寒', '彼岸花开乀']
    
    print("检查特定玩家职业:")
    for player_name in test_players:
        player_records = df[df['name'] == player_name]
        if len(player_records) > 0:
            profession = player_records['profession'].iloc[0]
            print(f"  {player_name}: {profession}")
        else:
            print(f"  {player_name}: 未找到")
    
    print("\n完整职业分布:")
    profession_dist = df['profession'].value_counts()
    for profession, count in profession_dist.items():
        print(f"  {profession}: {count}人次")
    
    # 检查是否还有unknown
    unknown_count = df[df['profession'].isin(['unknown', 'nil', 'nan', ''])].shape[0]
    print(f"\n职业不确定的记录: {unknown_count}条")
    
    if unknown_count == 0:
        print("✅ 所有职业信息都已正确！")
    else:
        print("❌ 仍有职业不确定的记录")

except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()
