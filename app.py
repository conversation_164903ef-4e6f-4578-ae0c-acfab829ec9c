#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
纸落云烟帮会管理系统 - Web应用
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for
import json
import os
from datetime import datetime

app = Flask(__name__)

# 数据文件路径
MEMBERS_FILE = 'members_with_teams.json'
BATTLE_DATA_FILE = '20250531_203611_纸落云烟_初影未来.csv'

def load_members():
    """加载成员数据"""
    try:
        with open(MEMBERS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return []

def save_members(members):
    """保存成员数据"""
    with open(MEMBERS_FILE, 'w', encoding='utf-8') as f:
        json.dump(members, f, ensure_ascii=False, indent=2)

def get_team_stats(members):
    """获取团队统计信息"""
    stats = {
        '1团': {'count': 0, 'professions': {}},
        '2团': {'count': 0, 'professions': {}},
        '3团': {'count': 0, 'professions': {}},
        '防守团': {'count': 0, 'professions': {}}
    }
    
    for member in members:
        team = member.get('team', '未分配')
        if team in stats:
            stats[team]['count'] += 1
            prof = member['profession']
            stats[team]['professions'][prof] = stats[team]['professions'].get(prof, 0) + 1
    
    return stats

@app.route('/')
def index():
    """主页"""
    members = load_members()
    stats = get_team_stats(members)
    
    return render_template('index.html', 
                         members=members, 
                         stats=stats,
                         total_members=len(members))

@app.route('/members')
def members_list():
    """成员列表页面"""
    members = load_members()
    
    # 按团队分组
    teams = {
        '1团': [],
        '2团': [],
        '3团': [],
        '防守团': [],
        '未分配': []
    }
    
    for member in members:
        team = member.get('team', '未分配')
        if team not in teams:
            teams[team] = []
        teams[team].append(member)
    
    return render_template('members.html', teams=teams)

@app.route('/edit_member/<member_name>')
def edit_member(member_name):
    """编辑成员页面"""
    members = load_members()
    member = next((m for m in members if m['name'] == member_name), None)
    
    if not member:
        return redirect(url_for('members_list'))
    
    # 职业列表
    professions = ['素问', '潮光', '九灵', '铁衣', '玄机', '龙吟', '血河', '神相', '碎梦']
    
    # 团队列表
    teams = ['1团', '2团', '3团', '防守团']
    
    # 位置列表
    positions = ['输出', '坦克', '治疗', '拆塔', '辅助', '控制']
    
    return render_template('edit_member.html', 
                         member=member,
                         professions=professions,
                         teams=teams,
                         positions=positions)

@app.route('/update_member', methods=['POST'])
def update_member():
    """更新成员信息"""
    members = load_members()
    
    member_name = request.form['name']
    new_profession = request.form['profession']
    new_team = request.form['team']
    new_position = request.form['position']
    
    # 查找并更新成员
    for member in members:
        if member['name'] == member_name:
            member['profession'] = new_profession
            member['team'] = new_team
            member['position'] = new_position
            break
    
    save_members(members)
    return redirect(url_for('members_list'))

@app.route('/teams')
def teams_view():
    """团队视图页面"""
    members = load_members()
    stats = get_team_stats(members)
    
    # 按团队分组
    teams = {
        '1团': [],
        '2团': [],
        '3团': [],
        '防守团': []
    }
    
    for member in members:
        team = member.get('team', '')
        if team in teams:
            teams[team].append(member)
    
    return render_template('teams.html', teams=teams, stats=stats)

@app.route('/battle_data')
def battle_data():
    """战斗数据页面"""
    return render_template('battle_data.html')

@app.route('/api/members')
def api_members():
    """API: 获取所有成员"""
    members = load_members()
    return jsonify(members)

@app.route('/api/team_stats')
def api_team_stats():
    """API: 获取团队统计"""
    members = load_members()
    stats = get_team_stats(members)
    return jsonify(stats)

@app.route('/export_data')
def export_data():
    """导出数据"""
    members = load_members()
    
    # 创建导出数据
    export_data = {
        'guild_name': '纸落云烟',
        'export_time': datetime.now().isoformat(),
        'total_members': len(members),
        'members': members
    }
    
    return jsonify(export_data)

if __name__ == '__main__':
    # 确保模板目录存在
    if not os.path.exists('templates'):
        os.makedirs('templates')
    
    # 确保静态文件目录存在
    if not os.path.exists('static'):
        os.makedirs('static')
    
    print("=" * 60)
    print("纸落云烟帮会管理系统启动中...")
    print("=" * 60)
    print("访问地址: http://localhost:5000")
    print("功能包括:")
    print("  - 成员管理 (查看、编辑)")
    print("  - 团队分配")
    print("  - 数据统计")
    print("  - 数据导出")
    print("=" * 60)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
