#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完全重新开始！基于帮战名单表.xlsx重新提取所有数据
"""

import pandas as pd
import json

def analyze_battle_roster_excel():
    """完全重新分析帮战名单表.xlsx"""
    print("=" * 80)
    print("🔄 完全重新开始！基于帮战名单表.xlsx重新提取所有数据")
    print("=" * 80)
    
    excel_file = "帮战名单表.xlsx"
    
    try:
        # 获取所有工作表
        excel_file_obj = pd.ExcelFile(excel_file)
        sheet_names = excel_file_obj.sheet_names
        
        print(f"📋 工作表列表: {sheet_names}")
        print()
        
        all_members = []
        
        # 分析每个工作表
        for sheet_name in sheet_names:
            print(f"--- 📊 分析工作表: {sheet_name} ---")
            try:
                df = pd.read_excel(excel_file, sheet_name=sheet_name, header=None)
                print(f"表格大小: {df.shape[0]}行 x {df.shape[1]}列")
                
                # 显示完整内容以便理解结构
                print("完整内容预览:")
                for row_idx in range(min(20, df.shape[0])):
                    row_content = []
                    for col_idx in range(min(15, df.shape[1])):
                        cell = df.iloc[row_idx, col_idx]
                        if pd.notna(cell):
                            row_content.append(f"列{col_idx+1}:'{cell}'")
                    if row_content:
                        print(f"  行{row_idx+1}: {' | '.join(row_content)}")
                
                # 提取这个工作表中的成员数据
                sheet_members = extract_members_from_sheet(df, sheet_name)
                all_members.extend(sheet_members)
                
                print(f"从 {sheet_name} 提取到 {len(sheet_members)} 名成员")
                print()
                
            except Exception as e:
                print(f"读取工作表 {sheet_name} 失败: {e}")
                print()
        
        return all_members
        
    except Exception as e:
        print(f"分析Excel文件失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def extract_members_from_sheet(df, sheet_name):
    """从单个工作表中提取成员信息"""
    members = []
    
    # 职业列表
    professions = ['素问', '潮光', '九灵', '铁衣', '玄机', '龙吟', '血河', '神相', '碎梦']
    
    # 团队关键词
    team_keywords = ['一团', '二团', '三团', '四团', '1团', '2团', '3团', '4团', '防守团', '进攻团']
    
    current_team = None
    current_profession = None
    
    for row_idx in range(df.shape[0]):
        for col_idx in range(df.shape[1]):
            cell_value = df.iloc[row_idx, col_idx]
            if pd.notna(cell_value):
                cell_text = str(cell_value).strip()
                
                # 检查是否是团队标识
                for keyword in team_keywords:
                    if keyword in cell_text:
                        current_team = keyword
                        print(f"    找到团队: {current_team} (行{row_idx+1}列{col_idx+1})")
                        break
                
                # 检查是否是职业
                if cell_text in professions:
                    current_profession = cell_text
                    print(f"    找到职业: {current_profession} (行{row_idx+1}列{col_idx+1})")
                
                # 检查是否是玩家名字（排除明显不是名字的内容）
                if (len(cell_text) >= 2 and len(cell_text) <= 15 and
                    cell_text not in professions and
                    not any(keyword in cell_text for keyword in team_keywords) and
                    not cell_text.isdigit() and
                    cell_text not in ['输出', '坦克', '治疗', '辅助', '拆塔', '控制', '备注', '姓名', '职业', '团队', '位置'] and
                    not any(word in cell_text for word in ['缺少', '设置', '统计', '总计', '页'])):
                    
                    # 这可能是玩家名字
                    member = {
                        'name': cell_text,
                        'profession': current_profession or '未知',
                        'team': current_team or '未分配',
                        'position': '',
                        'status': '主力',
                        'source': f'{sheet_name}_行{row_idx+1}列{col_idx+1}'
                    }
                    
                    # 检查是否已经存在
                    if not any(m['name'] == cell_text for m in members):
                        members.append(member)
                        print(f"    添加成员: {cell_text} ({current_profession or '未知'}) -> {current_team or '未分配'}")
    
    return members

def clean_and_deduplicate_members(all_members):
    """清理和去重成员数据"""
    print("=" * 60)
    print("🧹 清理和去重成员数据")
    print("=" * 60)
    
    # 去重（基于姓名）
    unique_members = []
    seen_names = set()
    
    for member in all_members:
        name = member['name']
        if name not in seen_names:
            unique_members.append(member)
            seen_names.add(name)
        else:
            # 如果重复，保留信息更完整的
            existing = next(m for m in unique_members if m['name'] == name)
            if member['profession'] != '未知' and existing['profession'] == '未知':
                existing['profession'] = member['profession']
            if member['team'] != '未分配' and existing['team'] == '未分配':
                existing['team'] = member['team']
    
    print(f"去重前: {len(all_members)} 条记录")
    print(f"去重后: {len(unique_members)} 名成员")
    
    # 团队名称标准化
    team_mapping = {
        '一团': '1团',
        '二团': '2团',
        '三团': '3团',
        '四团': '4团'
    }
    
    for member in unique_members:
        original_team = member['team']
        for old, new in team_mapping.items():
            if old in original_team:
                member['team'] = new
                break
    
    return unique_members

def generate_new_summary(members):
    """生成新的数据总结"""
    print("\n" + "=" * 80)
    print("📊 基于帮战名单表.xlsx的最新数据总结")
    print("=" * 80)
    
    print(f"总成员数: {len(members)}人")
    
    # 团队统计
    team_stats = {}
    for member in members:
        team = member['team']
        if team not in team_stats:
            team_stats[team] = {'count': 0, 'professions': {}}
        team_stats[team]['count'] += 1
        
        prof = member['profession']
        if prof not in team_stats[team]['professions']:
            team_stats[team]['professions'][prof] = []
        team_stats[team]['professions'][prof].append(member['name'])
    
    print(f"\n🎯 团队分布:")
    for team, stats in sorted(team_stats.items()):
        print(f"\n【{team}】({stats['count']}人):")
        for prof, names in sorted(stats['professions'].items()):
            print(f"  {prof}({len(names)}): {', '.join(names)}")
    
    # 职业统计
    profession_stats = {}
    for member in members:
        prof = member['profession']
        profession_stats[prof] = profession_stats.get(prof, 0) + 1
    
    print(f"\n💼 职业分布:")
    for prof, count in sorted(profession_stats.items()):
        print(f"  {prof}: {count}人")
    
    return members

def main():
    """主函数"""
    print("🚀 开始重新提取帮战名单数据...")
    
    # 完全重新分析Excel文件
    all_members = analyze_battle_roster_excel()
    
    if not all_members:
        print("❌ 未能提取到任何成员数据！")
        return
    
    # 清理和去重
    clean_members = clean_and_deduplicate_members(all_members)
    
    # 生成总结
    final_members = generate_new_summary(clean_members)
    
    # 保存新数据
    with open('members_from_battle_roster.json', 'w', encoding='utf-8') as f:
        json.dump(final_members, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 重新提取完成！")
    print(f"📁 新数据已保存到: members_from_battle_roster.json")
    print(f"📊 总共提取到 {len(final_members)} 名成员")
    
    print(f"\n🎯 这是基于帮战名单表.xlsx的全新数据，完全替代之前的数据！")

if __name__ == "__main__":
    main()
