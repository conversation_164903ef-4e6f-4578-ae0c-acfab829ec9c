#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第一步：从CSV文件中提取纸落云烟帮会成员信息
"""

import pandas as pd
import json

def extract_guild_members(csv_file):
    """从CSV文件中提取纸落云烟帮会成员信息"""

    members = []
    current_guild = None
    header_found = False

    # 直接读取文本文件，处理BOM
    try:
        with open(csv_file, 'r', encoding='utf-8-sig') as f:
            lines = f.readlines()
    except UnicodeDecodeError:
        with open(csv_file, 'r', encoding='gbk') as f:
            lines = f.readlines()

    for line_num, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue

        # 分割CSV行
        parts = [part.strip().replace('"', '') for part in line.split(',')]

        # 检查是否是帮会名称行
        if len(parts) >= 1 and parts[0] == '纸落云烟':
            current_guild = '纸落云烟'
            header_found = False
            print(f"找到纸落云烟帮会数据，行号: {line_num}")
            continue
        elif len(parts) >= 1 and parts[0] == '初影未来':
            # 遇到对手帮会，停止处理
            print(f"遇到对手帮会，停止处理，行号: {line_num}")
            break

        # 检查是否是表头行
        if current_guild == '纸落云烟' and not header_found:
            if len(parts) >= 2 and parts[0] == '玩家名字':
                header_found = True
                print(f"找到表头行，行号: {line_num}")
                continue

        # 提取成员数据
        if current_guild == '纸落云烟' and header_found:
            if len(parts) >= 2 and parts[0] and parts[1]:
                name = parts[0]
                profession = parts[1]

                # 跳过空行和无效数据
                if name and profession and name != '玩家名字':
                    members.append({
                        'name': name,
                        'profession': profession,
                        'team': '',  # 待分配
                        'position': ''  # 待分配
                    })
                    print(f"添加成员: {name} - {profession}")

    return members

def main():
    """主函数"""
    print("=" * 60)
    print("第一步：提取纸落云烟帮会成员信息")
    print("=" * 60)
    
    # 提取成员信息
    csv_file = "20250531_203611_纸落云烟_初影未来.csv"
    members = extract_guild_members(csv_file)
    
    print(f"从 {csv_file} 中提取到 {len(members)} 名成员：")
    print("-" * 40)
    
    # 按职业分组显示
    profession_groups = {}
    for member in members:
        prof = member['profession']
        if prof not in profession_groups:
            profession_groups[prof] = []
        profession_groups[prof].append(member['name'])
    
    for profession, names in profession_groups.items():
        print(f"{profession}: {len(names)}人")
        for name in names:
            print(f"  - {name}")
        print()
    
    # 保存到JSON文件
    output_file = "guild_members.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(members, f, ensure_ascii=False, indent=2)
    
    print(f"成员信息已保存到: {output_file}")
    print("\n下一步：我们需要为每个成员分配团队（1、2、3进攻团，防守团）和位置")

if __name__ == "__main__":
    main()
