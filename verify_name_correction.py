#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证名字修正并生成更新后的总结
"""

import json

def verify_name_correction():
    """验证名字修正"""
    print("=" * 60)
    print("🔍 验证成员名字修正")
    print("=" * 60)
    
    # 读取更新后的数据
    with open('final_guild_members.json', 'r', encoding='utf-8') as f:
        members = json.load(f)
    
    # 查找修正后的成员
    corrected_member = None
    for member in members:
        if member['name'] == '搅史的棍':
            corrected_member = member
            break
    
    if corrected_member:
        print("✅ 成员名字修正成功！")
        print(f"   姓名: {corrected_member['name']}")
        print(f"   职业: {corrected_member['profession']}")
        print(f"   团队: {corrected_member['team']}")
        print(f"   位置: {corrected_member['position']}")
    else:
        print("❌ 未找到修正后的成员")
    
    # 确认没有旧名字
    old_name_found = any(member['name'] == '搅屎的棍' for member in members)
    if old_name_found:
        print("⚠️ 警告：仍然存在旧名字 '搅屎的棍'")
    else:
        print("✅ 确认：旧名字 '搅屎的棍' 已完全移除")
    
    return members

def generate_updated_summary(members):
    """生成更新后的总结"""
    print("\n" + "=" * 60)
    print("📊 更新后的2团成员列表")
    print("=" * 60)
    
    # 显示2团成员
    team2_members = [m for m in members if m['team'] == '2团']
    
    print(f"【2团】({len(team2_members)}人):")
    
    # 按职业分组
    profession_groups = {}
    for member in team2_members:
        prof = member['profession']
        if prof not in profession_groups:
            profession_groups[prof] = []
        profession_groups[prof].append(member['name'])
    
    for prof, names in sorted(profession_groups.items()):
        print(f"  {prof}({len(names)}): {', '.join(names)}")
    
    print(f"\n✅ 成员名字修正完成！")
    print(f"📁 最终数据文件: final_guild_members.json")
    print(f"👥 总成员数: {len(members)}人")

def main():
    """主函数"""
    # 验证名字修正
    members = verify_name_correction()
    
    # 生成更新后的总结
    generate_updated_summary(members)

if __name__ == "__main__":
    main()
