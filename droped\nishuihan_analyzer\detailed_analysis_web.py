"""
纸落云烟数据分析 - 每个人详细分析Web版本
专门显示每个人的完整分析数据
"""

from flask import Flask, render_template_string
import sys
import os

# 添加路径
sys.path.append('src')

app = Flask(__name__)

# 详细分析HTML模板
DETAILED_HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>纸落云烟 - 每个人详细分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #667eea;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .search-box {
            width: 100%;
            max-width: 500px;
            margin: 20px auto;
            padding: 15px;
            border: 2px solid #667eea;
            border-radius: 10px;
            font-size: 1.1em;
            display: block;
        }
        .player-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .player-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            border-left: 5px solid #667eea;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .player-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .player-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        .player-name {
            font-size: 1.4em;
            font-weight: bold;
            color: #333;
        }
        .player-profession {
            background: #667eea;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }
        .stats-section {
            margin-bottom: 15px;
        }
        .section-title {
            font-size: 1.1em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 8px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 3px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            font-size: 0.9em;
        }
        .stat-item {
            display: flex;
            justify-content: space-between;
            padding: 3px 0;
        }
        .stat-label {
            color: #666;
        }
        .stat-value {
            font-weight: bold;
            color: #333;
        }
        .position-analysis {
            background: #e3f2fd;
            padding: 12px;
            border-radius: 8px;
            margin-top: 10px;
        }
        .position-scores {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 5px;
            margin-top: 8px;
        }
        .position-score {
            display: flex;
            justify-content: space-between;
            font-size: 0.85em;
        }
        .best-position {
            background: #4caf50;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            display: inline-block;
            margin-top: 5px;
        }
        .strength-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: bold;
            display: inline-block;
            margin-top: 10px;
        }
        .strength-超强 { background: #ff6b6b; color: white; }
        .strength-很强 { background: #ff8e53; color: white; }
        .strength-强 { background: #ffa726; color: white; }
        .strength-中等偏强 { background: #66bb6a; color: white; }
        .strength-中等 { background: #42a5f5; color: white; }
        .strength-中等偏弱 { background: #ab47bc; color: white; }
        .strength-弱 { background: #78909c; color: white; }
        .strength-很弱 { background: #90a4ae; color: white; }
        .summary-stats {
            background: #fff3e0;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .summary-number {
            font-size: 2em;
            font-weight: bold;
            color: #ff9800;
            margin: 0 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>纸落云烟 - 每个人详细分析</h1>
            <p>完整的个人数据分析报告</p>
            
            <div class="summary-stats">
                <span class="summary-number">{{ total_players }}</span>名玩家
                <span class="summary-number">{{ total_battles }}</span>场战斗
                <span class="summary-number">9</span>个职业
                <span class="summary-number">100%</span>职业完整
            </div>
        </div>
        
        <input type="text" class="search-box" id="searchBox" placeholder="搜索玩家名字或职业..." onkeyup="filterPlayers()">
        
        <div class="player-grid" id="playerGrid">
            {% for player in players %}
            <div class="player-card" data-name="{{ player.name.lower() }}" data-profession="{{ player.profession.lower() }}">
                <div class="player-header">
                    <div class="player-name">{{ player.name }}</div>
                    <div class="player-profession">{{ player.profession }}</div>
                </div>
                
                <div class="stats-section">
                    <div class="section-title">基础数据</div>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">参战次数:</span>
                            <span class="stat-value">{{ player.battles_count }}场</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">战力评分:</span>
                            <span class="stat-value">{{ player.combat_score }}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">KDA:</span>
                            <span class="stat-value">{{ player.kda }}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">对手强度:</span>
                            <span class="stat-value">{{ player.enemy_strength }}</span>
                        </div>
                    </div>
                </div>
                
                <div class="stats-section">
                    <div class="section-title">战斗统计</div>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">总击败:</span>
                            <span class="stat-value">{{ player.kills_total }}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">平均击败:</span>
                            <span class="stat-value">{{ player.kills_avg }}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">总助攻:</span>
                            <span class="stat-value">{{ player.assists_total }}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">平均助攻:</span>
                            <span class="stat-value">{{ player.assists_avg }}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">总死亡:</span>
                            <span class="stat-value">{{ player.deaths_total }}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">平均死亡:</span>
                            <span class="stat-value">{{ player.deaths_avg }}</span>
                        </div>
                    </div>
                </div>
                
                <div class="stats-section">
                    <div class="section-title">伤害数据</div>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">对玩家伤害:</span>
                            <span class="stat-value">{{ player.player_damage }}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">拆塔伤害:</span>
                            <span class="stat-value">{{ player.building_damage }}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">治疗量:</span>
                            <span class="stat-value">{{ player.healing }}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">承受伤害:</span>
                            <span class="stat-value">{{ player.damage_taken }}</span>
                        </div>
                    </div>
                </div>
                
                <div class="stats-section">
                    <div class="section-title">特殊数据</div>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">重伤:</span>
                            <span class="stat-value">{{ player.heavy_injuries }}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">羽化/清泉:</span>
                            <span class="stat-value">{{ player.resurrections }}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">资源:</span>
                            <span class="stat-value">{{ player.resources }}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">生存率:</span>
                            <span class="stat-value">{{ player.survival_rate }}%</span>
                        </div>
                    </div>
                </div>
                
                <div class="position-analysis">
                    <div class="section-title">位置适合性分析</div>
                    <div class="best-position">最适合: {{ player.best_position_name }} ({{ player.best_position_score }}分)</div>
                    <div class="position-scores">
                        {% for pos_name, score in player.position_scores.items() %}
                        <div class="position-score">
                            <span>{{ pos_name }}:</span>
                            <span>{{ score }}分</span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                
                <div class="strength-badge strength-{{ player.strength_level }}">{{ player.strength_level }}</div>
            </div>
            {% endfor %}
        </div>
    </div>

    <script>
        function filterPlayers() {
            const searchTerm = document.getElementById('searchBox').value.toLowerCase();
            const playerCards = document.querySelectorAll('.player-card');
            
            playerCards.forEach(card => {
                const name = card.getAttribute('data-name');
                const profession = card.getAttribute('data-profession');
                
                if (name.includes(searchTerm) || profession.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>
"""

@app.route('/')
def detailed_analysis():
    try:
        # 导入分析模块
        from data_processor import DataProcessor
        from analyzer import NishuihanPlayerAnalyzer
        
        # 加载数据
        processor = DataProcessor("data", "纸落弈酒.xlsx")
        raw_data = processor.load_all_data()
        df = processor.create_unified_dataframe()
        
        # 分析数据
        analyzer = NishuihanPlayerAnalyzer(df)
        player_stats = analyzer.calculate_player_scores()
        position_analysis = analyzer.analyze_position_suitability()
        
        # 准备每个人的详细数据
        players = []
        position_names = {
            'damage_dealer': '输出位',
            'tank': '坦克位',
            'support': '辅助位',
            'demolisher': '拆塔位'
        }
        
        for unique_id, player_data in player_stats.iterrows():
            # 获取位置分析数据
            pos_data = position_analysis.get(unique_id, {})
            
            player_info = {
                'name': player_data['name_first'],
                'profession': player_data['profession_first'],
                'battles_count': int(player_data['battles_count']),
                'combat_score': round(float(player_data['combat_score']), 1),
                'kda': round(float(player_data['kda']), 2),
                'enemy_strength': round(float(player_data['enemy_strength_mean']), 1),
                'strength_level': player_data['strength_level'],
                
                # 战斗统计
                'kills_total': int(player_data['kills_sum']),
                'kills_avg': round(float(player_data['kills_mean']), 1),
                'assists_total': int(player_data['assists_sum']),
                'assists_avg': round(float(player_data['assists_mean']), 1),
                'deaths_total': int(player_data['deaths_sum']),
                'deaths_avg': round(float(player_data['deaths_mean']), 1),
                
                # 伤害数据
                'player_damage': f"{int(player_data['player_damage_sum']):,}",
                'building_damage': f"{int(player_data['building_damage_sum']):,}",
                'healing': f"{int(player_data['healing_sum']):,}",
                'damage_taken': f"{int(player_data['damage_taken_sum']):,}",
                
                # 特殊数据
                'heavy_injuries': round(float(player_data['heavy_injuries_mean']), 1),
                'resurrections': round(float(player_data['resurrections_mean']), 1),
                'resources': round(float(player_data['resources_mean']), 1),
                'survival_rate': round(float(player_data['survival_rate']) * 100, 1),
                
                # 位置分析
                'best_position_name': position_names.get(pos_data.get('best_position', 'unknown'), '未知'),
                'best_position_score': round(float(pos_data.get('best_score', 0)), 1),
                'position_scores': {
                    position_names[pos]: f"{score:.1f}" 
                    for pos, score in pos_data.get('position_scores', {}).items()
                    if pos in position_names
                }
            }
            
            players.append(player_info)
        
        # 按战力排序
        players.sort(key=lambda x: x['combat_score'], reverse=True)
        
        return render_template_string(DETAILED_HTML_TEMPLATE,
            players=players,
            total_players=len(players),
            total_battles=len(raw_data)
        )
        
    except Exception as e:
        return f"""
        <h1>数据加载失败</h1>
        <p>错误信息: {str(e)}</p>
        <p>请确保数据文件存在并且格式正确。</p>
        <p><a href="javascript:location.reload()">重新加载</a></p>
        """

if __name__ == '__main__':
    print("启动纸落云烟详细分析Web应用...")
    print("访问地址: http://localhost:5001")
    print("这个页面显示每个人的完整分析数据！")
    app.run(debug=True, host='0.0.0.0', port=5001)
