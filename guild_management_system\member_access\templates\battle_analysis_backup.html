{% extends "base.html" %}

{% block title %}{% if readonly_mode %}战斗分析 - 纸落云烟{% else %}战斗分析 - 纸落云烟帮会管理系统{% endif %}{% endblock %}

{% block content %}
{% if readonly_mode %}
<!-- 只读模式横幅 -->
<div style="background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px; text-align: center; margin-bottom: 20px; border-radius: 10px;">
    <h2 style="margin: 0; font-size: 20px;"><i class="fas fa-chart-line"></i> 战斗分析 - 只读模式</h2>
    <p style="margin: 5px 0 0 0; opacity: 0.9; font-size: 14px;">📖 查看战斗数据和分析，与管理后台数据实时同步</p>
</div>
{% else %}
<!-- 上传战斗数据区域 -->
<div class="stat-card" style="margin-bottom: 20px;">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
        <h3 style="color: #667eea; margin: 0;">📤 上传战斗数据</h3>
        <button id="toggleUpload" style="background: #667eea; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer;">
            显示上传
        </button>
    </div>

    <div id="uploadSection" style="display: none;">
        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
            <p style="color: #6c757d; margin: 0; font-size: 14px;">
                📋 上传格式：<code>纸落云烟_对手帮会.csv</code> 或 <code>日期_时间_纸落云烟_对手帮会.csv</code>
            </p>
        </div>

        <form id="uploadForm" enctype="multipart/form-data">
            <div style="display: flex; gap: 15px; align-items: center;">
                <input type="file" id="battleFile" name="battle_file" accept=".csv"
                       style="flex: 1; padding: 10px; border: 2px dashed #667eea; border-radius: 8px; background: rgba(102, 126, 234, 0.05);">
                <button type="submit" id="uploadBtn" disabled
                        style="background: #28a745; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; white-space: nowrap;">
                    🚀 上传分析
                </button>
            </div>

            <div id="uploadStatus" style="margin-top: 10px; padding: 10px; border-radius: 6px; display: none;"></div>
        </form>
    </div>
</div>
{% endif %}

<!-- 战斗记录列表 -->
{% if battle_records %}
<div style="margin-bottom: 20px;">
    <h3 style="color: #667eea; margin-bottom: 15px;">⚔️ 战斗记录 ({{ battle_records|length }} 场)</h3>

    {% for record in battle_records %}
    <div class="stat-card" style="margin-bottom: 20px;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <div>
                <h4 style="color: #2c3e50; margin: 0;">
                    {{ record.our_guild }} VS {{ record.enemy_guild }}
                </h4>
                <div style="font-size: 12px; color: #6c757d; margin-top: 5px;">
                    📅 {{ record.upload_time[:19].replace('T', ' ') }} | 📁 {{ record.filename }}
                </div>
            </div>
            <div style="text-align: right; display: flex; gap: 10px;">
                <button onclick="toggleBattleDetail('{{ record.battle_id }}')"
                        style="background: #667eea; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer;">
                    查看详情
                </button>
                {% if not readonly_mode %}
                <button onclick="deleteBattleRecord('{{ record.battle_id }}', '{{ record.our_guild }} VS {{ record.enemy_guild }}')"
                        style="background: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer;">
                    🗑️ 删除
                </button>
                {% endif %}
            </div>
        </div>

        <!-- 战斗概览 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-bottom: 15px;">
            <div style="text-align: center; background: #f8f9fa; padding: 10px; border-radius: 8px;">
                <div style="font-size: 20px; font-weight: bold; color: #28a745;">{{ record.participated_members }}</div>
                <div style="color: #6c757d; font-size: 12px;">参战成员</div>
            </div>
            <div style="text-align: center; background: #f8f9fa; padding: 10px; border-radius: 8px;">
                <div style="font-size: 20px; font-weight: bold; color: #ffc107;">{{ record.new_members }}</div>
                <div style="color: #6c757d; font-size: 12px;">新成员/替补</div>
            </div>
            <div style="text-align: center; background: #f8f9fa; padding: 10px; border-radius: 8px;">
                {% set absent_count = (record.member_performance.values() | selectattr('status', 'equalto', '未参战') | list | length) %}
                <div style="font-size: 20px; font-weight: bold; color: #dc3545;">{{ absent_count }}</div>
                <div style="color: #6c757d; font-size: 12px;">缺席成员</div>
            </div>
            <div style="text-align: center; background: #f8f9fa; padding: 10px; border-radius: 8px;">
                <div style="font-size: 20px; font-weight: bold; color: #667eea;">
                    {% set avg_score = (record.member_performance.values() | selectattr('battle_data') | map(attribute='score') | list | sum) / (record.member_performance.values() | selectattr('battle_data') | list | length) if (record.member_performance.values() | selectattr('battle_data') | list | length) > 0 else 0 %}
                    {{ "%.1f"|format(avg_score) }}
                </div>
                <div style="color: #6c757d; font-size: 12px;">平均评分</div>
            </div>
        </div>

        <!-- 替补和缺席详情 -->
        {% if record.substitute_analysis %}
        <div style="background: #f8f9fa; padding: 12px; border-radius: 8px; margin-bottom: 15px; font-size: 12px;">
            <!-- 替补关系 -->
            {% if record.substitute_analysis.relations %}
            <div style="margin-bottom: 10px;">
                <strong style="color: #28a745;">🔄 替补关系 ({{ record.substitute_analysis.relations|length }}组):</strong>
                <div style="margin-top: 5px;">
                    {% for relation in record.substitute_analysis.relations %}
                    <div style="background: white; padding: 6px; border-radius: 4px; margin: 3px 0; border-left: 3px solid {% if relation.confidence == 'high' %}#28a745{% else %}#ffc107{% endif %};">
                        <span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 3px;">
                            {{ relation.substitute }}({{ relation.substitute_profession }})
                        </span>
                        <span style="color: #6c757d; margin: 0 5px;">替补</span>
                        <span style="background: #dc3545; color: white; padding: 2px 6px; border-radius: 3px;">
                            {{ relation.replaced }}({{ relation.replaced_profession }}/{{ relation.replaced_team }})
                        </span>
                        {% if relation.confidence == 'medium' %}
                        <span style="color: #ffc107; font-size: 10px;">⚠️推测</span>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- 未匹配的替补 -->
            {% if record.substitute_analysis.unmatched_substitutes %}
            <div style="margin-bottom: 8px;">
                <strong style="color: #ffc107;">🆕 其他替补 ({{ record.substitute_analysis.unmatched_substitutes|length }}人):</strong>
                {% for sub_name in record.substitute_analysis.unmatched_substitutes %}
                {% set sub_info = record.member_performance.get(sub_name, {}) %}
                <span style="background: #ffc107; color: white; padding: 2px 6px; border-radius: 4px; margin: 2px;">
                    {{ sub_name }}({{ sub_info.get('profession', '未知') }})
                </span>
                {% endfor %}
            </div>
            {% endif %}

            <!-- 未匹配的缺席 -->
            {% if record.substitute_analysis.unmatched_absent %}
            <div>
                <strong style="color: #dc3545;">❌ 缺席成员 ({{ record.substitute_analysis.unmatched_absent|length }}人):</strong>
                {% for absent_name in record.substitute_analysis.unmatched_absent %}
                {% set absent_info = record.member_performance.get(absent_name, {}) %}
                <span style="background: #dc3545; color: white; padding: 2px 6px; border-radius: 4px; margin: 2px;">
                    {{ absent_name }}({{ absent_info.get('profession', '未知') }}/{{ absent_info.get('team', '未知') }})
                </span>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        {% endif %}

        <!-- 详细数据（默认隐藏） -->
        <div id="detail_{{ record.battle_id }}" style="display: none;">
            <!-- 这里放置详细的成员表现数据 -->
            <div style="border-top: 1px solid #dee2e6; padding-top: 15px;">
                <h5 style="color: #667eea; margin-bottom: 10px;">成员表现详情</h5>
                <!-- 筛选和排序控件 -->
                <div style="display: flex; gap: 10px; margin-bottom: 15px; flex-wrap: wrap;">
                    <select class="team-type-filter" data-battle-id="{{ record.battle_id }}" style="padding: 8px; border: none; border-radius: 6px; background: rgba(255, 255, 255, 0.9); font-size: 12px;">
                        <option value="all">全部成员</option>
                        <option value="进攻团">🗡️ 进攻团</option>
                        <option value="防守团">🛡️ 防守团</option>
                        <option value="新成员/替补">🆕 新成员/替补</option>
                        <option value="缺席">❌ 缺席成员</option>
                        <option value="替补缺席">💤 替补缺席</option>
                    </select>

                    <select class="status-filter" data-battle-id="{{ record.battle_id }}" style="padding: 8px; border: none; border-radius: 6px; background: rgba(255, 255, 255, 0.9); font-size: 12px;">
                        <option value="all">全部状态</option>
                        <option value="排表参战">排表参战</option>
                        <option value="新成员/替补">新成员/替补</option>
                        <option value="未参战">未参战</option>
                    </select>

                    <select class="sort-by" data-battle-id="{{ record.battle_id }}" style="padding: 8px; border: none; border-radius: 6px; background: rgba(255, 255, 255, 0.9); font-size: 12px;">
                        <option value="score">按评分排序</option>
                        <option value="kills">按击杀排序</option>
                        <option value="demolitions">按拆迁排序</option>
                        <option value="name">按姓名排序</option>
                    </select>
                </div>

                <!-- 成员表现表格 -->
                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse; font-size: 11px;">
                        <thead>
                            <tr style="background: #f8f9fa; border-bottom: 1px solid #dee2e6;">
                                <th style="padding: 4px; text-align: left; font-weight: bold; color: #495057; font-size: 10px;">成员</th>
                                <th style="padding: 4px; text-align: center; font-weight: bold; color: #495057; font-size: 10px;">状态</th>
                                <th style="padding: 4px; text-align: center; font-weight: bold; color: #495057; font-size: 10px;">评级</th>
                                <th style="padding: 4px; text-align: center; font-weight: bold; color: #495057; font-size: 10px;">评分</th>
                                <th style="padding: 4px; text-align: center; font-weight: bold; color: #495057; font-size: 10px;">评分详情</th>
                                <th style="padding: 4px; text-align: center; font-weight: bold; color: #495057; font-size: 10px;">击杀</th>
                                <th style="padding: 4px; text-align: center; font-weight: bold; color: #495057; font-size: 10px;">助攻</th>
                                <th style="padding: 4px; text-align: center; font-weight: bold; color: #495057; font-size: 10px;">资源</th>
                                <th style="padding: 4px; text-align: center; font-weight: bold; color: #495057; font-size: 10px;">人伤</th>
                                <th style="padding: 4px; text-align: center; font-weight: bold; color: #495057; font-size: 10px;">塔伤(拆迁)</th>
                                <th style="padding: 4px; text-align: center; font-weight: bold; color: #495057; font-size: 10px;">治疗</th>
                                <th style="padding: 4px; text-align: center; font-weight: bold; color: #495057; font-size: 10px;">承伤</th>
                                <th style="padding: 4px; text-align: center; font-weight: bold; color: #495057; font-size: 10px;">重伤</th>
                                <th style="padding: 4px; text-align: center; font-weight: bold; color: #495057; font-size: 10px;">化羽/清泉</th>
                                <th style="padding: 4px; text-align: center; font-weight: bold; color: #495057; font-size: 10px;">焚骨</th>
                            </tr>
                        </thead>
                        <tbody class="battle-table-body" data-battle-id="{{ record.battle_id }}">
                            {% for member_name, performance in record.member_performance.items() %}
                            <tr class="battle-row"
                                data-battle-id="{{ record.battle_id }}"
                                data-name="{{ member_name }}"
                                data-profession="{{ performance.get('profession', '未知') }}"
                                data-team="{{ performance.get('team', '未知') }}"
                                data-team-type="{{ performance.get('team_type', '未知') }}"
                                data-status="{{ performance.get('status', '未知') }}"
                                data-rating="{{ performance.get('rating', '未知') }}"
                                data-score="{{ performance.get('score', 0) }}"
                                {% if performance.get('battle_data') %}
                                data-kills="{{ performance.battle_data.get('kills', 0) }}"
                                data-assists="{{ performance.battle_data.get('assists', 0) }}"
                                data-resources="{{ performance.battle_data.get('resources', 0) }}"
                                data-player-damage="{{ performance.battle_data.get('player_damage', 0) }}"
                                data-building-damage="{{ performance.battle_data.get('building_damage', 0) }}"
                                data-healing="{{ performance.battle_data.get('healing', 0) }}"
                                data-damage-taken="{{ performance.battle_data.get('damage_taken', 0) }}"
                                data-heavy-injuries="{{ performance.battle_data.get('heavy_injuries', 0) }}"
                                data-resurrections="{{ performance.battle_data.get('resurrections', 0) }}"
                                data-demolitions="{{ performance.battle_data.get('demolitions', 0) }}"
                                {% else %}
                                data-kills="0"
                                data-assists="0"
                                data-resources="0"
                                data-player-damage="0"
                                data-building-damage="0"
                                data-healing="0"
                                data-damage-taken="0"
                                data-heavy-injuries="0"
                                data-resurrections="0"
                                data-demolitions="0"
                                {% endif %}
                                style="border-bottom: 1px solid #dee2e6;">

                                <td style="padding: 6px;">
                                    <div style="font-weight: bold; color: #2c3e50; font-size: 12px;">
                                        {{ member_name }}
                                        {% if not performance.get('is_roster_member', True) %}
                                        <span style="color: #ff6b6b; font-size: 10px;">🆕替补</span>
                                        {% endif %}
                                    </div>
                                    <div style="font-size: 10px; color: #6c757d;">
                                        <span class="profession-badge profession-{{ performance.get('profession', '未知') }}" style="font-size: 9px; padding: 1px 3px;">{{ performance.get('profession', '未知') }}</span>
                                        {% if performance.get('is_roster_member', True) and performance.get('team') %}
                                        <span style="background: #667eea; color: white; padding: 1px 3px; border-radius: 3px; font-size: 9px;">{{ performance.get('team', '') }}</span>
                                        {% endif %}
                                        {% if performance.get('team_type') %}
                                        <span style="background: {% if performance.get('team_type') == '进攻团' %}#dc3545{% else %}#28a745{% endif %}; color: white; padding: 1px 3px; border-radius: 3px; font-size: 9px;">
                                            {% if performance.get('team_type') == '进攻团' %}🗡️{% else %}🛡️{% endif %}
                                        </span>
                                        {% endif %}
                                    </div>
                                </td>

                                <td style="padding: 6px; text-align: center;">
                                    {% if performance.get('status') == '排表参战' %}
                                    <span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 6px; font-size: 10px;">✅</span>
                                    {% elif performance.get('status') == '新成员/替补' %}
                                    <span style="background: #ffc107; color: white; padding: 2px 6px; border-radius: 6px; font-size: 10px;">🆕</span>
                                    {% else %}
                                    <span style="background: #6c757d; color: white; padding: 2px 6px; border-radius: 6px; font-size: 10px;">❌</span>
                                    {% endif %}
                                </td>

                                <td style="padding: 6px; text-align: center;">
                                    {% if performance.get('rating') == 'S+' %}
                                    <span style="background: linear-gradient(45deg, #ff6b6b, #feca57); color: white; padding: 2px 6px; border-radius: 8px; font-weight: bold; font-size: 10px;">{{ performance.get('rating', '-') }}</span>
                                    {% elif performance.get('rating') == 'S' %}
                                    <span style="background: #ff6b6b; color: white; padding: 2px 6px; border-radius: 8px; font-weight: bold; font-size: 10px;">{{ performance.get('rating', '-') }}</span>
                                    {% elif performance.get('rating') == 'A' %}
                                    <span style="background: #48dbfb; color: white; padding: 2px 6px; border-radius: 8px; font-weight: bold; font-size: 10px;">{{ performance.get('rating', '-') }}</span>
                                    {% elif performance.get('rating') == 'B' %}
                                    <span style="background: #0abde3; color: white; padding: 2px 6px; border-radius: 8px; font-weight: bold; font-size: 10px;">{{ performance.get('rating', '-') }}</span>
                                    {% elif performance.get('rating') == 'C' %}
                                    <span style="background: #feca57; color: white; padding: 2px 6px; border-radius: 8px; font-weight: bold; font-size: 10px;">{{ performance.get('rating', '-') }}</span>
                                    {% elif performance.get('rating') == 'D' %}
                                    <span style="background: #ff9ff3; color: white; padding: 2px 6px; border-radius: 8px; font-weight: bold; font-size: 10px;">{{ performance.get('rating', '-') }}</span>
                                    {% else %}
                                    <span style="background: #a4b0be; color: white; padding: 2px 6px; border-radius: 8px; font-size: 10px;">{{ performance.get('rating', '-') }}</span>
                                    {% endif %}
                                </td>

                                <td style="padding: 6px; text-align: center; font-weight: bold; color: #667eea; font-size: 11px;">
                                    {{ "%.1f"|format(performance.get('score', 0)) }}
                                </td>

                                <td style="padding: 4px; text-align: left; font-size: 9px; max-width: 200px;">
                                    {% if performance.get('battle_data') %}
                                    <!-- 职责信息 -->
                                    {% if performance.get('position') %}
                                    <div style="margin-bottom: 3px;">
                                        <span style="background: #667eea; color: white; padding: 1px 4px; border-radius: 3px; font-size: 8px;">
                                            {{ performance.get('position', '拆塔') }}职责
                                        </span>
                                    </div>
                                    {% endif %}

                                    <!-- 加分项 -->
                                    {% if performance.get('bonus_items') %}
                                    {% for bonus in performance.get('bonus_items', []) %}
                                    <div style="color: #28a745; margin: 1px 0; font-size: 8px;">
                                        ✅ {{ bonus }}
                                    </div>
                                    {% endfor %}
                                    {% endif %}

                                    <!-- 扣分项 -->
                                    {% if performance.get('penalty_items') %}
                                    {% for penalty in performance.get('penalty_items', []) %}
                                    <div style="color: #dc3545; margin: 1px 0; font-size: 8px;">
                                        ❌ {{ penalty }}
                                    </div>
                                    {% endfor %}
                                    {% endif %}

                                    <!-- 如果没有详细信息 -->
                                    {% if not performance.get('bonus_items') and not performance.get('penalty_items') %}
                                    <div style="color: #6c757d; font-size: 8px;">基础评分</div>
                                    {% endif %}
                                    {% else %}
                                    <div style="color: #6c757d; font-size: 8px;">未参战</div>
                                    {% endif %}
                                </td>

                                {% if performance.get('battle_data') %}
                                <td style="padding: 4px; text-align: center; font-size: 10px;">{{ performance.battle_data.get('kills', 0) }}</td>
                                <td style="padding: 4px; text-align: center; font-size: 10px;">{{ performance.battle_data.get('assists', 0) }}</td>
                                <td style="padding: 4px; text-align: center; font-size: 10px;">{{ performance.battle_data.get('resources', 0) }}</td>
                                <td style="padding: 4px; text-align: center; font-size: 10px;">{{ "%.1f万"|format(performance.battle_data.get('player_damage', 0) / 10000) }}</td>
                                <td style="padding: 4px; text-align: center; font-size: 10px; font-weight: bold; color: #e17055;">{{ "%.1f万"|format(performance.battle_data.get('building_damage', 0) / 10000) }}</td>
                                <td style="padding: 4px; text-align: center; font-size: 10px;">{{ "%.1f万"|format(performance.battle_data.get('healing', 0) / 10000) }}</td>
                                <td style="padding: 4px; text-align: center; font-size: 10px;">{{ "%.1f万"|format(performance.battle_data.get('damage_taken', 0) / 10000) }}</td>
                                <td style="padding: 4px; text-align: center; font-size: 10px; color: {% if performance.battle_data.get('heavy_injuries', 0) > 10 %}#dc3545{% elif performance.battle_data.get('heavy_injuries', 0) > 5 %}#ffc107{% else %}#28a745{% endif %};">
                                    {{ performance.battle_data.get('heavy_injuries', 0) }}
                                </td>
                                <td style="padding: 4px; text-align: center; font-size: 10px; font-weight: bold; color: #28a745;">{{ performance.battle_data.get('resurrections', 0) }}</td>
                                <td style="padding: 4px; text-align: center; font-size: 10px; font-weight: bold; color: #dc3545;">{{ performance.battle_data.get('demolitions', 0) }}</td>
                                {% else %}
                                <td style="padding: 4px; text-align: center; color: #6c757d; font-size: 10px;">-</td>
                                <td style="padding: 4px; text-align: center; color: #6c757d; font-size: 10px;">-</td>
                                <td style="padding: 4px; text-align: center; color: #6c757d; font-size: 10px;">-</td>
                                <td style="padding: 4px; text-align: center; color: #6c757d; font-size: 10px;">-</td>
                                <td style="padding: 4px; text-align: center; color: #6c757d; font-size: 10px;">-</td>
                                <td style="padding: 4px; text-align: center; color: #6c757d; font-size: 10px;">-</td>
                                <td style="padding: 4px; text-align: center; color: #6c757d; font-size: 10px;">-</td>
                                <td style="padding: 4px; text-align: center; color: #6c757d; font-size: 10px;">-</td>
                                <td style="padding: 4px; text-align: center; color: #6c757d; font-size: 10px;">-</td>
                                <td style="padding: 4px; text-align: center; color: #6c757d; font-size: 10px;">-</td>
                                <td style="padding: 4px; text-align: center; color: #6c757d; font-size: 10px;">-</td>
                                {% endif %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="stat-card" style="margin-bottom: 20px;">
    <div style="text-align: center; padding: 40px; color: #6c757d;">
        <div style="font-size: 48px; margin-bottom: 15px;">📊</div>
        <h4 style="margin-bottom: 10px;">暂无战斗数据</h4>
        <p>请上传战斗数据CSV文件开始分析</p>
    </div>
</div>
{% endif %}


{% endblock %}

{% block scripts %}
<script>
// 上传功能
const toggleUpload = document.getElementById('toggleUpload');
const uploadSection = document.getElementById('uploadSection');
const uploadForm = document.getElementById('uploadForm');
const battleFile = document.getElementById('battleFile');
const uploadBtn = document.getElementById('uploadBtn');
const uploadStatus = document.getElementById('uploadStatus');

// 切换上传区域显示
toggleUpload.addEventListener('click', function() {
    if (uploadSection.style.display === 'none') {
        uploadSection.style.display = 'block';
        toggleUpload.textContent = '隐藏上传';
    } else {
        uploadSection.style.display = 'none';
        toggleUpload.textContent = '显示上传';
    }
});

// 文件选择处理
battleFile.addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        if (file.name.endsWith('.csv')) {
            uploadBtn.disabled = false;
            uploadBtn.style.background = '#28a745';
            uploadBtn.style.cursor = 'pointer';
        } else {
            alert('请选择CSV文件！');
            battleFile.value = '';
            uploadBtn.disabled = true;
            uploadBtn.style.background = '#6c757d';
            uploadBtn.style.cursor = 'not-allowed';
        }
    }
});

// 表单提交
uploadForm.addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData();
    formData.append('battle_file', battleFile.files[0]);

    // 显示上传状态
    uploadStatus.style.display = 'block';
    uploadStatus.style.background = '#e3f2fd';
    uploadStatus.style.color = '#1976d2';
    uploadStatus.textContent = '正在上传和分析...';
    uploadBtn.disabled = true;
    uploadBtn.textContent = '上传中...';

    // 发送请求
    fetch('/upload_battle_data', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            uploadStatus.style.background = '#d4edda';
            uploadStatus.style.color = '#155724';
            uploadStatus.textContent = '✅ ' + data.message + ' - 正在刷新页面...';

            // 延迟刷新页面
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            uploadStatus.style.background = '#f8d7da';
            uploadStatus.style.color = '#721c24';
            uploadStatus.textContent = '❌ ' + data.error;
            uploadBtn.disabled = false;
            uploadBtn.textContent = '🚀 上传分析';
        }
    })
    .catch(error => {
        uploadStatus.style.background = '#f8d7da';
        uploadStatus.style.color = '#721c24';
        uploadStatus.textContent = '❌ 网络错误，请重试';
        uploadBtn.disabled = false;
        uploadBtn.textContent = '🚀 上传分析';
    });
});

// 战斗详情展开/收起
function toggleBattleDetail(battleId) {
    const detailDiv = document.getElementById('detail_' + battleId);
    const button = event.target;

    if (detailDiv.style.display === 'none') {
        detailDiv.style.display = 'block';
        button.textContent = '隐藏详情';
    } else {
        detailDiv.style.display = 'none';
        button.textContent = '查看详情';
    }
}

// 删除战斗记录
function deleteBattleRecord(battleId, battleName) {
    // 第一次确认
    if (!confirm(`确定要删除战斗记录 "${battleName}" 吗？\n\n此操作将：\n- 删除战斗分析数据\n- 删除对应的CSV文件\n- 无法恢复`)) {
        return;
    }

    // 第二次确认
    if (!confirm(`⚠️ 最后确认 ⚠️\n\n真的要删除 "${battleName}" 的战斗记录吗？\n\n这个操作不可撤销！`)) {
        return;
    }

    // 显示删除状态
    const button = event.target;
    const originalText = button.textContent;
    button.disabled = true;
    button.textContent = '删除中...';
    button.style.background = '#6c757d';

    // 发送删除请求
    fetch('/delete_battle_record', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            battle_id: battleId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 删除成功，刷新页面
            alert('✅ ' + data.message);
            window.location.reload();
        } else {
            // 删除失败
            alert('❌ 删除失败: ' + data.error);
            button.disabled = false;
            button.textContent = originalText;
            button.style.background = '#dc3545';
        }
    })
    .catch(error => {
        alert('❌ 网络错误: ' + error.message);
        button.disabled = false;
        button.textContent = originalText;
        button.style.background = '#dc3545';
    });
}

// 为每个战斗记录的筛选器添加事件监听
document.addEventListener('DOMContentLoaded', function() {
    // 团队类型筛选
    document.querySelectorAll('.team-type-filter').forEach(filter => {
        filter.addEventListener('change', function() {
            filterBattleRows(this.dataset.battleId);
        });
    });

    // 状态筛选
    document.querySelectorAll('.status-filter').forEach(filter => {
        filter.addEventListener('change', function() {
            filterBattleRows(this.dataset.battleId);
        });
    });

    // 排序
    document.querySelectorAll('.sort-by').forEach(filter => {
        filter.addEventListener('change', function() {
            filterBattleRows(this.dataset.battleId);
        });
    });
});

// 筛选特定战斗的行
function filterBattleRows(battleId) {
    const teamTypeFilter = document.querySelector(`.team-type-filter[data-battle-id="${battleId}"]`);
    const statusFilter = document.querySelector(`.status-filter[data-battle-id="${battleId}"]`);
    const sortBy = document.querySelector(`.sort-by[data-battle-id="${battleId}"]`);

    const selectedTeamType = teamTypeFilter.value;
    const selectedStatus = statusFilter.value;
    const sortCriteria = sortBy.value;

    // 获取该战斗的所有行
    const battleRows = document.querySelectorAll(`.battle-row[data-battle-id="${battleId}"]`);
    let rows = Array.from(battleRows);

    // 筛选
    rows = rows.filter(row => {
        const teamType = row.dataset.teamType;
        const status = row.dataset.status;

        let matchesTeamType = true;
        if (selectedTeamType !== 'all') {
            if (selectedTeamType === '新成员/替补') {
                matchesTeamType = status === '新成员/替补';
            } else if (selectedTeamType === '缺席') {
                matchesTeamType = status === '未参战';
            } else if (selectedTeamType === '替补缺席') {
                matchesTeamType = status === '替补缺席';
            } else {
                // 进攻团或防守团
                matchesTeamType = teamType === selectedTeamType;
            }
        }

        const matchesStatus = selectedStatus === 'all' || status === selectedStatus;

        return matchesTeamType && matchesStatus;
    });

    // 排序
    rows.sort((a, b) => {
        let aValue, bValue;

        switch(sortCriteria) {
            case 'score':
                aValue = parseFloat(a.dataset.score);
                bValue = parseFloat(b.dataset.score);
                return bValue - aValue; // 降序
            case 'kills':
                aValue = parseInt(a.dataset.kills);
                bValue = parseInt(b.dataset.kills);
                return bValue - aValue; // 降序
            case 'demolitions':
                aValue = parseInt(a.dataset.demolitions);
                bValue = parseInt(b.dataset.demolitions);
                return bValue - aValue; // 降序
            case 'name':
                aValue = a.dataset.name;
                bValue = b.dataset.name;
                return aValue.localeCompare(bValue); // 升序
            default:
                return 0;
        }
    });

    // 隐藏所有行
    battleRows.forEach(row => row.style.display = 'none');

    // 显示筛选后的行
    const tbody = document.querySelector(`.battle-table-body[data-battle-id="${battleId}"]`);
    rows.forEach(row => {
        row.style.display = '';
        tbody.appendChild(row); // 重新排序
    });
}


</script>
{% endblock %}
