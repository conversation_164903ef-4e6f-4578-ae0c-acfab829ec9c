#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
纸落云烟帮会管理系统 - API版本
提供RESTful API接口，支持前后端分离
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import os
from datetime import datetime
import hashlib

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 配置
app.config['SECRET_KEY'] = 'guild_secret_key_2024'

# 数据文件路径
MEMBERS_FILE = 'data/members.json'
BATTLE_RECORDS_FILE = 'data/battle_records.json'
AUTH_FILE = 'data/auth.json'

# 确保数据目录存在
os.makedirs('data', exist_ok=True)

# ==================== 认证相关 ====================

def load_auth_config():
    """加载认证配置"""
    if os.path.exists(AUTH_FILE):
        with open(AUTH_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    else:
        # 默认认证配置
        default_auth = {
            "auth_type": "simple_password",  # simple_password, invite_code, preset_accounts
            "passwords": {
                "admin": "admin123",      # 管理员密码
                "officer": "officer456",  # 干部密码  
                "member": "member123"     # 成员密码
            },
            "invite_codes": {},
            "preset_accounts": {}
        }
        save_auth_config(default_auth)
        return default_auth

def save_auth_config(auth_config):
    """保存认证配置"""
    with open(AUTH_FILE, 'w', encoding='utf-8') as f:
        json.dump(auth_config, f, ensure_ascii=False, indent=2)

def generate_token(user_info):
    """生成简单的token"""
    timestamp = str(int(datetime.now().timestamp()))
    data = f"{user_info['role']}_{user_info['name']}_{timestamp}"
    return hashlib.md5(data.encode()).hexdigest()

@app.route('/api/auth/login', methods=['POST'])
def login():
    """登录接口"""
    try:
        data = request.get_json()
        auth_type = data.get('auth_type', 'simple_password')
        
        auth_config = load_auth_config()
        
        if auth_type == 'simple_password':
            password = data.get('password')
            name = data.get('name', '匿名用户')
            
            # 检查密码对应的角色
            role = None
            for r, p in auth_config['passwords'].items():
                if password == p:
                    role = r
                    break
            
            if role:
                user_info = {
                    'name': name,
                    'role': role,
                    'login_time': datetime.now().isoformat()
                }
                token = generate_token(user_info)
                
                return jsonify({
                    'success': True,
                    'token': token,
                    'user': user_info,
                    'message': f'欢迎，{name}！'
                })
            else:
                return jsonify({'success': False, 'error': '密码错误'})
        
        elif auth_type == 'invite_code':
            invite_code = data.get('invite_code')
            
            if invite_code in auth_config['invite_codes']:
                code_info = auth_config['invite_codes'][invite_code]
                if not code_info.get('used', False):
                    # 标记邀请码为已使用
                    auth_config['invite_codes'][invite_code]['used'] = True
                    save_auth_config(auth_config)
                    
                    user_info = {
                        'name': code_info['name'],
                        'role': code_info['role'],
                        'login_time': datetime.now().isoformat()
                    }
                    token = generate_token(user_info)
                    
                    return jsonify({
                        'success': True,
                        'token': token,
                        'user': user_info,
                        'message': f'欢迎，{code_info["name"]}！'
                    })
                else:
                    return jsonify({'success': False, 'error': '邀请码已被使用'})
            else:
                return jsonify({'success': False, 'error': '无效的邀请码'})
        
        return jsonify({'success': False, 'error': '不支持的认证方式'})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/auth/config', methods=['GET'])
def get_auth_config():
    """获取认证配置（不包含敏感信息）"""
    auth_config = load_auth_config()
    return jsonify({
        'auth_type': auth_config['auth_type'],
        'available_roles': list(auth_config['passwords'].keys()) if auth_config['auth_type'] == 'simple_password' else []
    })

# ==================== 成员管理API ====================

def load_members():
    """加载成员数据"""
    if os.path.exists(MEMBERS_FILE):
        with open(MEMBERS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    return []

def save_members(members):
    """保存成员数据"""
    with open(MEMBERS_FILE, 'w', encoding='utf-8') as f:
        json.dump(members, f, ensure_ascii=False, indent=2)

@app.route('/api/members', methods=['GET'])
def get_members():
    """获取成员列表"""
    try:
        members = load_members()
        return jsonify({'success': True, 'data': members})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/members', methods=['POST'])
def add_member():
    """添加成员"""
    try:
        data = request.get_json()
        members = load_members()
        
        # 检查成员是否已存在
        if any(m['name'] == data['name'] for m in members):
            return jsonify({'success': False, 'error': '成员已存在'})
        
        # 添加新成员
        new_member = {
            'name': data['name'],
            'profession': data['profession'],
            'position': data.get('position', '拆塔'),
            'main_group': data.get('main_group', '其他团'),
            'sub_team': data.get('sub_team', '替补'),
            'squad': data.get('squad', '替补'),
            'status': data.get('status', '主力'),
            'created_at': datetime.now().isoformat()
        }
        
        members.append(new_member)
        save_members(members)
        
        return jsonify({'success': True, 'data': new_member, 'message': '成员添加成功'})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/members/<member_name>', methods=['PUT'])
def update_member(member_name):
    """更新成员信息"""
    try:
        data = request.get_json()
        members = load_members()
        
        # 查找成员
        member_index = None
        for i, member in enumerate(members):
            if member['name'] == member_name:
                member_index = i
                break
        
        if member_index is None:
            return jsonify({'success': False, 'error': '成员不存在'})
        
        # 更新成员信息
        members[member_index].update(data)
        members[member_index]['updated_at'] = datetime.now().isoformat()
        
        save_members(members)
        
        return jsonify({'success': True, 'data': members[member_index], 'message': '成员信息更新成功'})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/members/<member_name>', methods=['DELETE'])
def delete_member(member_name):
    """删除成员"""
    try:
        members = load_members()
        
        # 过滤掉要删除的成员
        original_count = len(members)
        members = [m for m in members if m['name'] != member_name]
        
        if len(members) == original_count:
            return jsonify({'success': False, 'error': '成员不存在'})
        
        save_members(members)
        
        return jsonify({'success': True, 'message': '成员删除成功'})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# ==================== 统计API ====================

@app.route('/api/stats/overview', methods=['GET'])
def get_overview_stats():
    """获取总览统计"""
    try:
        members = load_members()
        
        # 分别统计帮会成员和帮外成员
        guild_members = [m for m in members if not (m.get('main_group') == '其他团' and (m.get('sub_team') == '帮外' or m.get('squad') == '帮外'))]
        external_members = [m for m in members if m.get('main_group') == '其他团' and (m.get('sub_team') == '帮外' or m.get('squad') == '帮外')]
        
        # 统计各团人数
        attack_count = len([m for m in members if m.get('main_group') == '进攻团'])
        defense_count = len([m for m in members if m.get('main_group') == '防守团'])
        other_count = len([m for m in guild_members if m.get('main_group') == '其他团'])
        
        # 职业统计
        profession_stats = {}
        for member in guild_members:
            prof = member.get('profession', '未知')
            profession_stats[prof] = profession_stats.get(prof, 0) + 1
        
        stats = {
            'total_members': len(guild_members),
            'external_members': len(external_members),
            'attack_members': attack_count,
            'defense_members': defense_count,
            'other_members': other_count,
            'profession_stats': profession_stats,
            'active_members': len([m for m in guild_members if m.get('status', '主力') == '主力']),
            'substitute_members': len([m for m in guild_members if m.get('status', '主力') == '替补'])
        }
        
        return jsonify({'success': True, 'data': stats})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# ==================== 启动配置 ====================

if __name__ == '__main__':
    print("=" * 60)
    print("🚀 纸落云烟帮会管理系统 API服务启动中...")
    print("=" * 60)
    print("📊 API功能包括:")
    print("   - 认证API (/api/auth/*)")
    print("   - 成员管理API (/api/members/*)")
    print("   - 统计API (/api/stats/*)")
    print()
    print("🌐 API地址: http://localhost:5002")
    print("=" * 60)
    
    # 生产环境配置
    import os
    debug_mode = os.environ.get('FLASK_ENV') == 'development'
    port = int(os.environ.get('PORT', 5002))
    host = os.environ.get('HOST', '0.0.0.0')
    
    app.run(debug=debug_mode, host=host, port=port)
