@echo off
echo ============================================================
echo 纸落云烟帮会管理系统启动脚本
echo ============================================================

echo 检查Python环境...
python --version
if errorlevel 1 (
    echo 错误: Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo.
echo 安装Flask依赖...
pip install flask
if errorlevel 1 (
    echo 警告: Flask安装可能失败，尝试继续运行...
)

echo.
echo 检查数据文件...
if not exist "data\guild_members.json" (
    echo 错误: 数据文件不存在，请确保 data\guild_members.json 文件存在
    pause
    exit /b 1
)

echo.
echo ============================================================
echo 启动Web应用...
echo 访问地址: http://localhost:5000
echo 按 Ctrl+C 停止服务
echo ============================================================
echo.

python app.py

pause
