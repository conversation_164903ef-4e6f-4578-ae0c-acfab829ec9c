@echo off
chcp 65001 >nul

echo 🚀 启动纸落云烟帮会管理系统...
echo ============================================================

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 安装依赖
echo 📦 安装依赖包...
pip install -r requirements.txt

REM 创建必要的目录
if not exist logs mkdir logs
if not exist pids mkdir pids
if not exist data mkdir data
if not exist data\uploads mkdir data\uploads

echo 🌐 启动多个服务...
echo    - 管理后台: 端口 5888
echo    - API服务: 端口 5002
echo    - 成员访问: 端口 5000
echo.

REM 启动管理后台 (端口5888)
echo 🔧 启动管理后台...
start "管理后台" cmd /c "set PORT=5888 && set FLASK_ENV=production && python app.py > logs\backend.log 2>&1"

REM 等待2秒
timeout /t 2 /nobreak >nul

REM 启动API服务 (端口5002)
echo 🔌 启动API服务...
start "API服务" cmd /c "set FLASK_ENV=production && python api_app.py > logs\api.log 2>&1"

REM 等待2秒
timeout /t 2 /nobreak >nul

REM 启动成员访问服务 (端口5000)
echo 👥 启动成员访问服务...
start "成员访问" cmd /c "set MEMBER_PORT=5000 && set FLASK_ENV=production && python member_app.py > logs\member.log 2>&1"

REM 等待3秒让服务启动
echo ⏳ 等待服务启动...
timeout /t 3 /nobreak >nul

echo.
echo ✅ 所有服务启动完成！
echo ============================================================
echo 📱 访问地址:
echo    🔧 管理后台: http://localhost:5888 (管理员使用)
echo    🔌 API服务:  http://localhost:5002 (前端调用)
echo    👥 成员访问: http://localhost:5000 (成员查看)
echo.
echo 📋 服务管理:
echo    查看状态: status.bat
echo    停止服务: stop_all.bat
echo    查看日志: type logs\*.log
echo ============================================================
echo.
echo 🎉 系统启动完成！
echo 按任意键继续...
pause >nul
