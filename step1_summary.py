#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第一步总结：纸落云烟帮会成员信息汇总
"""

import json

def generate_summary():
    """生成第一步总结报告"""
    
    # 读取完整成员名单
    with open('complete_guild_members.json', 'r', encoding='utf-8') as f:
        members = json.load(f)
    
    print("=" * 80)
    print("第一步完成：纸落云烟帮会成员信息汇总")
    print("=" * 80)
    
    # 基本统计
    total_members = len(members)
    main_members = len([m for m in members if m['status'] == '主力'])
    battle_members = len([m for m in members if m['status'] == '战斗参与'])
    substitute_members = len([m for m in members if m['status'] == '替补'])
    
    print(f"📊 成员总数：{total_members}人")
    print(f"   - 主力成员：{main_members}人")
    print(f"   - 战斗参与：{battle_members}人")
    print(f"   - 替补成员：{substitute_members}人")
    print()
    
    # 职业分布统计
    profession_stats = {}
    for member in members:
        prof = member['profession'] if member['profession'] else '未知'
        if prof not in profession_stats:
            profession_stats[prof] = {'主力': 0, '战斗参与': 0, '替补': 0}
        profession_stats[prof][member['status']] += 1
    
    print("🎯 职业分布：")
    for profession, stats in sorted(profession_stats.items()):
        total = sum(stats.values())
        print(f"   {profession}：{total}人", end="")
        if stats['主力'] > 0:
            print(f" (主力{stats['主力']}", end="")
        if stats['战斗参与'] > 0:
            print(f", 战斗{stats['战斗参与']}", end="")
        if stats['替补'] > 0:
            print(f", 替补{stats['替补']}", end="")
        print(")")
    print()
    
    # 数据来源统计
    csv_members = len([m for m in members if m['source'] == 'CSV'])
    excel_members = len([m for m in members if m['source'] == 'Excel'])
    
    print("📁 数据来源：")
    print(f"   - CSV战斗数据：{csv_members}人")
    print(f"   - Excel管理表：{excel_members}人")
    print()
    
    # 详细成员列表
    print("👥 详细成员列表：")
    print("-" * 80)
    
    # 按职业分组显示
    for profession in sorted(profession_stats.keys()):
        if profession == '未知':
            continue
        prof_members = [m for m in members if m['profession'] == profession]
        print(f"\n【{profession}】({len(prof_members)}人)：")
        
        for member in sorted(prof_members, key=lambda x: x['name']):
            status_icon = {
                '主力': '⭐',
                '战斗参与': '🔥', 
                '替补': '🔄'
            }
            print(f"   {status_icon[member['status']]} {member['name']}")
    
    # 未知职业成员
    unknown_members = [m for m in members if not m['profession']]
    if unknown_members:
        print(f"\n【未知职业】({len(unknown_members)}人)：")
        for member in unknown_members:
            print(f"   🔄 {member['name']} (需要补充职业信息)")
    
    print("\n" + "=" * 80)
    print("✅ 第一步已完成！")
    print("📋 已生成文件：")
    print("   - guild_members.json (CSV提取的成员)")
    print("   - excel_members.json (Excel提取的成员)")
    print("   - complete_guild_members.json (完整成员名单)")
    print()
    print("🎯 下一步计划：")
    print("   1. 为替补成员补充职业信息")
    print("   2. 分配团队（1、2、3进攻团，防守团）")
    print("   3. 分配位置（输出、坦克、辅助、拆塔等）")
    print("   4. 构建Web管理界面")
    print("=" * 80)

def main():
    """主函数"""
    generate_summary()

if __name__ == "__main__":
    main()
