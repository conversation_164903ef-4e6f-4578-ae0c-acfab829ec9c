"""
简化的玩家名单生成器
生成可编辑的玩家名单CSV文件
"""

import sys
import os
import pandas as pd

# 添加路径
sys.path.append('src')

from data_processor import DataProcessor

def generate_simple_roster():
    """生成简化的玩家名单"""
    try:
        print("=" * 60)
        print("生成纸落云烟玩家名单")
        print("=" * 60)
        
        # 加载数据
        processor = DataProcessor("data", "纸落弈酒.xlsx")
        raw_data = processor.load_all_data()
        df = processor.create_unified_dataframe()
        
        print(f"📊 数据概况: {len(raw_data)}场战斗, {len(df)}条记录")
        
        # 按玩家分组统计
        player_stats = []
        
        for unique_id in df['unique_id'].unique():
            player_data = df[df['unique_id'] == unique_id]
            
            # 计算统计数据
            stats = {
                'unique_id': unique_id,
                'name': player_data['name'].iloc[0],
                'current_profession': player_data['profession'].iloc[0],
                'battles_count': len(player_data),
                'total_kills': int(player_data['kills'].sum()),
                'total_assists': int(player_data['assists'].sum()),
                'total_deaths': int(player_data['deaths'].sum()),
                'avg_kda': round(player_data['kda'].mean(), 2),
                'avg_player_damage': int(player_data['player_damage'].mean()),
                'avg_building_damage': int(player_data['building_damage'].mean()),
                'avg_healing': int(player_data['healing'].mean()),
                'avg_damage_taken': int(player_data['damage_taken'].mean()),
                'avg_heavy_injuries': round(player_data['heavy_injuries'].mean(), 1),
                'avg_resurrections': round(player_data['resurrections'].mean(), 1)
            }
            
            # 推测位置
            stats['inferred_position'] = infer_position(stats)
            
            # 计算简单评分
            stats['simple_score'] = calculate_simple_score(stats)
            
            player_stats.append(stats)
        
        # 按评分排序
        player_stats.sort(key=lambda x: x['simple_score'], reverse=True)
        
        # 创建DataFrame
        roster_df = pd.DataFrame(player_stats)
        
        # 添加编辑列
        roster_df['correct_profession'] = roster_df['current_profession']
        roster_df['main_position'] = roster_df['inferred_position']
        roster_df['backup_position'] = ''
        roster_df['notes'] = ''
        roster_df['rank'] = range(1, len(roster_df) + 1)
        
        # 重新排列列顺序
        columns_order = [
            'rank', 'name', 'current_profession', 'correct_profession',
            'main_position', 'backup_position', 'battles_count',
            'simple_score', 'avg_kda', 'total_kills', 'total_assists', 'total_deaths',
            'avg_player_damage', 'avg_building_damage', 'avg_healing', 'avg_damage_taken',
            'avg_heavy_injuries', 'avg_resurrections', 'notes'
        ]
        
        roster_df = roster_df[columns_order]
        
        # 保存为CSV
        roster_df.to_csv('player_roster.csv', index=False, encoding='utf-8-sig')
        
        print(f"\n📋 玩家名单生成完成！")
        print(f"   总玩家数: {len(roster_df)}")
        print(f"   文件保存: player_roster.csv")
        
        # 显示前10名
        print(f"\n🏆 评分前10名:")
        for i, player in roster_df.head(10).iterrows():
            print(f"  {player['rank']:2d}. {player['name']} ({player['current_profession']}) - 评分: {player['simple_score']:.1f} - 位置: {player['main_position']}")
        
        # 显示位置分布
        print(f"\n📊 位置分布:")
        position_dist = roster_df['main_position'].value_counts()
        for position, count in position_dist.items():
            print(f"  {position}: {count}人")
        
        # 显示职业分布
        print(f"\n👥 职业分布:")
        profession_dist = roster_df['current_profession'].value_counts()
        for profession, count in profession_dist.items():
            print(f"  {profession}: {count}人")
        
        print(f"\n💡 提示:")
        print(f"   1. 可以用Excel打开 player_roster.csv 编辑职业和位置")
        print(f"   2. 编辑 correct_profession 列确保职业正确")
        print(f"   3. 编辑 main_position 和 backup_position 列设置位置")
        print(f"   4. 在 notes 列添加备注")
        
        return roster_df
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def infer_position(stats):
    """推测玩家位置"""
    healing = stats['avg_healing']
    damage = stats['avg_player_damage']
    damage_taken = stats['avg_damage_taken']
    building_damage = stats['avg_building_damage']
    
    if healing > 30000000:  # 3千万治疗
        return "辅助位"
    elif damage_taken > 80000000:  # 8千万承伤
        return "坦克位"
    elif building_damage > damage * 0.6:  # 拆塔伤害占主要部分
        return "拆塔位"
    else:
        return "输出位"

def calculate_simple_score(stats):
    """计算简单评分"""
    kills_score = stats['total_kills'] * 10
    assists_score = stats['total_assists'] * 5
    damage_score = stats['avg_player_damage'] / 2000000  # 200万伤害为1分
    healing_score = stats['avg_healing'] / 3000000  # 300万治疗为1分
    building_score = stats['avg_building_damage'] / 8000000  # 800万拆塔为1分
    death_penalty = stats['total_deaths'] * -8
    kda_bonus = min(stats['avg_kda'] * 5, 50)
    
    total_score = (kills_score + assists_score + damage_score + healing_score + 
                  building_score + death_penalty + kda_bonus)
    
    return round(max(0, total_score), 1)

if __name__ == "__main__":
    generate_simple_roster()
