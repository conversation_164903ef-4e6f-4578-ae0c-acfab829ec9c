#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
纸落云烟帮会管理系统 - 成员只读访问
直接使用主系统的模板和功能，只提供只读访问
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for, make_response
import json
import os
from datetime import datetime

# 创建Flask应用
app = Flask(__name__)

# 设置只读模式标志
app.config['READONLY_MODE'] = True
app.jinja_env.globals['readonly_mode'] = True

# 职业颜色映射（从主系统复制）
def get_profession_color(profession):
    """获取职业对应的颜色"""
    color_map = {
        '素问': '#c62828',
        '铁衣': '#ef6c00',
        '潮光': '#0277bd',
        '九灵': '#7b1fa2',
        '龙吟': '#2e7d32',
        '血河': '#d32f2f',
        '碎梦': '#00695c',
        '玄机': '#f57f17',
        '神相': '#3f51b5',
        '沧澜': '#558b2f'
    }
    return color_map.get(profession, '#6c757d')

# 注册模板函数
app.jinja_env.globals.update(get_profession_color=get_profession_color)

# 数据文件路径
MEMBERS_FILE = 'data/guild_members.json'
BATTLE_RECORDS_FILE = 'data/battle_records.json'

def load_members():
    """加载成员数据"""
    try:
        with open(MEMBERS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return []

def load_battle_records():
    """加载战斗记录"""
    try:
        with open(BATTLE_RECORDS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return []

def get_organization_structure(members):
    """获取三层级组织结构（从主系统复制）"""
    structure = {
        '进攻团': {},
        '防守团': {},
        '其他团': {}
    }

    for member in members:
        # 解析成员的组织信息
        main_group = member.get('main_group', '其他团')  # 进攻团/防守团/其他团
        sub_team = member.get('sub_team', '一团')        # 一团/二团/三团
        squad = member.get('squad', '1队')              # 1队/2队/3队/4队/5队

        # 初始化结构
        if main_group not in structure:
            structure[main_group] = {}
        if sub_team not in structure[main_group]:
            structure[main_group][sub_team] = {
                'count': 0,
                'squads': {},
                'members': [],
                'professions': {},
                'status': {'主力': 0, '替补': 0, '请假': 0}
            }
        if squad not in structure[main_group][sub_team]['squads']:
            structure[main_group][sub_team]['squads'][squad] = {
                'count': 0,
                'members': []
            }

        # 添加成员到结构中
        structure[main_group][sub_team]['count'] += 1
        structure[main_group][sub_team]['members'].append(member)
        structure[main_group][sub_team]['squads'][squad]['count'] += 1
        structure[main_group][sub_team]['squads'][squad]['members'].append(member)

        # 统计职业
        prof = member['profession']
        structure[main_group][sub_team]['professions'][prof] = \
            structure[main_group][sub_team]['professions'].get(prof, 0) + 1

        # 统计状态
        status = member.get('status', '主力')
        structure[main_group][sub_team]['status'][status] = \
            structure[main_group][sub_team]['status'].get(status, 0) + 1

    return structure

def get_member_battle_history(member_name):
    """获取成员的战斗历史记录"""
    try:
        battle_records = load_battle_records()
        member_battles = []

        for record in battle_records:
            member_performance = record.get('member_performance', {})
            if member_name in member_performance:
                performance = member_performance[member_name]
                battle_info = {
                    'battle_id': record.get('battle_id', ''),
                    'battle_name': f"{record.get('our_guild', '纸落云烟')} VS {record.get('enemy_guild', '未知对手')}",
                    'our_guild': record.get('our_guild', ''),
                    'enemy_guild': record.get('enemy_guild', ''),
                    'upload_time': record.get('upload_time', ''),
                    'score': performance.get('final_score', 0),
                    'battle_data': performance.get('battle_data', {}),
                    'bonus_items': performance.get('bonus_items', []),
                    'penalty_items': performance.get('penalty_items', [])
                }
                member_battles.append(battle_info)

        # 按时间排序
        member_battles.sort(key=lambda x: x['upload_time'])
        return member_battles

    except Exception as e:
        print(f"获取成员战斗历史失败: {e}")
        return []

# ==================== 只读路由 ====================

@app.route('/')
def index():
    """主页 - 重定向到成员详情页面"""
    return redirect(url_for('members_list'))

@app.route('/members')
def members_list():
    """成员详情页面（只读）"""
    members = load_members()
    
    # 获取搜索和筛选参数
    search_query = request.args.get('search', '')
    profession_filter = request.args.get('profession', '')
    position_filter = request.args.get('position', '')
    team_filter = request.args.get('team', '')
    
    # 筛选成员
    filtered_members = members
    
    if search_query:
        filtered_members = [m for m in filtered_members if search_query.lower() in m['name'].lower()]
    
    if profession_filter:
        filtered_members = [m for m in filtered_members if m['profession'] == profession_filter]
    
    if position_filter:
        filtered_members = [m for m in filtered_members if m['position'] == position_filter]
    
    if team_filter:
        filtered_members = [m for m in filtered_members if m.get('main_group') == team_filter]
    
    # 获取筛选选项
    professions = sorted(list(set(m['profession'] for m in members)))
    positions = sorted(list(set(m['position'] for m in members)))
    teams = sorted(list(set(m.get('main_group', '其他团') for m in members)))
    
    # 使用主系统的只读模板
    return render_template('member_details_readonly.html',
                         members=filtered_members,
                         search_query=search_query,
                         profession_filter=profession_filter,
                         position_filter=position_filter,
                         team_filter=team_filter,
                         professions=professions,
                         positions=positions,
                         teams=teams)

@app.route('/member_detail/<member_name>')
def member_detail(member_name):
    """单个成员详情页面（只读）"""
    members = load_members()
    member = next((m for m in members if m['name'] == member_name), None)

    if not member:
        return redirect(url_for('members_list'))

    # 获取该成员的战斗历史
    battle_history = get_member_battle_history(member_name)

    # 使用主系统的成员详情模板
    return render_template('member_detail.html',
                         member=member,
                         battle_history=battle_history)

@app.route('/organization_chart')
def organization_chart():
    """组织架构图页面（只读）"""
    members = load_members()
    organization = get_organization_structure(members)

    # 使用主系统的只读组织架构模板
    return render_template('organization_readonly.html',
                         members=members,
                         organization=organization)

@app.route('/battle_analysis')
def battle_analysis():
    """战斗数据分析页面（只读）"""
    battle_records = load_battle_records()
    
    # 按时间倒序排列（最新的在前）
    battle_records.sort(key=lambda x: x.get('upload_time', ''), reverse=True)

    # 使用主系统的只读战斗分析模板
    return render_template('battle_analysis_readonly.html',
                         battle_records=battle_records)

@app.route('/api/battle/<battle_id>')
def get_battle_detail(battle_id):
    """获取战斗详情API（只读）"""
    battle_records = load_battle_records()

    battle = None
    for record in battle_records:
        if record.get('battle_id') == battle_id:
            battle = record
            break

    if not battle:
        return jsonify({'error': '战斗记录不存在'}), 404

    return jsonify(battle)

# ==================== 禁用的路由 ====================

@app.route('/api/members', methods=['POST'])
@app.route('/api/members/<member_name>', methods=['PUT', 'DELETE'])
@app.route('/upload_battle_data', methods=['POST'])
@app.route('/api/battle/<battle_id>', methods=['DELETE'])
@app.route('/drag_board')
def readonly_forbidden():
    """禁用所有编辑功能"""
    return jsonify({'error': '只读模式，无法进行编辑操作'}), 403

if __name__ == '__main__':
    print("=" * 60)
    print("👥 纸落云烟帮会管理系统 - 成员只读访问")
    print("=" * 60)
    print("📊 功能包括:")
    print("   - 成员详情 (只读查看)")
    print("   - 组织架构 (只读查看)")
    print("   - 战斗分析 (只读查看)")
    print("   - 与管理后台数据实时同步")
    print()
    print("🌐 访问地址: http://localhost:5001")
    print("=" * 60)

    # 运行在不同端口，避免与主系统冲突
    app.run(debug=False, host='0.0.0.0', port=5001)
