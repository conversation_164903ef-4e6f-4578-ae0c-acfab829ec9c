{% extends "base.html" %}

{% block title %}成员详情 - 纸落云烟{% endblock %}

{% block content %}
<style>
    .readonly-header {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .search-filters {
        background: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .filter-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 15px;
    }
    
    .filter-group {
        display: flex;
        flex-direction: column;
    }
    
    .filter-group label {
        font-weight: 500;
        margin-bottom: 5px;
        color: #333;
    }
    
    .filter-group input,
    .filter-group select {
        padding: 8px 12px;
        border: 2px solid #e9ecef;
        border-radius: 5px;
        font-size: 14px;
    }
    
    .filter-group input:focus,
    .filter-group select:focus {
        outline: none;
        border-color: #28a745;
    }
    
    .members-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }
    
    .member-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border-left: 5px solid #28a745;
    }
    
    .member-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .member-name {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        margin-bottom: 10px;
    }
    
    .member-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
        margin-bottom: 15px;
    }
    
    .info-item {
        display: flex;
        flex-direction: column;
    }
    
    .info-label {
        font-size: 12px;
        color: #666;
        margin-bottom: 2px;
    }
    
    .info-value {
        font-size: 14px;
        font-weight: 500;
        color: #333;
    }
    
    .profession-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: 500;
        color: white;
        margin-bottom: 10px;
    }
    
    .position-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 5px;
        font-size: 12px;
        background: #e9ecef;
        color: #495057;
    }
    
    .team-info {
        background: #f8f9fa;
        padding: 10px;
        border-radius: 5px;
        margin-top: 10px;
    }
    
    .team-path {
        font-size: 12px;
        color: #666;
    }
    
    .readonly-notice {
        background: #d4edda;
        color: #155724;
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 20px;
        text-align: center;
        border: 1px solid #c3e6cb;
    }
    
    .stats-summary {
        background: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        text-align: center;
    }
</style>

<div class="readonly-header">
    <h2><i class="fas fa-users"></i> 成员详情</h2>
    <p>查看公会成员信息 | 只读模式</p>
</div>

<div class="readonly-notice">
    <i class="fas fa-info-circle"></i> 当前为只读模式，仅可查看成员信息，无法进行编辑操作
</div>

<!-- 统计摘要 -->
<div class="stats-summary">
    <h4>当前显示：{{ members|length }} 名成员</h4>
</div>

<!-- 搜索和筛选 -->
<div class="search-filters">
    <form method="GET" action="/members">
        <div class="filter-row">
            <div class="filter-group">
                <label for="search">搜索成员</label>
                <input type="text" id="search" name="search" value="{{ search_query }}" 
                       placeholder="输入成员姓名...">
            </div>
            
            <div class="filter-group">
                <label for="profession">职业筛选</label>
                <select id="profession" name="profession">
                    <option value="">全部职业</option>
                    {% for prof in professions %}
                    <option value="{{ prof }}" {% if prof == profession_filter %}selected{% endif %}>
                        {{ prof }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="filter-group">
                <label for="position">职责筛选</label>
                <select id="position" name="position">
                    <option value="">全部职责</option>
                    {% for pos in positions %}
                    <option value="{{ pos }}" {% if pos == position_filter %}selected{% endif %}>
                        {{ pos }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="filter-group">
                <label for="team">团队筛选</label>
                <select id="team" name="team">
                    <option value="">全部团队</option>
                    {% for team in teams %}
                    <option value="{{ team }}" {% if team == team_filter %}selected{% endif %}>
                        {{ team }}
                    </option>
                    {% endfor %}
                </select>
            </div>
        </div>
        
        <div style="text-align: center;">
            <button type="submit" style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;">
                <i class="fas fa-search"></i> 搜索筛选
            </button>
            <a href="/members" style="margin-left: 10px; padding: 10px 20px; background: #6c757d; color: white; text-decoration: none; border-radius: 5px;">
                <i class="fas fa-undo"></i> 重置
            </a>
        </div>
    </form>
</div>

<!-- 成员列表 -->
<div class="members-grid">
    {% for member in members %}
    <div class="member-card">
        <div class="member-name">{{ member.name }}</div>
        
        <div class="profession-badge" style="background: {{ get_profession_color(member.profession) }};">
            {{ member.profession }}
        </div>
        
        <div class="member-info">
            <div class="info-item">
                <div class="info-label">职责</div>
                <div class="info-value">
                    <span class="position-badge">{{ member.position }}</span>
                </div>
            </div>
            
            <div class="info-item">
                <div class="info-label">状态</div>
                <div class="info-value">{{ member.get('status', '主力') }}</div>
            </div>
        </div>
        
        <div class="team-info">
            <div class="team-path">
                <strong>{{ member.get('main_group', '未分配') }}</strong>
                {% if member.get('sub_team') and member.get('sub_team') != '未分配' %}
                → {{ member.sub_team }}
                {% endif %}
                {% if member.get('squad') and member.get('squad') != '未分配' %}
                → {{ member.squad }}
                {% endif %}
            </div>
        </div>
    </div>
    {% endfor %}
</div>

{% if not members %}
<div style="text-align: center; padding: 50px; color: #666;">
    <i class="fas fa-search" style="font-size: 48px; margin-bottom: 20px;"></i>
    <h3>没有找到符合条件的成员</h3>
    <p>请尝试调整搜索条件或筛选器</p>
</div>
{% endif %}

<script>
// 职业颜色映射
function getProfessionColor(profession) {
    const colors = {
        '素问': '#ff69b4',
        '九灵': '#9370db', 
        '潮光': '#87ceeb',
        '血河': '#dc143c',
        '神相': '#4169e1',
        '玄机': '#ffd700',
        '铁衣': '#ff8c00',
        '龙吟': '#32cd32',
        '碎梦': '#2e8b57'
    };
    return colors[profession] || '#6c757d';
}

// 自动提交表单（实时搜索）
document.getElementById('search').addEventListener('input', function() {
    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(() => {
        this.form.submit();
    }, 500);
});

// 筛选器变化时自动提交
document.querySelectorAll('select').forEach(select => {
    select.addEventListener('change', function() {
        this.form.submit();
    });
});

// 只读模式提示
console.log('🛡️ 成员详情 - 只读模式');
</script>

{% endblock %}
