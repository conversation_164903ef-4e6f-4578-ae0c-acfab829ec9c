# 逆水寒手游数据分析系统

这是一个专门用于分析逆水寒手游战斗数据的Python项目，能够自动分析玩家数据并分辨强弱。

## 功能特点

- 📊 **自动数据处理**：智能读取CSV文件，处理编码问题
- 🎯 **综合评分系统**：基于多维度指标计算玩家战力评分
- 📈 **强弱分类**：自动将玩家分为8个强弱等级
- 🏆 **排行榜生成**：生成战力和KDA排行榜
- 👥 **职业分析**：分析各职业特点和角色定位
- 📋 **详细报告**：生成个人和整体分析报告
- 🎨 **数据可视化**：生成多种图表和交互式仪表板
- 🔍 **交互式查询**：支持实时查询玩家信息

## 项目结构

```
nishuihan_analyzer/
├── data/                   # 数据文件目录
│   ├── *.csv              # 战斗数据CSV文件
├── src/                   # 源代码目录
│   ├── data_processor.py  # 数据处理模块
│   ├── analyzer.py        # 数据分析模块
│   └── visualizer.py      # 数据可视化模块
├── output/                # 输出结果目录
├── config/                # 配置文件目录
├── main.py               # 主程序
├── requirements.txt      # 依赖包列表
└── README.md            # 说明文档
```

## 安装和使用

### 1. 环境准备

确保您的系统已安装Python 3.7或更高版本。

### 2. 安装依赖

```bash
cd nishuihan_analyzer
pip install -r requirements.txt
```

### 3. 准备数据

将逆水寒手游的战斗数据CSV文件放入 `data/` 目录中。

CSV文件格式应包含以下列：
- 玩家名字
- 职业
- 击败
- 助攻
- 资源
- 对玩家伤害
- 对建筑伤害
- 治疗值
- 承受伤害
- 重伤
- 化羽/清泉
- 焚骨

### 4. 运行分析

```bash
python main.py
```

## 分析指标说明

### 综合战力评分

系统会根据以下指标计算玩家的综合战力评分（0-100分）：

- **击败数** (权重20%)：直接击败敌方玩家的次数
- **助攻数** (权重15%)：协助击败敌方玩家的次数  
- **对玩家伤害** (权重25%)：对敌方玩家造成的伤害
- **对建筑伤害** (权重10%)：对敌方建筑造成的伤害
- **治疗量** (权重10%)：为己方提供的治疗
- **KDA** (权重15%)：(击败+助攻)/死亡次数
- **伤害效率** (权重5%)：每次死亡造成的平均伤害

### 强弱等级分类

根据综合战力评分，玩家被分为8个等级：

- **超强** (90-100分)：顶尖玩家
- **很强** (80-89分)：高水平玩家
- **强** (70-79分)：优秀玩家
- **中等偏强** (60-69分)：中上水平
- **中等** (50-59分)：平均水平
- **中等偏弱** (40-49分)：中下水平
- **弱** (30-39分)：较弱玩家
- **很弱** (0-29分)：新手玩家

### 职业角色分类

系统会根据数据特征自动识别职业角色：

- **治疗**：平均治疗量 > 5000万
- **输出**：平均对玩家伤害 > 5000万
- **坦克**：平均承受伤害 > 5000万
- **辅助**：其他情况

## 输出结果

运行完成后，`output/` 目录将包含：

### 图表文件
- `player_ranking.png` - 玩家排行榜
- `profession_analysis.png` - 职业分析图
- `strength_distribution.png` - 强弱分布图
- `correlation_heatmap.png` - 指标相关性热力图
- `interactive_dashboard.html` - 交互式仪表板

### 报告文件
- `analysis_summary.txt` - 总体分析报告
- `{玩家名}_report.png` - 个人详细报告（前5名）

## 交互式查询

程序运行完成后，可以进入交互式查询模式：

1. **查询玩家信息** - 查看特定玩家的详细数据
2. **查看职业排行** - 显示各职业的平均战力排名
3. **查找相似玩家** - 找到与指定玩家数据相似的其他玩家
4. **查看强弱分布** - 显示所有玩家的强弱等级分布

## 技术特点

- **智能编码处理**：自动检测和处理不同编码格式的CSV文件
- **数据标准化**：使用Z-score标准化确保不同指标的公平比较
- **机器学习**：使用scikit-learn进行数据分析和相似度计算
- **多维度可视化**：结合matplotlib、seaborn和plotly生成丰富的图表
- **模块化设计**：清晰的代码结构，易于维护和扩展

## 注意事项

1. 确保CSV文件格式正确，包含所有必需的列
2. 如果遇到编码问题，程序会自动尝试多种编码格式
3. 生成的图表支持中文显示，需要系统安装中文字体
4. 交互式仪表板需要在浏览器中打开HTML文件查看

## 扩展功能

系统设计为模块化架构，可以轻松添加新功能：

- 添加新的评分算法
- 增加更多可视化图表
- 支持更多数据格式
- 添加预测功能
- 集成数据库存储

## 联系方式

如有问题或建议，请联系开发者。
