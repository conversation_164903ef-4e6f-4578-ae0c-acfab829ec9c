"""
纸落云烟 - 每个人详细分析报告
显示所有60名玩家的完整分析数据
"""

import sys
import os

# 添加路径
sys.path.append('src')

from data_processor import DataProcessor
from analyzer import NishuihanPlayerAnalyzer

def generate_everyone_analysis():
    """生成每个人的详细分析报告"""
    try:
        print("=" * 80)
        print("纸落云烟 - 每个人详细分析报告")
        print("=" * 80)
        
        # 加载数据
        processor = DataProcessor("data", "纸落弈酒.xlsx")
        raw_data = processor.load_all_data()
        df = processor.create_unified_dataframe()
        
        # 分析数据
        analyzer = NishuihanPlayerAnalyzer(df)
        player_stats = analyzer.calculate_player_scores()
        time_series_analysis = analyzer.analyze_time_series()
        position_analysis = analyzer.analyze_position_suitability()
        
        print(f"📊 总共分析了 {len(player_stats)} 名玩家，{len(raw_data)} 场战斗")
        print(f"✅ 所有职业信息完整，0名职业不确定")
        
        # 位置名称映射
        position_names = {
            'damage_dealer': '输出位',
            'tank': '坦克位',
            'support': '辅助位',
            'demolisher': '拆塔位'
        }
        
        # 按战力排序显示每个人
        sorted_players = player_stats.sort_values('combat_score', ascending=False)
        
        for rank, (unique_id, player_data) in enumerate(sorted_players.iterrows(), 1):
            print("\n" + "=" * 80)
            print(f"第 {rank} 名: {player_data['name_first']} ({player_data['profession_first']})")
            print("=" * 80)
            
            # 基础信息
            print("📋 基础信息:")
            print(f"  玩家名字: {player_data['name_first']}")
            print(f"  职业: {player_data['profession_first']}")
            print(f"  参战次数: {int(player_data['battles_count'])}场")
            print(f"  战力评分: {player_data['combat_score']:.1f}")
            print(f"  强弱等级: {player_data['strength_level']}")
            print(f"  平均对手强度: {player_data['enemy_strength_mean']:.1f}")
            
            # 战斗统计
            print("\n⚔️ 战斗统计:")
            print(f"  击败: {int(player_data['kills_sum'])} (平均: {player_data['kills_mean']:.1f})")
            print(f"  助攻: {int(player_data['assists_sum'])} (平均: {player_data['assists_mean']:.1f})")
            print(f"  死亡: {int(player_data['deaths_sum'])} (平均: {player_data['deaths_mean']:.1f})")
            print(f"  KDA: {player_data['kda']:.2f}")
            print(f"  生存率: {player_data['survival_rate']*100:.1f}%")
            
            # 伤害数据
            print("\n💥 伤害数据:")
            print(f"  对玩家伤害: {int(player_data['player_damage_sum']):,} (平均: {int(player_data['player_damage_mean']):,})")
            print(f"  拆塔伤害: {int(player_data['building_damage_sum']):,} (平均: {int(player_data['building_damage_mean']):,})")
            print(f"  治疗量: {int(player_data['healing_sum']):,} (平均: {int(player_data['healing_mean']):,})")
            print(f"  承受伤害: {int(player_data['damage_taken_sum']):,} (平均: {int(player_data['damage_taken_mean']):,})")
            
            # 特殊数据
            print("\n🎯 特殊数据:")
            print(f"  重伤: {int(player_data['heavy_injuries_sum'])} (平均: {player_data['heavy_injuries_mean']:.1f})")
            print(f"  羽化/清泉: {int(player_data['resurrections_sum'])} (平均: {player_data['resurrections_mean']:.1f})")
            print(f"  资源: {int(player_data['resources_sum'])} (平均: {player_data['resources_mean']:.1f})")
            
            # 位置适合性分析
            if unique_id in position_analysis:
                pos_data = position_analysis[unique_id]
                print("\n🎯 位置适合性分析:")
                print(f"  最适合位置: {position_names.get(pos_data['best_position'], pos_data['best_position'])} (评分: {pos_data['best_score']:.1f})")
                
                print("  各位置评分:")
                for position, score in pos_data['position_scores'].items():
                    pos_name = position_names.get(position, position)
                    print(f"    {pos_name}: {score:.1f}分")
                
                if pos_data['specialties']:
                    print("  突出特长:")
                    for specialty, data in pos_data['specialties'].items():
                        print(f"    {specialty}: {data['value']:.1f} (排名第{data['rank']})")
                
                print("  建议:")
                for rec in pos_data['recommendations']:
                    print(f"    • {rec}")
            
            # 时间序列分析（如果有多场比赛）
            if unique_id in time_series_analysis:
                ts_data = time_series_analysis[unique_id]
                progress = ts_data['progress_assessment']
                print("\n📈 进步情况分析:")
                print(f"  进步等级: {progress['progress_level']}")
                print(f"  进步评分: {progress['progress_score']:+.2f}")
                print(f"  总结: {progress['summary']}")
                
                # 显示趋势
                if ts_data['trends']:
                    print("  各项指标趋势:")
                    for metric, trend in ts_data['trends'].items():
                        if trend['confidence'] in ['high', 'medium']:
                            print(f"    {metric}: {trend['direction']} (置信度: {trend['confidence']})")
            else:
                print("\n📈 进步情况分析:")
                print("  数据不足: 需要至少2场比赛才能分析趋势")
            
            # 分隔线
            if rank < len(sorted_players):
                print("\n" + "-" * 80)
        
        # 总结统计
        print("\n" + "=" * 80)
        print("📊 总结统计")
        print("=" * 80)
        
        # 职业分布
        profession_dist = player_stats['profession_first'].value_counts()
        print("\n职业分布:")
        for profession, count in profession_dist.items():
            percentage = (count / len(player_stats)) * 100
            print(f"  {profession}: {count}人 ({percentage:.1f}%)")
        
        # 强弱分布
        strength_dist = player_stats['strength_level'].value_counts()
        print("\n强弱分布:")
        for level, count in strength_dist.items():
            percentage = (count / len(player_stats)) * 100
            print(f"  {level}: {count}人 ({percentage:.1f}%)")
        
        # 各位置最佳人选
        print("\n各位置最佳人选:")
        for position, name in position_names.items():
            rankings = analyzer.get_position_rankings(position, 5)
            print(f"  {name}:")
            for i, player in enumerate(rankings, 1):
                best_mark = " ⭐" if player['is_best_position'] else ""
                print(f"    {i}. {player['name']} ({player['profession']}) - {player['score']:.1f}{best_mark}")
        
        print("\n" + "=" * 80)
        print("🎉 每个人详细分析报告生成完成！")
        print("💡 如需查看Web版本，请运行: python detailed_analysis_web.py")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"❌ 生成报告失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    generate_everyone_analysis()
