#!/bin/bash

# 停止所有服务

echo "🛑 停止纸落云烟帮会管理系统..."

# 检查PID文件是否存在
if [ -d "pids" ]; then
    # 停止管理后台
    if [ -f "pids/backend.pid" ]; then
        BACKEND_PID=$(cat pids/backend.pid)
        if ps -p $BACKEND_PID > /dev/null; then
            kill $BACKEND_PID
            echo "✅ 管理后台已停止 (PID: $BACKEND_PID)"
        else
            echo "⚠️ 管理后台进程不存在"
        fi
        rm -f pids/backend.pid
    fi
    
    # 停止API服务
    if [ -f "pids/api.pid" ]; then
        API_PID=$(cat pids/api.pid)
        if ps -p $API_PID > /dev/null; then
            kill $API_PID
            echo "✅ API服务已停止 (PID: $API_PID)"
        else
            echo "⚠️ API服务进程不存在"
        fi
        rm -f pids/api.pid
    fi
    
    # 停止成员访问服务
    if [ -f "pids/member.pid" ]; then
        MEMBER_PID=$(cat pids/member.pid)
        if ps -p $MEMBER_PID > /dev/null; then
            kill $MEMBER_PID
            echo "✅ 成员访问服务已停止 (PID: $MEMBER_PID)"
        else
            echo "⚠️ 成员访问服务进程不存在"
        fi
        rm -f pids/member.pid
    fi
else
    echo "⚠️ 未找到PID文件，尝试通过端口查找进程..."
    
    # 通过端口查找并停止进程
    pkill -f "python3 app.py"
    pkill -f "python3 api_app.py" 
    pkill -f "python3 member_app.py"
    
    echo "✅ 已尝试停止所有相关进程"
fi

echo ""
echo "🎉 所有服务已停止！"
