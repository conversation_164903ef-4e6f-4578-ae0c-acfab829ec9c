"""
综合分析工具
包含玩家名单、每场战斗分析和综合评分
"""

import sys
import os
import pandas as pd
import json
from datetime import datetime

# 添加路径
sys.path.append('src')

from data_processor import DataProcessor

def comprehensive_analysis():
    """综合分析所有数据"""
    try:
        print("=" * 80)
        print("纸落云烟综合数据分析")
        print("=" * 80)
        
        # 加载数据
        processor = DataProcessor("data", "纸落弈酒.xlsx")
        raw_data = processor.load_all_data()
        df = processor.create_unified_dataframe()
        
        print(f"📊 总数据: {len(raw_data)}场战斗, {len(df)}条记录")
        
        # 1. 生成完整玩家名单
        print(f"\n{'='*60}")
        print("1. 完整玩家名单")
        print(f"{'='*60}")
        
        player_roster = generate_player_roster(df)
        
        # 2. 分析每场战斗
        print(f"\n{'='*60}")
        print("2. 每场战斗分析")
        print(f"{'='*60}")
        
        battle_analysis = analyze_all_battles(df, raw_data)
        
        # 3. 综合评分计算
        print(f"\n{'='*60}")
        print("3. 综合评分计算")
        print(f"{'='*60}")
        
        final_scores = calculate_final_scores(df, battle_analysis)
        
        # 4. 保存所有结果
        comprehensive_results = {
            'player_roster': player_roster,
            'battle_analysis': battle_analysis,
            'final_scores': final_scores,
            'summary': {
                'total_players': len(player_roster),
                'total_battles': len(raw_data),
                'total_records': len(df),
                'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        }
        
        with open("comprehensive_analysis.json", 'w', encoding='utf-8') as f:
            json.dump(comprehensive_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n{'='*80}")
        print("📋 综合分析结果")
        print(f"{'='*80}")
        
        # 显示最终排名
        print("\n🏆 最终综合排名 (前15名):")
        sorted_players = sorted(final_scores, key=lambda x: x['final_score'], reverse=True)
        for i, player in enumerate(sorted_players[:15], 1):
            print(f"{i:2d}. {player['name']} ({player['profession']}) - 综合评分: {player['final_score']:.1f}")
            print(f"     最佳位置: {player['best_position']} | 参战: {player['battles_count']}场")
        
        print(f"\n✅ 综合分析完成！结果已保存到 comprehensive_analysis.json")
        return comprehensive_results
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def generate_player_roster(df):
    """生成完整玩家名单"""
    print("生成玩家名单...")
    
    roster = []
    unique_players = df.groupby('unique_id').agg({
        'name': 'first',
        'profession': 'first',
        'battles_count': 'count',
        'player_damage': 'mean',
        'healing': 'mean',
        'building_damage': 'mean',
        'damage_taken': 'mean',
        'kills': 'mean',
        'assists': 'mean',
        'deaths': 'mean'
    }).reset_index()
    
    for _, player in unique_players.iterrows():
        # 推测主要位置
        main_position = infer_main_position(player)
        
        roster_entry = {
            'unique_id': player['unique_id'],
            'name': player['name'],
            'current_profession': player['profession'],
            'correct_profession': player['profession'],  # 默认当前职业正确
            'main_position': main_position,
            'backup_position': '',
            'battles_count': int(player['battles_count']),
            'avg_damage': int(player['player_damage']),
            'avg_healing': int(player['healing']),
            'avg_building_damage': int(player['building_damage']),
            'notes': ''
        }
        
        roster.append(roster_entry)
        print(f"  ✅ {player['name']} ({player['profession']}) - 推测位置: {main_position}")
    
    return roster

def analyze_all_battles(df, raw_data):
    """分析所有战斗"""
    print("分析所有战斗...")
    
    battle_analysis = {}
    
    # 按日期分组分析
    for battle_date in df['battle_date'].unique():
        battle_df = df[df['battle_date'] == battle_date]
        
        # 找到对应的战斗信息
        battle_info = None
        for battle in raw_data:
            if battle['date'] in str(battle_date) or str(battle_date) in battle['date']:
                battle_info = battle
                break
        
        if not battle_info:
            continue
        
        battle_name = battle_info['filename'].replace('.csv', '')
        print(f"\n  📅 分析战斗: {battle_name}")
        print(f"     参战人数: {len(battle_df)}人")
        
        # 分析每个玩家在这场战斗中的表现
        players_performance = []
        
        for _, player in battle_df.iterrows():
            position = infer_battle_position(player)
            performance_score = calculate_battle_performance(player)
            
            performance = {
                'name': player['name'],
                'profession': player['profession'],
                'position': position,
                'performance_score': performance_score,
                'stats': {
                    'kills': int(player['kills']),
                    'assists': int(player['assists']),
                    'deaths': int(player['deaths']),
                    'kda': round(player['kda'], 2),
                    'player_damage': int(player['player_damage']),
                    'building_damage': int(player['building_damage']),
                    'healing': int(player['healing']),
                    'damage_taken': int(player['damage_taken'])
                }
            }
            
            players_performance.append(performance)
            print(f"     {player['name']} ({player['profession']}) - {position} - 评分: {performance_score}")
        
        battle_analysis[battle_name] = {
            'date': str(battle_date),
            'participants': len(battle_df),
            'players_performance': players_performance
        }
    
    return battle_analysis

def calculate_final_scores(df, battle_analysis):
    """计算最终综合评分"""
    print("计算综合评分...")
    
    final_scores = []
    
    # 按玩家分组
    for unique_id in df['unique_id'].unique():
        player_data = df[df['unique_id'] == unique_id]
        player_name = player_data['name'].iloc[0]
        player_profession = player_data['profession'].iloc[0]
        
        # 计算各项平均指标
        avg_stats = {
            'kills': player_data['kills'].mean(),
            'assists': player_data['assists'].mean(),
            'deaths': player_data['deaths'].mean(),
            'kda': player_data['kda'].mean(),
            'player_damage': player_data['player_damage'].mean(),
            'building_damage': player_data['building_damage'].mean(),
            'healing': player_data['healing'].mean(),
            'damage_taken': player_data['damage_taken'].mean()
        }
        
        # 计算综合评分
        final_score = calculate_comprehensive_score(avg_stats)
        
        # 确定最佳位置
        best_position = infer_main_position(avg_stats)
        
        final_scores.append({
            'unique_id': unique_id,
            'name': player_name,
            'profession': player_profession,
            'battles_count': len(player_data),
            'final_score': final_score,
            'best_position': best_position,
            'avg_stats': avg_stats
        })
        
        print(f"  {player_name} ({player_profession}) - 综合评分: {final_score:.1f} - 最佳位置: {best_position}")
    
    return final_scores

def infer_main_position(player_stats):
    """推测玩家主要位置"""
    if hasattr(player_stats, 'healing'):
        healing = player_stats.healing
        damage = player_stats.player_damage
        damage_taken = player_stats.damage_taken
        building_damage = player_stats.building_damage
    else:
        healing = player_stats['healing']
        damage = player_stats['player_damage']
        damage_taken = player_stats['damage_taken']
        building_damage = player_stats['building_damage']
    
    if healing > 30000000:  # 3千万治疗
        return "辅助位"
    elif damage_taken > 80000000:  # 8千万承伤
        return "坦克位"
    elif building_damage > damage * 0.6:  # 拆塔伤害占主要部分
        return "拆塔位"
    else:
        return "输出位"

def infer_battle_position(player_data):
    """推测单场战斗位置"""
    return infer_main_position(player_data)

def calculate_battle_performance(player_data):
    """计算单场战斗表现评分"""
    kills_score = player_data['kills'] * 12
    assists_score = player_data['assists'] * 6
    damage_score = player_data['player_damage'] / 2000000
    healing_score = player_data['healing'] / 3000000
    building_score = player_data['building_damage'] / 8000000
    death_penalty = player_data['deaths'] * -8
    kda_bonus = min(player_data['kda'] * 3, 30)
    
    total_score = (kills_score + assists_score + damage_score + healing_score + 
                  building_score + death_penalty + kda_bonus)
    
    return round(max(0, total_score), 1)

def calculate_comprehensive_score(avg_stats):
    """计算综合评分"""
    # 基于平均数据的综合评分
    kills_score = avg_stats['kills'] * 15
    assists_score = avg_stats['assists'] * 8
    damage_score = avg_stats['player_damage'] / 1500000
    healing_score = avg_stats['healing'] / 2500000
    building_score = avg_stats['building_damage'] / 6000000
    death_penalty = avg_stats['deaths'] * -10
    kda_bonus = min(avg_stats['kda'] * 5, 50)
    
    total_score = (kills_score + assists_score + damage_score + healing_score + 
                  building_score + death_penalty + kda_bonus)
    
    return round(max(0, total_score), 1)

if __name__ == "__main__":
    comprehensive_analysis()
