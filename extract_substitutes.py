#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
根据Excel表格提取真正的替补成员信息
"""

import pandas as pd
import json

def extract_real_substitutes(excel_file):
    """提取真正的替补成员"""
    print("=" * 60)
    print("提取Excel中的替补成员信息")
    print("=" * 60)
    
    try:
        # 读取团队管理表
        df = pd.read_excel(excel_file, sheet_name='团队管理表')
        
        # 职业对应的行（根据截图）
        profession_mapping = {
            '九灵': ['林若涯', '不送（请假）'],
            '素问': ['逸夏', '呼噜'],
            '神相': ['冷茶', '小金（请假）'],
            '玄机': ['态度要家的海螺塔'],
            '碎梦': ['百里（请假）', '表夏（请假）', '送夏（请假）'],
            '血河': ['你还没手高呢（请'],
            '龙吟': ['对夏（请假）', '徐星花卉（请假）', '逸千夏'],
            '铁衣': [],
            '潮光': ['马场塔家的态度夏']
        }
        
        substitutes = []
        
        # 手动添加从截图中看到的替补成员（排除请假的）
        real_substitutes = {
            '九灵': ['林若涯'],
            '素问': ['逸夏', '呼噜'],
            '神相': ['冷茶'],
            '玄机': ['态度要家的海螺塔'],
            '碎梦': [],  # 都在请假
            '血河': ['你还没手高呢'],  # 去掉（请假）部分
            '龙吟': ['逸千夏'],  # 只有这个没请假
            '铁衣': [],
            '潮光': ['马场塔家的态度夏']
        }
        
        # 添加替补成员
        for profession, names in real_substitutes.items():
            for name in names:
                if name and name.strip():
                    substitutes.append({
                        'name': name.strip(),
                        'profession': profession,
                        'team': '',
                        'position': '',
                        'status': '替补',
                        'source': 'Excel'
                    })
        
        print(f"找到 {len(substitutes)} 名替补成员：")
        for sub in substitutes:
            print(f"  - {sub['name']} ({sub['profession']})")
        
        return substitutes
        
    except Exception as e:
        print(f"提取替补成员失败: {e}")
        return []

def update_complete_members():
    """更新完整成员名单"""
    print("\n" + "=" * 60)
    print("更新完整成员名单")
    print("=" * 60)
    
    # 读取现有的完整成员名单
    with open('complete_guild_members.json', 'r', encoding='utf-8') as f:
        complete_members = json.load(f)
    
    # 提取真正的替补成员
    real_substitutes = extract_real_substitutes("纸落弈酒.xlsx")
    
    # 移除之前错误识别的替补成员
    complete_members = [m for m in complete_members if m['status'] != '替补']
    
    # 添加真正的替补成员
    complete_members.extend(real_substitutes)
    
    # 保存更新后的名单
    with open('complete_guild_members_updated.json', 'w', encoding='utf-8') as f:
        json.dump(complete_members, f, ensure_ascii=False, indent=2)
    
    # 统计信息
    total_members = len(complete_members)
    main_members = len([m for m in complete_members if m['status'] == '主力'])
    battle_members = len([m for m in complete_members if m['status'] == '战斗参与'])
    substitute_members = len([m for m in complete_members if m['status'] == '替补'])
    
    print(f"\n更新后的成员统计：")
    print(f"  总成员数：{total_members}人")
    print(f"  - 主力成员：{main_members}人")
    print(f"  - 战斗参与：{battle_members}人")
    print(f"  - 替补成员：{substitute_members}人")
    
    # 按职业统计替补
    print(f"\n替补成员按职业分布：")
    profession_subs = {}
    for member in complete_members:
        if member['status'] == '替补':
            prof = member['profession']
            if prof not in profession_subs:
                profession_subs[prof] = []
            profession_subs[prof].append(member['name'])
    
    for profession, names in sorted(profession_subs.items()):
        print(f"  {profession}: {len(names)}人 - {', '.join(names)}")
    
    print(f"\n✅ 更新后的完整名单已保存到: complete_guild_members_updated.json")
    
    return complete_members

def main():
    """主函数"""
    updated_members = update_complete_members()
    
    print("\n" + "=" * 60)
    print("第一步更新完成")
    print("=" * 60)
    print("现在我们有了准确的帮会成员信息，包括：")
    print("1. 主力成员（参与战斗的核心成员）")
    print("2. 战斗参与成员（临时参战成员）") 
    print("3. 替补成员（真正的替补，不包括请假的）")
    print("\n下一步可以开始分配团队和位置了！")

if __name__ == "__main__":
    main()
