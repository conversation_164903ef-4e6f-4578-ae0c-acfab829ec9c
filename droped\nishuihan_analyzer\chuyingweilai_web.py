"""
初影未来战斗分析Web应用
专门针对初影未来这一场战斗的数据分析
"""

from flask import Flask, render_template_string, request, jsonify
import sys
import os
import pandas as pd
import json

# 添加路径
sys.path.append('src')

from data_processor import DataProcessor

app = Flask(__name__)

# 全局变量存储数据
battle_data = None
guild_members = {}

def load_chuyingweilai_data():
    """加载初影未来战斗数据"""
    global battle_data, guild_members
    
    try:
        # 加载数据处理器
        processor = DataProcessor("data", "纸落弈酒.xlsx")
        raw_data = processor.load_all_data()
        df = processor.create_unified_dataframe()
        
        # 只保留初影未来的数据
        chuyingweilai_df = None
        for battle in raw_data:
            if "初影未来" in battle['filename']:
                battle_date = battle['date']
                chuyingweilai_df = df[df['battle_date'] == battle_date]
                break
        
        if chuyingweilai_df is None or len(chuyingweilai_df) == 0:
            # 如果没找到，使用所有数据（因为可能只有一场战斗）
            chuyingweilai_df = df
        
        battle_data = chuyingweilai_df
        guild_members = processor.guild_members
        
        print(f"✅ 成功加载初影未来战斗数据: {len(battle_data)}名玩家")
        return True
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return False

# HTML模板
MAIN_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>纸落云烟 - 初影未来战斗分析</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 15px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            cursor: pointer;
            background: #f8f9fa;
            border: none;
            font-size: 1.1em;
            transition: all 0.3s ease;
            color: #333;
        }
        
        .nav-tab.active {
            background: #667eea;
            color: white;
        }
        
        .nav-tab:hover {
            background: #5a6fd8;
            color: white;
        }
        
        .content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            min-height: 600px;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #667eea;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>纸落云烟 - 初影未来战斗分析</h1>
            <p>专业的逆水寒手游战斗数据分析系统</p>
        </div>
        
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('roster')">帮众名单</button>
            <button class="nav-tab" onclick="showTab('analysis')">战斗分析</button>
            <button class="nav-tab" onclick="showTab('positions')">位置评分</button>
            <button class="nav-tab" onclick="showTab('reports')">分析报告</button>
        </div>
        
        <div class="content">
            <!-- 数据概览 -->
            <div class="stats-overview">
                <div class="stat-card">
                    <div class="stat-number" id="totalPlayers">{{ total_players }}</div>
                    <div class="stat-label">参战玩家</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">初影未来</div>
                    <div class="stat-label">分析战斗</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="completedRoster">0</div>
                    <div class="stat-label">名单完成度</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="avgScore">-</div>
                    <div class="stat-label">平均评分</div>
                </div>
            </div>
            
            <!-- 帮众名单标签页 -->
            <div id="rosterTab" class="tab-content active">
                <div class="loading">正在加载帮众名单...</div>
            </div>
            
            <!-- 战斗分析标签页 -->
            <div id="analysisTab" class="tab-content">
                <div class="loading">正在加载战斗分析...</div>
            </div>
            
            <!-- 位置评分标签页 -->
            <div id="positionsTab" class="tab-content">
                <div class="loading">正在加载位置评分...</div>
            </div>
            
            <!-- 分析报告标签页 -->
            <div id="reportsTab" class="tab-content">
                <div class="loading">正在加载分析报告...</div>
            </div>
        </div>
    </div>

    <script>
        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签的active状态
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName + 'Tab').classList.add('active');
            
            // 激活选中的标签
            event.target.classList.add('active');
            
            // 根据标签页加载相应内容
            loadTabContent(tabName);
        }
        
        // 加载标签页内容
        function loadTabContent(tabName) {
            switch(tabName) {
                case 'roster':
                    loadRosterContent();
                    break;
                case 'analysis':
                    loadAnalysisContent();
                    break;
                case 'positions':
                    loadPositionsContent();
                    break;
                case 'reports':
                    loadReportsContent();
                    break;
            }
        }
        
        // 加载帮众名单内容
        function loadRosterContent() {
            fetch('/api/roster')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayRosterContent(data.roster);
                    } else {
                        document.getElementById('rosterTab').innerHTML = 
                            '<div class="error">加载失败: ' + data.error + '</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('rosterTab').innerHTML = 
                        '<div class="error">网络错误: ' + error.message + '</div>';
                });
        }
        
        // 显示帮众名单内容
        function displayRosterContent(roster) {
            // 这里将在下一步实现
            document.getElementById('rosterTab').innerHTML = 
                '<div class="success">帮众名单加载成功，共 ' + roster.length + ' 名玩家</div>';
        }
        
        // 其他内容加载函数（后续实现）
        function loadAnalysisContent() {
            document.getElementById('analysisTab').innerHTML = 
                '<div class="loading">战斗分析功能开发中...</div>';
        }
        
        function loadPositionsContent() {
            document.getElementById('positionsTab').innerHTML = 
                '<div class="loading">位置评分功能开发中...</div>';
        }
        
        function loadReportsContent() {
            document.getElementById('reportsTab').innerHTML = 
                '<div class="loading">分析报告功能开发中...</div>';
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadRosterContent();
        });
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """主页"""
    if battle_data is None:
        if not load_chuyingweilai_data():
            return "数据加载失败，请检查数据文件"
    
    total_players = len(battle_data) if battle_data is not None else 0
    
    return render_template_string(MAIN_TEMPLATE, total_players=total_players)

@app.route('/api/roster')
def api_roster():
    """获取帮众名单API"""
    try:
        if battle_data is None:
            return jsonify({'success': False, 'error': '数据未加载'})
        
        # 生成帮众名单
        roster = []
        for _, player in battle_data.iterrows():
            roster.append({
                'name': player['name'],
                'profession': player['profession'],
                'kills': int(player['kills']),
                'assists': int(player['assists']),
                'deaths': int(player['deaths']),
                'player_damage': int(player['player_damage']),
                'building_damage': int(player['building_damage']),
                'healing': int(player['healing']),
                'heavy_injuries': int(player['heavy_injuries']),
                'resurrections': int(player['resurrections'])
            })
        
        return jsonify({'success': True, 'roster': roster})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    print("启动初影未来战斗分析Web应用...")
    print("访问地址: http://localhost:5003")
    
    # 预加载数据
    if load_chuyingweilai_data():
        print("✅ 数据预加载成功")
        app.run(debug=True, host='0.0.0.0', port=5003)
    else:
        print("❌ 数据预加载失败，请检查数据文件")
