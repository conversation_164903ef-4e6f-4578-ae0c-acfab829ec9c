"""
数据处理模块
用于读取、清理和预处理逆水寒手游战斗数据
专门针对纸落云烟公会的数据分析
"""

import pandas as pd
import numpy as np
import chardet
import os
from typing import List, Dict, Tuple, Optional
from datetime import datetime


class DataProcessor:
    """数据处理器类"""

    def __init__(self, data_dir: str = "data", guild_file: str = "纸落弈酒.xlsx"):
        """
        初始化数据处理器

        Args:
            data_dir: 数据文件目录
            guild_file: 公会成员信息文件
        """
        self.data_dir = data_dir
        self.guild_file = guild_file
        self.raw_data = []
        self.processed_data = None
        self.guild_members = {}  # 存储公会成员信息 {name: profession}
        self.load_guild_members()

    def load_guild_members(self):
        """
        从纸落弈酒.xlsx文件加载公会成员信息
        """
        try:
            # 读取团队管理表
            df = pd.read_excel(self.guild_file, sheet_name='团队管理表', engine='openpyxl')

            # 从第5行开始，提取玩家名字和职业信息
            # 根据观察，玩家信息在Unnamed: 25 (职业) 和 Unnamed: 26 (ID/名字) 列
            for i, row in df.iterrows():
                if i >= 5:  # 从第5行开始
                    profession = row.get('Unnamed: 25')
                    player_name = row.get('Unnamed: 26')

                    if pd.notna(profession) and pd.notna(player_name) and player_name != 0:
                        # 清理数据
                        profession = str(profession).strip()
                        player_name = str(player_name).strip()

                        # 过滤掉无效数据
                        if profession not in ['0', '职业', 'nan'] and player_name not in ['0', 'nan']:
                            # 创建唯一标识符：名字_职业
                            unique_id = f"{player_name}_{profession}"
                            self.guild_members[unique_id] = {
                                'name': player_name,
                                'profession': profession,
                                'original_name': player_name
                            }

                            # 同时保存只有名字的映射，用于查找
                            if player_name not in self.guild_members:
                                self.guild_members[player_name] = []
                            if isinstance(self.guild_members[player_name], list):
                                self.guild_members[player_name].append(profession)
                            else:
                                # 如果已经是字典，转换为列表
                                old_prof = self.guild_members[player_name]['profession']
                                self.guild_members[player_name] = [old_prof, profession]

            print(f"成功加载 {len([k for k in self.guild_members.keys() if '_' in k])} 名公会成员信息")

        except Exception as e:
            print(f"加载公会成员信息失败：{str(e)}")
            print("将使用CSV文件中的职业信息")

    def get_player_profession(self, player_name: str, csv_profession: str = None) -> str:
        """
        获取玩家职业，优先使用公会文件中的信息，包含手动补充的职业映射

        Args:
            player_name: 玩家名字
            csv_profession: CSV文件中的职业信息

        Returns:
            职业名称
        """
        # 手动补充的职业映射（基于提供的表格和准确的职业信息）
        manual_profession_mapping = {
            # 从表格中提取的职业信息
            '林若道': '九灵',
            '逸尘': '素问',
            '今念': '素问',
            '老铁真爱的清纯妹': '神相',
            '旧事': '碎梦',
            '秋实': '碎梦',
            '迟夏': '碎梦',
            '徐远师爷救苦': '龙吟',
            '折翼': '血河',
            '侯爵花开': '血河',
            '海妖瞳爱的太喜真': '潮光',
            '不连跳': '紫霞',
            # 基于数据分析补充的职业
            '绝乂': '素问',
            '阿橘喵': '九灵',
            '望予': '神相',
            '彼岸花开乀': '血河',
            '小金丶': '神相',
            '祁寒': '血河',
            '楚晚寕': '铁衣',
            '月夜之瞳': '素问',
            # 最新补充的准确职业信息
            '秋寒': '碎梦',
            '凌千落': '素问',  # 注：有素问和潮光两个职业，这里先用素问
            '千面辛': '神相',
            '凇鴉': '九灵',
            '晚淞': '玄机',
            '饱饱丶': '九灵',
            '不送': '九灵',
            '困嘟': '碎梦'
        }

        # 处理名字变体（考虑到可能的输入差异）
        name_variants = {
            '千面幸': '千面辛',
            '松鸦': '凇鴉',
            '饱饱': '饱饱丶'
        }

        # 检查名字变体
        actual_name = name_variants.get(player_name, player_name)

        # 优先使用手动映射
        if actual_name in manual_profession_mapping:
            return manual_profession_mapping[actual_name]

        # 然后检查公会文件中的信息
        if player_name in self.guild_members:
            guild_info = self.guild_members[player_name]
            if isinstance(guild_info, list):
                # 如果有多个职业，优先返回与CSV匹配的
                if csv_profession and csv_profession in guild_info:
                    return csv_profession
                else:
                    return guild_info[0]  # 返回第一个职业
            elif isinstance(guild_info, dict):
                return guild_info['profession']

        # 如果公会文件中没有，使用CSV中的职业信息
        return csv_profession if csv_profession else 'unknown'

    def detect_encoding(self, file_path: str) -> str:
        """
        检测文件编码
        
        Args:
            file_path: 文件路径
            
        Returns:
            检测到的编码格式
        """
        with open(file_path, 'rb') as f:
            raw_data = f.read()
            result = chardet.detect(raw_data)
            return result['encoding']
    
    def read_csv_file(self, file_path: str) -> pd.DataFrame:
        """
        读取CSV文件，自动处理编码问题

        Args:
            file_path: CSV文件路径

        Returns:
            DataFrame对象
        """
        try:
            # 首先尝试UTF-8编码，不使用header，允许不同行有不同列数
            df = pd.read_csv(file_path, encoding='utf-8', header=None,
                           on_bad_lines='skip', engine='python')
        except UnicodeDecodeError:
            try:
                # 尝试GBK编码
                df = pd.read_csv(file_path, encoding='gbk', header=None,
                               on_bad_lines='skip', engine='python')
            except UnicodeDecodeError:
                # 自动检测编码
                encoding = self.detect_encoding(file_path)
                df = pd.read_csv(file_path, encoding=encoding, header=None,
                               on_bad_lines='skip', engine='python')

        return df
    
    def parse_battle_data(self, file_path: str) -> Dict:
        """
        解析单个战斗数据文件，专门处理纸落云烟的数据

        Args:
            file_path: 文件路径

        Returns:
            包含战斗信息的字典
        """
        df = self.read_csv_file(file_path)

        # 提取文件名信息
        filename = os.path.basename(file_path)
        parts = filename.replace('.csv', '').split('_')

        # 解析日期时间
        date_str = parts[0] if len(parts) > 0 else 'unknown'
        time_str = parts[1] if len(parts) > 1 else 'unknown'

        # 转换为标准日期格式
        battle_date = self.parse_date(date_str)

        battle_info = {
            'filename': filename,
            'date': date_str,
            'time': time_str,
            'battle_date': battle_date,
            'guild1': parts[2] if len(parts) > 2 else 'unknown',
            'guild2': parts[3] if len(parts) > 3 else 'unknown',
            'our_guild_teams': [],  # 纸落云烟的队伍
            'enemy_teams': []       # 对手队伍
        }

        # 解析团队数据
        current_team = None
        current_players = []
        is_our_guild = False

        for idx, row in df.iterrows():
            # 检查是否是团队标题行（第一列是团队名，第二列是数字，且列数较少）
            if (pd.notna(row.iloc[0]) and len(row) > 1 and pd.notna(row.iloc[1]) and
                str(row.iloc[1]).isdigit() and len([x for x in row if pd.notna(x)]) == 2):

                # 保存上一个团队
                if current_team is not None:
                    team_data = {
                        'name': current_team,
                        'players': current_players,
                        'is_our_guild': is_our_guild
                    }

                    if is_our_guild:
                        battle_info['our_guild_teams'].append(team_data)
                    else:
                        battle_info['enemy_teams'].append(team_data)

                # 开始新团队
                current_team = str(row.iloc[0]).strip()
                current_players = []

                # 判断是否是我们的公会（纸落云烟）
                is_our_guild = '纸落云烟' in current_team

            # 检查是否是列标题行
            elif pd.notna(row.iloc[0]) and '玩家名字' in str(row.iloc[0]):
                continue
            # 检查是否是玩家数据行（有足够的列，且第一列不是数字）
            elif (pd.notna(row.iloc[0]) and len(row) >= 12 and
                  not str(row.iloc[0]).isdigit() and '玩家名字' not in str(row.iloc[0]) and
                  pd.notna(row.iloc[1]) and str(row.iloc[0]).strip() != ''):

                player_data = self.parse_player_row(row, is_our_guild)
                if player_data:
                    current_players.append(player_data)

        # 添加最后一个团队
        if current_team is not None:
            team_data = {
                'name': current_team,
                'players': current_players,
                'is_our_guild': is_our_guild
            }

            if is_our_guild:
                battle_info['our_guild_teams'].append(team_data)
            else:
                battle_info['enemy_teams'].append(team_data)

        return battle_info

    def parse_date(self, date_str: str) -> datetime:
        """
        解析日期字符串

        Args:
            date_str: 日期字符串，如 "20250531"

        Returns:
            datetime对象
        """
        try:
            if len(date_str) == 8:  # YYYYMMDD格式
                return datetime.strptime(date_str, '%Y%m%d')
            elif len(date_str) == 7:  # YYYYMDD格式
                return datetime.strptime(date_str, '%Y%m%d')
            else:
                return datetime.now()
        except:
            return datetime.now()
    
    def parse_player_row(self, row: pd.Series, is_our_guild: bool = False) -> Optional[Dict]:
        """
        解析玩家数据行

        Args:
            row: 数据行
            is_our_guild: 是否是我们公会的玩家

        Returns:
            玩家数据字典
        """
        try:
            # 检查行是否有足够的列
            if len(row) < 12:
                return None

            name = str(row.iloc[0]).strip()
            csv_profession = str(row.iloc[1]).strip() if pd.notna(row.iloc[1]) else 'unknown'

            # 过滤掉无效数据
            if name in ['', 'nan', 'nil', 'NaN']:
                return None

            # 如果CSV中的职业是nil或unknown，且是我们公会的玩家，使用公会文件中的信息
            if is_our_guild and (csv_profession in ['nil', 'unknown', 'nan']):
                profession = self.get_player_profession(name, None)
            elif is_our_guild:
                profession = self.get_player_profession(name, csv_profession)
            else:
                profession = csv_profession if csv_profession not in ['nil', 'nan'] else 'unknown'

            # 创建唯一标识符（名字_职业）
            unique_id = f"{name}_{profession}"

            # 安全地解析数值，处理可能的空值或无效值
            def safe_int(value, default=0):
                try:
                    if pd.isna(value) or value == '':
                        return default
                    return int(float(str(value).replace(',', '')))
                except (ValueError, TypeError):
                    return default

            player_data = {
                'name': name,
                'profession': profession,
                'unique_id': unique_id,
                'is_our_guild': is_our_guild,
                'kills': safe_int(row.iloc[2]),
                'assists': safe_int(row.iloc[3]),
                'resources': safe_int(row.iloc[4]),
                'player_damage': safe_int(row.iloc[5]),
                'building_damage': safe_int(row.iloc[6]),
                'healing': safe_int(row.iloc[7]),
                'damage_taken': safe_int(row.iloc[8]),
                'deaths': safe_int(row.iloc[9]),
                'heavy_injuries': safe_int(row.iloc[10]),  # 重伤
                'resurrections': safe_int(row.iloc[11])    # 化羽/清泉
            }

            # 计算一些衍生指标
            player_data['kda'] = (player_data['kills'] + player_data['assists']) / max(player_data['deaths'], 1)
            player_data['damage_per_death'] = player_data['player_damage'] / max(player_data['deaths'], 1)
            player_data['total_damage'] = player_data['player_damage'] + player_data['building_damage']

            return player_data

        except (ValueError, IndexError) as e:
            print(f"解析玩家数据行时出错: {e}, 行数据: {row.tolist() if hasattr(row, 'tolist') else row}")
            return None
    
    def load_all_data(self) -> List[Dict]:
        """
        加载所有CSV文件的数据
        
        Returns:
            所有战斗数据的列表
        """
        csv_files = [f for f in os.listdir(self.data_dir) if f.endswith('.csv')]
        
        for csv_file in csv_files:
            file_path = os.path.join(self.data_dir, csv_file)
            try:
                battle_data = self.parse_battle_data(file_path)
                self.raw_data.append(battle_data)
                print(f"成功加载文件: {csv_file}")
            except Exception as e:
                print(f"加载文件 {csv_file} 时出错: {str(e)}")
        
        return self.raw_data
    
    def create_unified_dataframe(self) -> pd.DataFrame:
        """
        创建统一的DataFrame用于分析，只包含纸落云烟的数据

        Returns:
            统一格式的DataFrame
        """
        our_guild_players = []

        for battle in self.raw_data:
            # 只处理我们公会的队伍
            for team in battle['our_guild_teams']:
                for player in team['players']:
                    player_record = player.copy()
                    player_record['battle_date_str'] = battle['date']
                    player_record['battle_time'] = battle['time']
                    player_record['battle_date'] = battle['battle_date']
                    player_record['guild1'] = battle['guild1']
                    player_record['guild2'] = battle['guild2']
                    player_record['team'] = team['name']
                    player_record['filename'] = battle['filename']

                    # 计算对手强度（基于对手队伍的平均数据）
                    enemy_strength = self.calculate_enemy_strength(battle['enemy_teams'])
                    player_record['enemy_strength'] = enemy_strength

                    our_guild_players.append(player_record)

        self.processed_data = pd.DataFrame(our_guild_players)

        # 按日期排序
        if not self.processed_data.empty:
            self.processed_data = self.processed_data.sort_values('battle_date')

        return self.processed_data

    def calculate_enemy_strength(self, enemy_teams: List[Dict]) -> float:
        """
        计算对手强度

        Args:
            enemy_teams: 对手队伍列表

        Returns:
            对手强度评分
        """
        if not enemy_teams:
            return 50.0  # 默认中等强度

        total_damage = 0
        total_kills = 0
        total_players = 0

        for team in enemy_teams:
            for player in team['players']:
                total_damage += player.get('player_damage', 0)
                total_kills += player.get('kills', 0)
                total_players += 1

        if total_players == 0:
            return 50.0

        # 基于平均伤害和击杀计算强度
        avg_damage = total_damage / total_players
        avg_kills = total_kills / total_players

        # 简单的强度评分算法（可以根据需要调整）
        strength = min(100, max(0, (avg_damage / 100000000) * 30 + avg_kills * 10 + 20))

        return strength
    
    def get_summary_stats(self) -> Dict:
        """
        获取数据摘要统计

        Returns:
            统计信息字典
        """
        if self.processed_data is None or self.processed_data.empty:
            return {}

        stats = {
            'total_battles': len(self.raw_data),
            'total_players': len(self.processed_data),  # 添加缺失的键
            'total_our_guild_records': len(self.processed_data),
            'unique_players': self.processed_data['name'].nunique(),
            'unique_player_profession_combinations': self.processed_data['unique_id'].nunique(),
            'professions': self.processed_data['profession'].value_counts().to_dict(),
            'avg_kills': self.processed_data['kills'].mean(),
            'avg_assists': self.processed_data['assists'].mean(),
            'avg_player_damage': self.processed_data['player_damage'].mean(),
            'avg_building_damage': self.processed_data['building_damage'].mean(),
            'avg_healing': self.processed_data['healing'].mean(),
            'avg_heavy_injuries': self.processed_data['heavy_injuries'].mean(),
            'avg_resurrections': self.processed_data['resurrections'].mean(),
            'avg_enemy_strength': self.processed_data['enemy_strength'].mean(),
            'date_range': {
                'start': self.processed_data['battle_date'].min().strftime('%Y-%m-%d'),
                'end': self.processed_data['battle_date'].max().strftime('%Y-%m-%d')
            }
        }

        return stats
