#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建完整的只读版本
"""

import os

def create_readonly_version():
    """创建完整的只读版本"""
    
    print("🔄 开始创建只读版本...")
    
    # 读取app.py的完整内容
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"✅ 读取app.py成功，共{len(content)}字符")
    except Exception as e:
        print(f"❌ 读取app.py失败: {e}")
        return False
    
    # 修改内容
    print("🔄 修改配置...")
    
    # 1. 修改标题
    content = content.replace(
        '纸落云烟帮会管理系统 - Web应用',
        '纸落云烟帮会管理系统 - 成员只读版本'
    )
    
    # 2. 修改端口
    content = content.replace('port=5888', 'port=5000')
    content = content.replace("'PORT', 5888)", "'PORT', 5000)")
    content = content.replace('localhost:5888', 'localhost:5000')
    
    # 3. 添加只读模式配置
    lines = content.split('\n')
    for i, line in enumerate(lines):
        if 'app = Flask(__name__)' in line:
            lines.insert(i+1, '')
            lines.insert(i+2, '# 配置只读模式')
            lines.insert(i+3, 'app.config["READONLY_MODE"] = True')
            lines.insert(i+4, 'app.jinja_env.globals["readonly_mode"] = True')
            print("✅ 添加只读模式配置")
            break
    
    content = '\n'.join(lines)
    
    # 4. 修改启动信息
    content = content.replace(
        '🚀 纸落云烟帮会管理系统启动中...',
        '👥 纸落云烟帮会管理系统 - 成员只读版本启动中...'
    )
    
    content = content.replace(
        '   - 成员管理 (查看、编辑、搜索)',
        '   - 成员详情 (只读查看)'
    )
    
    content = content.replace(
        '   - 团队分配管理',
        '   - 组织架构 (只读查看)'
    )
    
    content = content.replace(
        '   - 数据统计和可视化',
        '   - 战斗分析 (只读查看)'
    )
    
    content = content.replace(
        '   - 数据导出',
        '   - 与管理后台数据实时同步'
    )
    
    # 写入新文件
    try:
        with open('member_readonly.py', 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 写入member_readonly.py成功")
    except Exception as e:
        print(f"❌ 写入文件失败: {e}")
        return False
    
    # 验证文件
    try:
        with open('member_readonly.py', 'r', encoding='utf-8') as f:
            new_content = f.read()
        print(f"✅ 验证成功，新文件共{len(new_content)}字符")
        
        # 检查关键修改
        if 'readonly_mode' in new_content:
            print("✅ 只读模式配置已添加")
        if 'port=5000' in new_content:
            print("✅ 端口已修改为5000")
        if '成员只读版本' in new_content:
            print("✅ 标题已修改")
            
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == '__main__':
    if create_readonly_version():
        print("\n🎉 只读版本创建成功！")
        print("📝 现在可以运行: python member_readonly.py")
    else:
        print("\n❌ 创建失败！")
