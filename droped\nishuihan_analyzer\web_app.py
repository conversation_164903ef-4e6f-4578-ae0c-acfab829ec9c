"""
纸落云烟数据分析Web界面
使用Flask创建可视化Web应用
"""

from flask import Flask, render_template, jsonify, request
import sys
import os
import json
import pandas as pd

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_processor import DataProcessor
from analyzer import NishuihanPlayerAnalyzer

app = Flask(__name__)

# 全局变量存储分析结果
analyzer = None
player_stats = None
time_series_analysis = None
position_analysis = None

def load_data():
    """加载和分析数据"""
    global analyzer, player_stats, time_series_analysis, position_analysis
    
    try:
        # 数据处理
        processor = DataProcessor("data", "纸落弈酒.xlsx")
        raw_data = processor.load_all_data()
        df = processor.create_unified_dataframe()
        
        # 数据分析
        analyzer = NishuihanPlayerAnalyzer(df)
        player_stats = analyzer.calculate_player_scores()
        time_series_analysis = analyzer.analyze_time_series()
        position_analysis = analyzer.analyze_position_suitability()
        
        return True
    except Exception as e:
        print(f"数据加载失败: {e}")
        return False

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/overview')
def api_overview():
    """获取数据概览"""
    if player_stats is None:
        return jsonify({'error': '数据未加载'})
    
    # 强弱分布
    strength_dist = player_stats['strength_level'].value_counts().to_dict()
    
    # 职业分布
    profession_dist = player_stats['profession_first'].value_counts().to_dict()
    
    # 进步情况分布
    progress_dist = {}
    if time_series_analysis:
        for analysis in time_series_analysis.values():
            level = analysis['progress_assessment']['progress_level']
            progress_dist[level] = progress_dist.get(level, 0) + 1
    
    return jsonify({
        'total_players': len(player_stats),
        'strength_distribution': strength_dist,
        'profession_distribution': profession_dist,
        'progress_distribution': progress_dist
    })

@app.route('/api/players')
def api_players():
    """获取所有玩家列表"""
    if player_stats is None:
        return jsonify({'error': '数据未加载'})
    
    players = []
    for unique_id, data in player_stats.iterrows():
        player_info = {
            'unique_id': unique_id,
            'name': data['name_first'],
            'profession': data['profession_first'],
            'battles_count': int(data['battles_count']),
            'combat_score': round(float(data['combat_score']), 1),
            'strength_level': data['strength_level'],
            'kills_total': int(data['kills_sum']),
            'assists_total': int(data['assists_sum']),
            'player_damage_total': int(data['player_damage_sum']),
            'healing_total': int(data['healing_sum']),
            'kda': round(float(data['kda']), 2)
        }
        
        # 添加进步情况
        if unique_id in time_series_analysis:
            progress = time_series_analysis[unique_id]['progress_assessment']
            player_info['progress_level'] = progress['progress_level']
            player_info['progress_score'] = round(float(progress['progress_score']), 2)
        else:
            player_info['progress_level'] = '数据不足'
            player_info['progress_score'] = 0
        
        # 添加位置适合性
        if unique_id in position_analysis:
            pos_data = position_analysis[unique_id]
            player_info['best_position'] = pos_data['best_position']
            player_info['best_position_score'] = round(float(pos_data['best_score']), 1)
        else:
            player_info['best_position'] = 'unknown'
            player_info['best_position_score'] = 0
        
        players.append(player_info)
    
    return jsonify(players)

@app.route('/api/player/<unique_id>')
def api_player_detail(unique_id):
    """获取单个玩家详细信息"""
    if player_stats is None:
        return jsonify({'error': '数据未加载'})
    
    if unique_id not in player_stats.index:
        return jsonify({'error': '玩家不存在'})
    
    data = player_stats.loc[unique_id]
    
    player_detail = {
        'unique_id': unique_id,
        'name': data['name_first'],
        'profession': data['profession_first'],
        'battles_count': int(data['battles_count']),
        'combat_score': round(float(data['combat_score']), 1),
        'strength_level': data['strength_level'],
        'stats': {
            'kills': {'total': int(data['kills_sum']), 'average': round(float(data['kills_mean']), 1)},
            'assists': {'total': int(data['assists_sum']), 'average': round(float(data['assists_mean']), 1)},
            'player_damage': {'total': int(data['player_damage_sum']), 'average': int(data['player_damage_mean'])},
            'building_damage': {'total': int(data['building_damage_sum']), 'average': int(data['building_damage_mean'])},
            'healing': {'total': int(data['healing_sum']), 'average': int(data['healing_mean'])},
            'damage_taken': {'total': int(data['damage_taken_sum']), 'average': int(data['damage_taken_mean'])},
            'deaths': {'total': int(data['deaths_sum']), 'average': round(float(data['deaths_mean']), 1)},
            'heavy_injuries': {'total': int(data['heavy_injuries_sum']), 'average': round(float(data['heavy_injuries_mean']), 1)},
            'resurrections': {'total': int(data['resurrections_sum']), 'average': round(float(data['resurrections_mean']), 1)},
            'kda': round(float(data['kda']), 2),
            'enemy_strength': round(float(data['enemy_strength_mean']), 1)
        }
    }
    
    # 添加时间序列分析
    if unique_id in time_series_analysis:
        ts_data = time_series_analysis[unique_id]
        player_detail['time_series'] = {
            'progress_assessment': ts_data['progress_assessment'],
            'trends': ts_data['trends']
        }
    
    # 添加位置分析
    if unique_id in position_analysis:
        pos_data = position_analysis[unique_id]
        player_detail['position_analysis'] = {
            'position_scores': pos_data['position_scores'],
            'best_position': pos_data['best_position'],
            'best_score': round(float(pos_data['best_score']), 1),
            'specialties': pos_data['specialties'],
            'recommendations': pos_data['recommendations']
        }
    
    return jsonify(player_detail)

@app.route('/api/rankings/<position>')
def api_position_rankings(position):
    """获取位置排行榜"""
    if analyzer is None:
        return jsonify({'error': '数据未加载'})
    
    try:
        rankings = analyzer.get_position_rankings(position, 20)
        return jsonify(rankings)
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/api/unknown_professions')
def api_unknown_professions():
    """获取职业不确定的玩家"""
    if player_stats is None:
        return jsonify({'error': '数据未加载'})
    
    unknown_players = []
    for unique_id, data in player_stats.iterrows():
        if data['profession_first'] in ['unknown', 'nil', 'nan', '']:
            unknown_players.append({
                'unique_id': unique_id,
                'name': data['name_first'],
                'profession': data['profession_first'],
                'player_damage_avg': int(data['player_damage_mean']),
                'healing_avg': int(data['healing_mean']),
                'building_damage_avg': int(data['building_damage_mean']),
                'damage_taken_avg': int(data['damage_taken_mean'])
            })
    
    return jsonify(unknown_players)

if __name__ == '__main__':
    print("正在启动纸落云烟数据分析Web应用...")
    
    # 加载数据
    if load_data():
        print("数据加载成功！")
        print("Web应用启动地址: http://localhost:5000")
        app.run(debug=True, host='0.0.0.0', port=5000)
    else:
        print("数据加载失败，请检查数据文件！")
