# 测试最终的职业更新结果
import sys
import os
sys.path.append('nishuihan_analyzer/src')

from data_processor import DataProcessor
from analyzer import NishuihanPlayerAnalyzer

try:
    print("=" * 70)
    print("测试最终职业更新结果")
    print("=" * 70)
    
    # 加载数据
    processor = DataProcessor("nishuihan_analyzer/data", "nishuihan_analyzer/纸落弈酒.xlsx")
    raw_data = processor.load_all_data()
    df = processor.create_unified_dataframe()
    
    print(f"总共加载了 {len(df)} 条战斗记录")
    
    # 检查职业分布
    print("\n📊 职业分布统计:")
    profession_dist = df['profession'].value_counts()
    total_records = len(df)
    
    for profession, count in profession_dist.items():
        percentage = (count / total_records) * 100
        print(f"  {profession}: {count}人次 ({percentage:.1f}%)")
    
    # 检查是否还有未知职业
    unknown_count = df[df['profession'].isin(['unknown', 'nil', 'nan', ''])].shape[0]
    print(f"\n❓ 职业不确定的记录: {unknown_count}条")
    
    if unknown_count == 0:
        print("🎉 太好了！所有玩家的职业信息都已完整！")
    else:
        unknown_players = df[df['profession'].isin(['unknown', 'nil', 'nan', ''])]['name'].unique()
        print(f"仍有 {len(unknown_players)} 名玩家职业不确定:")
        for player in unknown_players:
            print(f"  - {player}")
    
    # 分析数据
    print("\n🔍 开始数据分析...")
    analyzer = NishuihanPlayerAnalyzer(df)
    player_stats = analyzer.calculate_player_scores()
    position_analysis = analyzer.analyze_position_suitability()
    
    print(f"✅ 成功分析了 {len(player_stats)} 名玩家")
    
    # 显示战力前10名
    print("\n🏆 战力排行榜前10名:")
    top_10 = player_stats.nlargest(10, 'combat_score')
    for i, (unique_id, data) in enumerate(top_10.iterrows(), 1):
        print(f"  {i:2d}. {data['name_first']} ({data['profession_first']}) - 战力: {data['combat_score']:.1f}")
    
    # 显示各职业的代表玩家
    print("\n👥 各职业代表玩家:")
    for profession in profession_dist.index:
        if profession not in ['unknown', 'nil', 'nan', '']:
            prof_players = player_stats[player_stats['profession_first'] == profession]
            if len(prof_players) > 0:
                top_player = prof_players.loc[prof_players['combat_score'].idxmax()]
                print(f"  {profession}: {top_player['name_first']} (战力: {top_player['combat_score']:.1f})")
    
    # 显示各位置最佳人选
    print("\n🎯 各位置最佳人选:")
    position_names = {
        'damage_dealer': '输出位',
        'tank': '坦克位',
        'support': '辅助位',
        'demolisher': '拆塔位'
    }
    
    for position, name in position_names.items():
        rankings = analyzer.get_position_rankings(position, 3)
        print(f"  {name}:")
        for i, player in enumerate(rankings, 1):
            best_mark = " ⭐" if player['is_best_position'] else ""
            print(f"    {i}. {player['name']} ({player['profession']}) - {player['score']:.1f}{best_mark}")
    
    print("\n" + "=" * 70)
    print("🎉 职业信息更新完成！系统已准备就绪。")
    print("💡 现在可以运行Web界面查看完整分析结果:")
    print("   cd nishuihan_analyzer && python simple_web.py")
    print("=" * 70)

except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
