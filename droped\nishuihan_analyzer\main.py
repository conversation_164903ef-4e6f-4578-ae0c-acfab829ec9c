"""
逆水寒手游数据分析主程序
自动分析战斗数据，分辨玩家强弱
"""

import os
import sys
import pandas as pd
from datetime import datetime

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_processor import DataProcessor
from analyzer import NishuihanPlayerAnalyzer
from visualizer import DataVisualizer


class NishuihanAnalyzer:
    """逆水寒数据分析器主类"""
    
    def __init__(self):
        """初始化分析器"""
        self.data_processor = DataProcessor("data")
        self.analyzer = None
        self.visualizer = DataVisualizer("output")
        self.raw_data = None
        self.processed_data = None
        self.player_stats = None
        self.profession_stats = None
        
    def run_analysis(self):
        """运行完整的数据分析流程"""
        print("=" * 60)
        print("逆水寒手游数据分析系统")
        print("=" * 60)
        
        # 1. 加载数据
        print("\n1. 正在加载数据...")
        self.load_data()
        
        # 2. 数据分析
        print("\n2. 正在分析数据...")
        self.analyze_data()
        
        # 3. 生成可视化
        print("\n3. 正在生成图表...")
        self.generate_visualizations()
        
        # 4. 生成报告
        print("\n4. 正在生成报告...")
        self.generate_reports()
        
        print("\n" + "=" * 60)
        print("分析完成！请查看output目录中的结果文件。")
        print("=" * 60)
    
    def load_data(self):
        """加载和处理数据"""
        try:
            # 加载原始数据
            self.raw_data = self.data_processor.load_all_data()
            
            if not self.raw_data:
                print("错误：未找到有效的数据文件！")
                return False
            
            # 创建统一的DataFrame
            self.processed_data = self.data_processor.create_unified_dataframe()
            
            # 显示数据摘要
            stats = self.data_processor.get_summary_stats()
            print(f"  - 加载了 {stats['total_battles']} 场战斗数据")
            print(f"  - 包含 {stats['unique_players']} 名不同玩家")
            print(f"  - 总计 {stats['total_players']} 条玩家记录")
            
            return True
            
        except Exception as e:
            print(f"数据加载失败：{str(e)}")
            return False
    
    def analyze_data(self):
        """分析数据"""
        try:
            # 初始化分析器
            self.analyzer = NishuihanPlayerAnalyzer(self.processed_data)

            # 计算玩家评分
            self.player_stats = self.analyzer.calculate_player_scores()
            print(f"  - 计算了 {len(self.player_stats)} 名玩家的综合评分")

            # 时间序列分析
            time_series_results = self.analyzer.analyze_time_series()
            print(f"  - 完成了 {len(time_series_results)} 名玩家的时间序列分析")

            # 位置适合性分析
            position_analysis = self.analyzer.analyze_position_suitability()
            print(f"  - 完成了 {len(position_analysis)} 名玩家的位置适合性分析")

            # 分析职业特点
            self.profession_stats = self.analyzer.analyze_professions()
            print(f"  - 分析了 {len(self.profession_stats)} 个职业的特点")

            # 显示强弱分布
            strength_dist = self.player_stats['strength_level'].value_counts()
            print("  - 玩家强弱分布：")
            for level, count in strength_dist.items():
                print(f"    {level}: {count}人 ({count/len(self.player_stats)*100:.1f}%)")

            # 显示进步情况统计
            progress_stats = {}
            for unique_id, analysis in time_series_results.items():
                level = analysis['progress_assessment']['progress_level']
                progress_stats[level] = progress_stats.get(level, 0) + 1

            print("  - 玩家进步情况分布：")
            for level, count in progress_stats.items():
                print(f"    {level}: {count}人")

            return True

        except Exception as e:
            print(f"数据分析失败：{str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def generate_visualizations(self):
        """生成可视化图表"""
        try:
            # 玩家排行榜
            ranking_file = self.visualizer.plot_player_ranking(self.player_stats)
            print(f"  - 生成玩家排行榜：{ranking_file}")
            
            # 职业分析图
            profession_file = self.visualizer.plot_profession_analysis(self.profession_stats)
            print(f"  - 生成职业分析图：{profession_file}")
            
            # 强弱分布图
            distribution_file = self.visualizer.plot_strength_distribution(self.player_stats)
            print(f"  - 生成强弱分布图：{distribution_file}")
            
            # 相关性热力图
            heatmap_file = self.visualizer.plot_correlation_heatmap(self.player_stats)
            print(f"  - 生成相关性热力图：{heatmap_file}")
            
            # 交互式仪表板
            dashboard_file = self.visualizer.create_interactive_dashboard(
                self.player_stats, self.profession_stats)
            print(f"  - 生成交互式仪表板：{dashboard_file}")
            
            return True
            
        except Exception as e:
            print(f"可视化生成失败：{str(e)}")
            return False
    
    def generate_reports(self):
        """生成分析报告"""
        try:
            # 生成总体报告
            self.generate_summary_report()
            
            # 生成前10名玩家的详细报告
            top_players = self.player_stats.nlargest(10, 'combat_score')
            for player_name in top_players.index[:5]:  # 只为前5名生成详细报告
                try:
                    report_file = self.visualizer.generate_player_report(
                        player_name, self.player_stats, self.processed_data)
                    if report_file:
                        print(f"  - 生成 {player_name} 的详细报告：{report_file}")
                except Exception as e:
                    print(f"  - 生成 {player_name} 报告时出错：{str(e)}")
            
            return True
            
        except Exception as e:
            print(f"报告生成失败：{str(e)}")
            return False
    
    def generate_summary_report(self):
        """生成总结报告"""
        try:
            report_content = []
            report_content.append("逆水寒手游数据分析报告")
            report_content.append("=" * 50)
            report_content.append(f"生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report_content.append("")
            
            # 数据概览
            stats = self.data_processor.get_summary_stats()
            report_content.append("数据概览：")
            report_content.append(f"  战斗场次：{stats['total_battles']}")
            report_content.append(f"  参与玩家：{stats['unique_players']}人")
            report_content.append(f"  总记录数：{stats['total_players']}")
            report_content.append("")
            
            # 职业分布
            report_content.append("职业分布：")
            for profession, count in stats['professions'].items():
                if profession != 'unknown' and profession != 'nil':
                    report_content.append(f"  {profession}：{count}人次")
            report_content.append("")
            
            # 强弱分布
            strength_dist = self.player_stats['strength_level'].value_counts()
            report_content.append("玩家强弱分布：")
            for level, count in strength_dist.items():
                percentage = count / len(self.player_stats) * 100
                report_content.append(f"  {level}：{count}人 ({percentage:.1f}%)")
            report_content.append("")
            
            # 前10名玩家
            top_10 = self.player_stats.nlargest(10, 'combat_score')
            report_content.append("战力排行榜前10名：")
            for i, (name, data) in enumerate(top_10.iterrows(), 1):
                profession = data['profession_first']
                score = data['combat_score']
                kda = data['kda']
                level = data['strength_level']
                report_content.append(f"  {i:2d}. {name} ({profession}) - 战力:{score:.1f} KDA:{kda:.2f} 等级:{level}")
            report_content.append("")
            
            # 职业分析
            report_content.append("职业特点分析：")
            for profession, data in self.profession_stats.iterrows():
                if profession != 'unknown' and profession != 'nil':
                    role = data['role_type']
                    avg_score = data['combat_score']
                    avg_kda = data['kda']
                    report_content.append(f"  {profession} ({role})：平均战力 {avg_score:.1f}，平均KDA {avg_kda:.2f}")
            report_content.append("")
            
            # 保存报告
            report_file = os.path.join(self.visualizer.output_dir, "analysis_summary.txt")
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(report_content))
            
            print(f"  - 生成总结报告：{report_file}")
            
        except Exception as e:
            print(f"总结报告生成失败：{str(e)}")
    
    def interactive_query(self):
        """交互式查询功能"""
        if self.player_stats is None:
            print("请先运行完整分析！")
            return
        
        while True:
            print("\n" + "=" * 50)
            print("纸落云烟数据分析系统 - 交互式查询")
            print("=" * 50)
            print("1. 查询玩家详细信息")
            print("2. 查看位置排行榜")
            print("3. 查看玩家进步情况")
            print("4. 查看位置适合性分析")
            print("5. 查看职业统计")
            print("6. 查看强弱分布")
            print("7. 查找相似玩家")
            print("0. 退出")
            print("=" * 50)

            choice = input("请选择功能 (0-7): ").strip()

            if choice == '0':
                break
            elif choice == '1':
                self.query_player_detailed()
            elif choice == '2':
                self.show_position_rankings()
            elif choice == '3':
                self.show_player_progress()
            elif choice == '4':
                self.show_position_analysis()
            elif choice == '5':
                self.show_profession_ranking()
            elif choice == '6':
                self.show_strength_distribution()
            elif choice == '7':
                self.find_similar_players()
            else:
                print("无效选择，请重试！")
    
    def query_player_detailed(self):
        """查询玩家详细信息"""
        player_name = input("请输入玩家名字: ").strip()

        # 查找匹配的玩家（可能有多个职业）
        matching_players = []
        for unique_id in self.player_stats.index:
            if player_name in unique_id:
                matching_players.append(unique_id)

        if not matching_players:
            print(f"未找到玩家 '{player_name}'")
            return

        if len(matching_players) > 1:
            print(f"找到多个匹配的玩家：")
            for i, unique_id in enumerate(matching_players, 1):
                print(f"  {i}. {unique_id}")

            try:
                choice = int(input("请选择 (输入序号): ")) - 1
                if 0 <= choice < len(matching_players):
                    selected_id = matching_players[choice]
                else:
                    print("无效选择")
                    return
            except ValueError:
                print("无效输入")
                return
        else:
            selected_id = matching_players[0]

        # 显示详细信息
        data = self.player_stats.loc[selected_id]
        print(f"\n{selected_id} 的详细信息：")
        print(f"  玩家名字：{data['name_first']}")
        print(f"  职业：{data['profession_first']}")
        print(f"  参战次数：{data['battles_count']}")
        print(f"  总击败：{data['kills_sum']} (平均: {data['kills_mean']:.1f})")
        print(f"  总助攻：{data['assists_sum']} (平均: {data['assists_mean']:.1f})")
        print(f"  总伤害：{data['player_damage_sum']:,} (平均: {data['player_damage_mean']:,.0f})")
        print(f"  拆塔伤害：{data['building_damage_sum']:,} (平均: {data['building_damage_mean']:,.0f})")
        print(f"  治疗量：{data['healing_sum']:,} (平均: {data['healing_mean']:,.0f})")
        print(f"  KDA：{data['kda']:.2f}")
        print(f"  战力评分：{data['combat_score']:.1f}")
        print(f"  强弱等级：{data['strength_level']}")
        print(f"  平均对手强度：{data['enemy_strength_mean']:.1f}")

        # 显示时间序列分析
        if hasattr(self.analyzer, 'time_series_analysis') and selected_id in self.analyzer.time_series_analysis:
            ts_data = self.analyzer.time_series_analysis[selected_id]
            progress = ts_data['progress_assessment']
            print(f"\n进步情况分析：")
            print(f"  {progress['summary']}")

        # 显示位置适合性
        if hasattr(self.analyzer, 'position_analysis') and selected_id in self.analyzer.position_analysis:
            pos_data = self.analyzer.position_analysis[selected_id]
            print(f"\n位置适合性分析：")
            print(f"  最适合位置：{pos_data['best_position']} (评分: {pos_data['best_score']:.1f})")
            if pos_data['specialties']:
                print(f"  突出特长：{', '.join(pos_data['specialties'].keys())}")
            print(f"  建议：")
            for rec in pos_data['recommendations']:
                print(f"    - {rec}")

    def show_position_rankings(self):
        """显示位置排行榜"""
        positions = {
            '1': 'damage_dealer',
            '2': 'tank',
            '3': 'support',
            '4': 'demolisher'
        }

        print("\n请选择位置：")
        print("1. 输出位")
        print("2. 坦克位")
        print("3. 辅助位")
        print("4. 拆塔位")

        choice = input("请选择 (1-4): ").strip()

        if choice not in positions:
            print("无效选择")
            return

        position = positions[choice]
        position_names = {
            'damage_dealer': '输出位',
            'tank': '坦克位',
            'support': '辅助位',
            'demolisher': '拆塔位'
        }

        rankings = self.analyzer.get_position_rankings(position, 10)

        print(f"\n{position_names[position]}排行榜 (前10名)：")
        print("-" * 60)
        for i, player in enumerate(rankings, 1):
            best_mark = " ★" if player['is_best_position'] else ""
            print(f"{i:2d}. {player['name']} ({player['profession']}) - 评分: {player['score']:.1f}{best_mark}")

    def show_player_progress(self):
        """显示玩家进步情况"""
        if not hasattr(self.analyzer, 'time_series_analysis'):
            print("时间序列分析数据不可用")
            return

        print("\n玩家进步情况排行：")
        print("-" * 60)

        # 按进步分数排序
        progress_list = []
        for unique_id, analysis in self.analyzer.time_series_analysis.items():
            progress_list.append({
                'unique_id': unique_id,
                'name': analysis['name'],
                'profession': analysis['profession'],
                'progress_score': analysis['progress_assessment']['progress_score'],
                'progress_level': analysis['progress_assessment']['progress_level'],
                'summary': analysis['progress_assessment']['summary']
            })

        progress_list.sort(key=lambda x: x['progress_score'], reverse=True)

        for i, player in enumerate(progress_list[:15], 1):  # 显示前15名
            print(f"{i:2d}. {player['name']} ({player['profession']}) - {player['progress_level']} ({player['progress_score']:+.2f})")
            print(f"    {player['summary']}")
            print()

    def show_position_analysis(self):
        """显示位置适合性分析"""
        if not hasattr(self.analyzer, 'position_analysis'):
            print("位置分析数据不可用")
            return

        # 统计各位置的最佳人选
        position_stats = {}
        for unique_id, analysis in self.analyzer.position_analysis.items():
            best_pos = analysis['best_position']
            if best_pos not in position_stats:
                position_stats[best_pos] = []
            position_stats[best_pos].append({
                'unique_id': unique_id,
                'name': analysis['name'],
                'profession': analysis['profession'],
                'score': analysis['best_score']
            })

        position_names = {
            'damage_dealer': '输出位',
            'tank': '坦克位',
            'support': '辅助位',
            'demolisher': '拆塔位'
        }

        print("\n各位置最佳人选：")
        print("=" * 60)

        for position, players in position_stats.items():
            players.sort(key=lambda x: x['score'], reverse=True)
            print(f"\n{position_names.get(position, position)}：")
            for i, player in enumerate(players[:5], 1):  # 显示前5名
                print(f"  {i}. {player['name']} ({player['profession']}) - 评分: {player['score']:.1f}")

    def query_player(self):
        """查询玩家信息（保持兼容性）"""
        self.query_player_detailed()
    
    def show_profession_ranking(self):
        """显示职业排行"""
        print("\n职业战力排行：")
        sorted_professions = self.profession_stats.sort_values('combat_score', ascending=False)
        for i, (profession, data) in enumerate(sorted_professions.iterrows(), 1):
            if profession not in ['unknown', 'nil']:
                print(f"  {i}. {profession} - 平均战力: {data['combat_score']:.1f}")
    
    def find_similar_players(self):
        """查找相似玩家"""
        player_name = input("请输入玩家名字: ").strip()
        
        if player_name in self.player_stats.index:
            similar_players = self.analyzer.find_similar_players(player_name)
            print(f"\n与 {player_name} 相似的玩家：")
            for i, similar_player in enumerate(similar_players, 1):
                score = self.player_stats.loc[similar_player, 'combat_score']
                print(f"  {i}. {similar_player} (战力: {score:.1f})")
        else:
            print(f"未找到玩家 '{player_name}'")
    
    def show_strength_distribution(self):
        """显示强弱分布"""
        print("\n玩家强弱分布：")
        strength_dist = self.player_stats['strength_level'].value_counts()
        total = len(self.player_stats)
        
        for level, count in strength_dist.items():
            percentage = count / total * 100
            print(f"  {level}: {count}人 ({percentage:.1f}%)")


def main():
    """主函数"""
    analyzer = NishuihanAnalyzer()
    
    # 检查数据文件是否存在
    if not os.path.exists("data") or not os.listdir("data"):
        print("错误：data目录不存在或为空！")
        print("请确保将CSV数据文件放在data目录中。")
        return
    
    try:
        # 运行完整分析
        analyzer.run_analysis()
        
        # 提供交互式查询
        while True:
            choice = input("\n是否进入交互式查询模式？(y/n): ").strip().lower()
            if choice == 'y':
                analyzer.interactive_query()
                break
            elif choice == 'n':
                break
            else:
                print("请输入 y 或 n")
                
    except KeyboardInterrupt:
        print("\n\n程序被用户中断。")
    except Exception as e:
        print(f"\n程序运行出错：{str(e)}")


if __name__ == "__main__":
    main()
