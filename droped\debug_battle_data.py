# 调试战斗数据
import sys
import os
sys.path.append('nishuihan_analyzer/src')

from data_processor import DataProcessor

try:
    print("调试战斗数据...")
    
    # 加载数据
    processor = DataProcessor("nishuihan_analyzer/data", "nishuihan_analyzer/纸落弈酒.xlsx")
    raw_data = processor.load_all_data()
    df = processor.create_unified_dataframe()
    
    print(f"总共 {len(raw_data)} 场战斗")
    print(f"总共 {len(df)} 条记录")
    
    # 检查每场战斗的日期
    print("\n战斗日期信息:")
    for i, battle in enumerate(raw_data):
        print(f"第{i+1}场: {battle['filename']} - 日期: {battle['date']}")
    
    # 检查DataFrame中的日期
    print("\nDataFrame中的日期分布:")
    date_counts = df['battle_date'].value_counts()
    for date, count in date_counts.items():
        print(f"  {date}: {count}条记录")
    
    # 检查第三场战斗
    third_battle_date = None
    for battle in raw_data:
        if "2025526_233916" in battle['filename']:
            third_battle_date = battle['date']
            break
    
    if third_battle_date:
        print(f"\n第三场战斗日期: {third_battle_date}")
        third_battle_data = df[df['battle_date'] == third_battle_date]
        print(f"第三场战斗记录数: {len(third_battle_data)}")
        
        if len(third_battle_data) > 0:
            print("第三场战斗玩家:")
            for _, player in third_battle_data.iterrows():
                print(f"  {player['name']} ({player['profession']})")
        else:
            print("❌ 第三场战斗没有数据")
    else:
        print("❌ 没有找到第三场战斗")

except Exception as e:
    print(f"调试失败: {e}")
    import traceback
    traceback.print_exc()
