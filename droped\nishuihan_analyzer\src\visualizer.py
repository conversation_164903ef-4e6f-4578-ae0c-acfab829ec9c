"""
数据可视化模块
用于生成图表和报告
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import os
from typing import Dict, List, Optional

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class DataVisualizer:
    """数据可视化器"""
    
    def __init__(self, output_dir: str = "output"):
        """
        初始化可视化器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
    
    def plot_player_ranking(self, player_stats: pd.DataFrame, top_n: int = 20) -> str:
        """
        绘制玩家排行榜
        
        Args:
            player_stats: 玩家统计数据
            top_n: 显示前n名
            
        Returns:
            保存的文件路径
        """
        top_players = player_stats.nlargest(top_n, 'combat_score')
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))
        
        # 战力评分排行
        bars1 = ax1.barh(range(len(top_players)), top_players['combat_score'], 
                        color=plt.cm.viridis(np.linspace(0, 1, len(top_players))))
        ax1.set_yticks(range(len(top_players)))
        ax1.set_yticklabels(top_players.index, fontsize=10)
        ax1.set_xlabel('战力评分')
        ax1.set_title(f'前{top_n}名玩家战力排行榜')
        ax1.grid(axis='x', alpha=0.3)
        
        # 添加数值标签
        for i, bar in enumerate(bars1):
            width = bar.get_width()
            ax1.text(width + 1, bar.get_y() + bar.get_height()/2, 
                    f'{width:.1f}', ha='left', va='center', fontsize=8)
        
        # KDA排行
        top_kda = player_stats.nlargest(top_n, 'kda')
        bars2 = ax2.barh(range(len(top_kda)), top_kda['kda'],
                        color=plt.cm.plasma(np.linspace(0, 1, len(top_kda))))
        ax2.set_yticks(range(len(top_kda)))
        ax2.set_yticklabels(top_kda.index, fontsize=10)
        ax2.set_xlabel('KDA')
        ax2.set_title(f'前{top_n}名玩家KDA排行榜')
        ax2.grid(axis='x', alpha=0.3)
        
        # 添加数值标签
        for i, bar in enumerate(bars2):
            width = bar.get_width()
            ax2.text(width + 0.1, bar.get_y() + bar.get_height()/2, 
                    f'{width:.2f}', ha='left', va='center', fontsize=8)
        
        plt.tight_layout()
        filepath = os.path.join(self.output_dir, 'player_ranking.png')
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        return filepath
    
    def plot_profession_analysis(self, profession_stats: pd.DataFrame) -> str:
        """
        绘制职业分析图
        
        Args:
            profession_stats: 职业统计数据
            
        Returns:
            保存的文件路径
        """
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 职业平均伤害
        ax1.bar(profession_stats.index, profession_stats['player_damage_mean'], 
                color='skyblue', alpha=0.7)
        ax1.set_title('各职业平均对玩家伤害')
        ax1.set_ylabel('平均伤害')
        ax1.tick_params(axis='x', rotation=45)
        
        # 职业平均治疗
        ax2.bar(profession_stats.index, profession_stats['healing_mean'], 
                color='lightgreen', alpha=0.7)
        ax2.set_title('各职业平均治疗量')
        ax2.set_ylabel('平均治疗量')
        ax2.tick_params(axis='x', rotation=45)
        
        # 职业KDA
        ax3.bar(profession_stats.index, profession_stats['kda'], 
                color='orange', alpha=0.7)
        ax3.set_title('各职业平均KDA')
        ax3.set_ylabel('KDA')
        ax3.tick_params(axis='x', rotation=45)
        
        # 职业战力评分
        ax4.bar(profession_stats.index, profession_stats['combat_score'], 
                color='purple', alpha=0.7)
        ax4.set_title('各职业平均战力评分')
        ax4.set_ylabel('战力评分')
        ax4.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        filepath = os.path.join(self.output_dir, 'profession_analysis.png')
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        return filepath
    
    def plot_strength_distribution(self, player_stats: pd.DataFrame) -> str:
        """
        绘制强弱分布图
        
        Args:
            player_stats: 玩家统计数据
            
        Returns:
            保存的文件路径
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        
        # 强弱等级分布饼图
        strength_counts = player_stats['strength_level'].value_counts()
        colors = plt.cm.Set3(np.linspace(0, 1, len(strength_counts)))
        
        wedges, texts, autotexts = ax1.pie(strength_counts.values, labels=strength_counts.index, 
                                          autopct='%1.1f%%', colors=colors, startangle=90)
        ax1.set_title('玩家强弱等级分布')
        
        # 战力评分分布直方图
        ax2.hist(player_stats['combat_score'], bins=20, color='skyblue', alpha=0.7, edgecolor='black')
        ax2.set_xlabel('战力评分')
        ax2.set_ylabel('玩家数量')
        ax2.set_title('战力评分分布')
        ax2.grid(alpha=0.3)
        
        # 添加统计信息
        mean_score = player_stats['combat_score'].mean()
        median_score = player_stats['combat_score'].median()
        ax2.axvline(mean_score, color='red', linestyle='--', label=f'平均值: {mean_score:.1f}')
        ax2.axvline(median_score, color='green', linestyle='--', label=f'中位数: {median_score:.1f}')
        ax2.legend()
        
        plt.tight_layout()
        filepath = os.path.join(self.output_dir, 'strength_distribution.png')
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        return filepath
    
    def plot_correlation_heatmap(self, player_stats: pd.DataFrame) -> str:
        """
        绘制指标相关性热力图
        
        Args:
            player_stats: 玩家统计数据
            
        Returns:
            保存的文件路径
        """
        # 选择数值列
        numeric_cols = ['kills_mean', 'assists_mean', 'player_damage_mean', 
                       'building_damage_mean', 'healing_mean', 'damage_taken_mean',
                       'deaths_mean', 'kda', 'combat_score']
        
        correlation_matrix = player_stats[numeric_cols].corr()
        
        plt.figure(figsize=(12, 10))
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        
        sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm', 
                   center=0, square=True, linewidths=0.5, cbar_kws={"shrink": .8})
        
        plt.title('玩家指标相关性热力图')
        plt.tight_layout()
        
        filepath = os.path.join(self.output_dir, 'correlation_heatmap.png')
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        return filepath
    
    def create_interactive_dashboard(self, player_stats: pd.DataFrame, 
                                   profession_stats: pd.DataFrame) -> str:
        """
        创建交互式仪表板
        
        Args:
            player_stats: 玩家统计数据
            profession_stats: 职业统计数据
            
        Returns:
            HTML文件路径
        """
        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('战力评分 vs KDA', '职业平均战力', '强弱等级分布', '伤害 vs 治疗'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"type": "pie"}, {"secondary_y": False}]]
        )
        
        # 战力评分 vs KDA 散点图
        fig.add_trace(
            go.Scatter(x=player_stats['combat_score'], y=player_stats['kda'],
                      mode='markers', name='玩家',
                      text=player_stats.index,
                      hovertemplate='<b>%{text}</b><br>战力评分: %{x:.1f}<br>KDA: %{y:.2f}'),
            row=1, col=1
        )
        
        # 职业平均战力柱状图
        fig.add_trace(
            go.Bar(x=profession_stats.index, y=profession_stats['combat_score'],
                   name='职业战力', marker_color='lightblue'),
            row=1, col=2
        )
        
        # 强弱等级分布饼图
        strength_counts = player_stats['strength_level'].value_counts()
        fig.add_trace(
            go.Pie(labels=strength_counts.index, values=strength_counts.values,
                   name="强弱分布"),
            row=2, col=1
        )
        
        # 伤害 vs 治疗散点图
        fig.add_trace(
            go.Scatter(x=player_stats['player_damage_mean'], y=player_stats['healing_mean'],
                      mode='markers', name='伤害vs治疗',
                      text=player_stats.index,
                      hovertemplate='<b>%{text}</b><br>平均伤害: %{x:,.0f}<br>平均治疗: %{y:,.0f}'),
            row=2, col=2
        )
        
        # 更新布局
        fig.update_layout(
            title_text="逆水寒手游数据分析仪表板",
            showlegend=False,
            height=800
        )
        
        # 更新坐标轴标签
        fig.update_xaxes(title_text="战力评分", row=1, col=1)
        fig.update_yaxes(title_text="KDA", row=1, col=1)
        fig.update_xaxes(title_text="职业", row=1, col=2)
        fig.update_yaxes(title_text="平均战力评分", row=1, col=2)
        fig.update_xaxes(title_text="平均对玩家伤害", row=2, col=2)
        fig.update_yaxes(title_text="平均治疗量", row=2, col=2)
        
        filepath = os.path.join(self.output_dir, 'interactive_dashboard.html')
        fig.write_html(filepath)
        
        return filepath
    
    def generate_player_report(self, player_name: str, player_stats: pd.DataFrame, 
                             raw_data: pd.DataFrame) -> str:
        """
        生成单个玩家的详细报告
        
        Args:
            player_name: 玩家名
            player_stats: 玩家统计数据
            raw_data: 原始数据
            
        Returns:
            报告文件路径
        """
        if player_name not in player_stats.index:
            return ""
        
        player_data = player_stats.loc[player_name]
        player_battles = raw_data[raw_data['name'] == player_name]
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 各场战斗表现趋势
        battle_performance = player_battles.groupby('battle_date').agg({
            'kills': 'sum',
            'assists': 'sum',
            'player_damage': 'sum',
            'deaths': 'sum'
        }).fillna(0)  # 填充NaN值

        if len(battle_performance) > 0:
            ax1.plot(range(len(battle_performance)), battle_performance['kills'], 'o-', label='击败')
            ax1.plot(range(len(battle_performance)), battle_performance['assists'], 's-', label='助攻')
            ax1.plot(range(len(battle_performance)), battle_performance['deaths'], '^-', label='死亡')
        ax1.set_title(f'{player_name} - 各场战斗表现趋势')
        ax1.set_xlabel('战斗场次')
        ax1.set_ylabel('次数')
        ax1.legend()
        ax1.grid(alpha=0.3)
        
        # 伤害分布
        damage_data = [
            player_battles['player_damage'].fillna(0).sum(),
            player_battles['building_damage'].fillna(0).sum(),
            player_battles['healing'].fillna(0).sum()
        ]
        damage_labels = ['对玩家伤害', '对建筑伤害', '治疗量']
        colors = ['red', 'orange', 'green']

        # 过滤掉0值
        non_zero_data = [(data, label, color) for data, label, color in zip(damage_data, damage_labels, colors) if data > 0]
        if non_zero_data:
            data_values, labels, colors = zip(*non_zero_data)
            ax2.pie(data_values, labels=labels, autopct='%1.1f%%', colors=colors)
        ax2.set_title(f'{player_name} - 伤害/治疗分布')
        
        # 与同职业对比
        profession = player_data['profession_first']
        same_profession = player_stats[player_stats['profession_first'] == profession]
        
        metrics = ['kills_mean', 'assists_mean', 'player_damage_mean', 'healing_mean', 'kda']
        player_values = [float(player_data[metric]) if pd.notna(player_data[metric]) else 0 for metric in metrics]
        avg_values = [float(same_profession[metric].mean()) if pd.notna(same_profession[metric].mean()) else 0 for metric in metrics]
        
        x = np.arange(len(metrics))
        width = 0.35
        
        ax3.bar(x - width/2, player_values, width, label=player_name, alpha=0.8)
        ax3.bar(x + width/2, avg_values, width, label=f'{profession}平均', alpha=0.8)
        ax3.set_xlabel('指标')
        ax3.set_ylabel('数值')
        ax3.set_title(f'{player_name} vs {profession}职业平均')
        ax3.set_xticks(x)
        ax3.set_xticklabels(['平均击败', '平均助攻', '平均伤害', '平均治疗', 'KDA'], rotation=45)
        ax3.legend()
        ax3.grid(alpha=0.3)
        
        # 战力雷达图
        categories = ['击败', '助攻', '伤害', '治疗', 'KDA']
        
        # 标准化数值到0-100范围
        max_vals = same_profession[metrics].max()
        normalized_player = [(player_data[metrics[i]] / max_vals[i]) * 100 for i in range(len(metrics))]
        normalized_avg = [(same_profession[metrics[i]].mean() / max_vals[i]) * 100 for i in range(len(metrics))]
        
        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        normalized_player += normalized_player[:1]  # 闭合图形
        normalized_avg += normalized_avg[:1]
        angles += angles[:1]
        
        ax4 = plt.subplot(2, 2, 4, projection='polar')
        ax4.plot(angles, normalized_player, 'o-', linewidth=2, label=player_name)
        ax4.fill(angles, normalized_player, alpha=0.25)
        ax4.plot(angles, normalized_avg, 's-', linewidth=2, label=f'{profession}平均')
        ax4.fill(angles, normalized_avg, alpha=0.25)
        ax4.set_xticks(angles[:-1])
        ax4.set_xticklabels(categories)
        ax4.set_ylim(0, 100)
        ax4.set_title(f'{player_name} - 能力雷达图')
        ax4.legend()
        
        plt.tight_layout()
        filepath = os.path.join(self.output_dir, f'{player_name}_report.png')
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        return filepath
