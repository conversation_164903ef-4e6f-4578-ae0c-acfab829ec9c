#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确查找Excel中的替补成员位置
"""

import pandas as pd
import json

def find_exact_substitutes(excel_file):
    """精确查找替补成员"""
    print("=" * 60)
    print("精确查找Excel中的替补成员")
    print("=" * 60)
    
    try:
        # 读取团队管理表
        df = pd.read_excel(excel_file, sheet_name='团队管理表', header=None)
        
        # 根据截图中看到的替补成员名字进行搜索
        target_names = [
            '林若涯', '逸夏', '呼噜', '冷茶', '小金', 
            '态度要家的海螺塔', '百里', '表夏', '送夏',
            '你还没手高呢', '对夏', '徐星花卉', '逸千夏',
            '马场塔家的态度夏'
        ]
        
        found_substitutes = []
        
        print("搜索目标替补成员名字...")
        
        # 遍历整个表格查找这些名字
        for row_idx in range(df.shape[0]):
            for col_idx in range(df.shape[1]):
                cell_value = df.iloc[row_idx, col_idx]
                if pd.notna(cell_value):
                    cell_text = str(cell_value).strip()
                    
                    # 检查是否包含目标名字
                    for target_name in target_names:
                        if target_name in cell_text:
                            print(f"找到 '{target_name}' 在位置: 行{row_idx+1}, 列{col_idx+1}")
                            print(f"  完整内容: '{cell_text}'")
                            
                            # 判断是否请假
                            is_on_leave = '请假' in cell_text
                            
                            if not is_on_leave:
                                # 清理名字
                                clean_name = target_name
                                
                                # 尝试确定职业（查看同行的职业信息）
                                profession = None
                                for check_col in range(max(0, col_idx-5), min(df.shape[1], col_idx+5)):
                                    check_cell = df.iloc[row_idx, check_col]
                                    if pd.notna(check_cell):
                                        check_text = str(check_cell).strip()
                                        if check_text in ['九灵', '素问', '神相', '玄机', '碎梦', '血河', '龙吟', '铁衣', '潮光']:
                                            profession = check_text
                                            break
                                
                                found_substitutes.append({
                                    'name': clean_name,
                                    'profession': profession or '未知',
                                    'original_text': cell_text,
                                    'position': f"行{row_idx+1}列{col_idx+1}",
                                    'status': '替补'
                                })
                                print(f"    -> 添加替补: {clean_name} ({profession or '未知'})")
                            else:
                                print(f"    -> 跳过（请假）: {target_name}")
        
        # 去重
        unique_substitutes = []
        seen_names = set()
        for sub in found_substitutes:
            if sub['name'] not in seen_names:
                unique_substitutes.append(sub)
                seen_names.add(sub['name'])
        
        print(f"\n找到 {len(unique_substitutes)} 名有效替补成员:")
        for sub in unique_substitutes:
            print(f"  - {sub['name']} ({sub['profession']})")
        
        return unique_substitutes
        
    except Exception as e:
        print(f"查找替补成员失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def search_by_pattern(excel_file):
    """通过模式搜索替补成员区域"""
    print("\n" + "=" * 60)
    print("通过模式搜索替补成员区域")
    print("=" * 60)
    
    try:
        df = pd.read_excel(excel_file, sheet_name='团队管理表', header=None)
        
        # 查找包含"备用指挥"的行，这可能是替补区域的标识
        for row_idx in range(df.shape[0]):
            for col_idx in range(df.shape[1]):
                cell_value = df.iloc[row_idx, col_idx]
                if pd.notna(cell_value) and '备用指挥' in str(cell_value):
                    print(f"找到'备用指挥'在位置: 行{row_idx+1}, 列{col_idx+1}")
                    
                    # 显示该行及周围几行的内容
                    for check_row in range(max(0, row_idx-2), min(df.shape[0], row_idx+10)):
                        print(f"\n行{check_row+1}:")
                        for check_col in range(max(0, col_idx-2), min(df.shape[1], col_idx+8)):
                            cell = df.iloc[check_row, check_col]
                            if pd.notna(cell):
                                print(f"  列{check_col+1}: '{cell}'")
                    break
        
        # 查找包含职业名称且右侧有内容的行
        professions = ['九灵', '素问', '神相', '玄机', '碎梦', '血河', '龙吟', '铁衣', '潮光']
        
        print(f"\n查找职业右侧的替补成员...")
        substitutes = []
        
        for row_idx in range(df.shape[0]):
            for col_idx in range(df.shape[1]):
                cell_value = df.iloc[row_idx, col_idx]
                if pd.notna(cell_value) and str(cell_value).strip() in professions:
                    profession = str(cell_value).strip()
                    
                    # 检查右侧几列是否有替补成员
                    for check_col in range(col_idx + 1, min(df.shape[1], col_idx + 6)):
                        right_cell = df.iloc[row_idx, check_col]
                        if pd.notna(right_cell):
                            right_text = str(right_cell).strip()
                            
                            # 跳过明显不是玩家名字的内容
                            if (right_text and 
                                len(right_text) > 1 and
                                right_text not in ['备用指挥', '资源', '输出', '辅助', '坦克', '治疗'] and
                                not right_text.isdigit() and
                                '缺少' not in right_text and
                                '团' not in right_text and
                                '页' not in right_text):
                                
                                # 检查是否是我们要找的替补成员
                                potential_names = ['林若涯', '逸夏', '呼噜', '冷茶', '小金', 
                                                 '态度', '海螺', '百里', '表夏', '送夏',
                                                 '你还没', '对夏', '徐星', '逸千夏', '马场']
                                
                                for name_part in potential_names:
                                    if name_part in right_text:
                                        print(f"在 {profession} 右侧找到可能的替补: '{right_text}' (行{row_idx+1}列{check_col+1})")
                                        
                                        # 判断是否请假
                                        if '请假' not in right_text:
                                            clean_name = right_text.split('（')[0].split('(')[0].strip()
                                            substitutes.append({
                                                'name': clean_name,
                                                'profession': profession,
                                                'original_text': right_text,
                                                'position': f"行{row_idx+1}列{check_col+1}"
                                            })
                                        break
        
        return substitutes
        
    except Exception as e:
        print(f"模式搜索失败: {e}")
        return []

def main():
    """主函数"""
    excel_file = "纸落弈酒.xlsx"
    
    # 精确查找
    substitutes1 = find_exact_substitutes(excel_file)
    
    # 模式搜索
    substitutes2 = search_by_pattern(excel_file)
    
    # 合并结果
    all_substitutes = substitutes1 + substitutes2
    
    # 去重
    unique_substitutes = []
    seen_names = set()
    for sub in all_substitutes:
        if sub['name'] not in seen_names:
            unique_substitutes.append(sub)
            seen_names.add(sub['name'])
    
    if unique_substitutes:
        with open('exact_substitutes.json', 'w', encoding='utf-8') as f:
            json.dump(unique_substitutes, f, ensure_ascii=False, indent=2)
        
        print(f"\n最终找到 {len(unique_substitutes)} 名替补成员:")
        for sub in unique_substitutes:
            print(f"  - {sub['name']} ({sub['profession']})")
        
        print(f"\n替补成员信息已保存到: exact_substitutes.json")
    else:
        print("\n未找到替补成员，可能需要手动指定位置")

if __name__ == "__main__":
    main()
