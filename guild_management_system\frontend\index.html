<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>纸落云烟 - 公会管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 90%;
            max-width: 400px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .login-form {
            padding: 30px 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
        }
        
        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .error-message {
            background: #fee;
            color: #c33;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            display: none;
        }
        
        .success-message {
            background: #efe;
            color: #3c3;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            display: none;
        }
        
        .auth-type-selector {
            margin-bottom: 20px;
            text-align: center;
        }
        
        .auth-type-btn {
            padding: 8px 16px;
            margin: 0 5px;
            border: 2px solid #e1e5e9;
            background: white;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .auth-type-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .dashboard {
            display: none;
            padding: 20px;
        }
        
        .user-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        .logout-btn {
            width: 100%;
            padding: 10px;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 登录界面 -->
        <div id="loginPage">
            <div class="header">
                <h1>纸落云烟</h1>
                <p>公会管理系统</p>
            </div>
            
            <div class="login-form">
                <div class="auth-type-selector">
                    <button class="auth-type-btn active" data-type="simple_password">密码登录</button>
                    <button class="auth-type-btn" data-type="invite_code">邀请码</button>
                </div>
                
                <div class="error-message" id="errorMessage"></div>
                <div class="success-message" id="successMessage"></div>
                
                <form id="loginForm">
                    <div class="form-group" id="nameGroup">
                        <label for="name">姓名</label>
                        <input type="text" id="name" placeholder="请输入您的游戏名">
                    </div>
                    
                    <div class="form-group" id="passwordGroup">
                        <label for="password">密码</label>
                        <input type="password" id="password" placeholder="请输入密码">
                    </div>
                    
                    <div class="form-group" id="inviteCodeGroup" style="display: none;">
                        <label for="inviteCode">邀请码</label>
                        <input type="text" id="inviteCode" placeholder="请输入邀请码">
                    </div>
                    
                    <button type="submit" class="login-btn" id="loginBtn">登录</button>
                </form>
            </div>
        </div>
        
        <!-- 主界面 -->
        <div id="dashboardPage" style="display: none;">
            <div class="header">
                <h1>纸落云烟</h1>
                <p>公会数据总览</p>
            </div>
            
            <div class="dashboard">
                <div class="user-info">
                    <strong id="userName"></strong> | <span id="userRole"></span>
                </div>
                
                <div class="stats-grid" id="statsGrid">
                    <!-- 统计数据将在这里动态加载 -->
                </div>
                
                <button class="logout-btn" onclick="logout()">退出登录</button>
            </div>
        </div>
    </div>

    <script src="config.js"></script>
    <script src="app.js"></script>
</body>
</html>
