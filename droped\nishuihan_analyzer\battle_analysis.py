"""
每场战斗详细分析
分析每个人在每一场的表现，职业和位置
"""

import sys
import os
import pandas as pd
import json

# 添加路径
sys.path.append('src')

from data_processor import DataProcessor

def analyze_each_battle():
    """分析每场战斗的详细表现"""
    try:
        print("=" * 80)
        print("每场战斗详细分析")
        print("=" * 80)
        
        # 加载数据
        processor = DataProcessor("data", "纸落弈酒.xlsx")
        raw_data = processor.load_all_data()
        df = processor.create_unified_dataframe()
        
        print(f"总共 {len(raw_data)} 场战斗，{len(df)} 条记录")
        
        # 分析每场战斗
        battle_analysis = {}
        
        for i, battle in enumerate(raw_data):
            battle_name = battle['filename'].replace('.csv', '')
            print(f"\n{'='*60}")
            print(f"第 {i+1} 场战斗: {battle_name}")
            print(f"{'='*60}")
            
            # 获取这场战斗的数据
            battle_df = df[df['battle_date'] == battle['date']]
            
            if len(battle_df) == 0:
                print("❌ 没有找到纸落云烟的参战数据")
                continue
            
            print(f"📊 纸落云烟参战人数: {len(battle_df)}人")
            
            # 分析每个玩家在这场战斗中的表现
            players_performance = []
            
            for _, player in battle_df.iterrows():
                # 根据数据推测位置
                position = analyze_player_position(player)
                
                performance = {
                    'name': player['name'],
                    'profession': player['profession'],
                    'inferred_position': position,
                    'kills': int(player['kills']),
                    'assists': int(player['assists']),
                    'deaths': int(player['deaths']),
                    'kda': round(player['kda'], 2),
                    'player_damage': int(player['player_damage']),
                    'building_damage': int(player['building_damage']),
                    'healing': int(player['healing']),
                    'damage_taken': int(player['damage_taken']),
                    'heavy_injuries': int(player['heavy_injuries']),
                    'resurrections': int(player['resurrections']),
                    'resources': int(player['resources'])
                }
                
                players_performance.append(performance)
                
                # 显示玩家表现
                print(f"\n👤 {player['name']} ({player['profession']}) - 推测位置: {position}")
                print(f"   ⚔️  击败: {performance['kills']}, 助攻: {performance['assists']}, 死亡: {performance['deaths']}, KDA: {performance['kda']}")
                print(f"   💥 对玩家伤害: {performance['player_damage']:,}, 拆塔: {performance['building_damage']:,}")
                print(f"   🩺 治疗: {performance['healing']:,}, 承伤: {performance['damage_taken']:,}")
                print(f"   🎯 重伤: {performance['heavy_injuries']}, 羽化/清泉: {performance['resurrections']}")
            
            # 按位置分组显示
            print(f"\n📋 位置分布:")
            position_groups = {}
            for player in players_performance:
                pos = player['inferred_position']
                if pos not in position_groups:
                    position_groups[pos] = []
                position_groups[pos].append(player)
            
            for position, players in position_groups.items():
                print(f"  {position}: {len(players)}人")
                for player in players:
                    print(f"    - {player['name']} ({player['profession']})")
            
            # 保存战斗分析
            battle_analysis[battle_name] = {
                'date': battle['date'],
                'participants': len(battle_df),
                'players_performance': players_performance,
                'position_distribution': {pos: len(players) for pos, players in position_groups.items()}
            }
        
        # 保存分析结果
        with open("battle_analysis.json", 'w', encoding='utf-8') as f:
            json.dump(battle_analysis, f, ensure_ascii=False, indent=2)
        
        print(f"\n{'='*80}")
        print("📊 战斗分析总结")
        print(f"{'='*80}")
        
        # 统计每个玩家在不同战斗中的位置变化
        player_positions = {}
        for battle_name, analysis in battle_analysis.items():
            for player in analysis['players_performance']:
                name = player['name']
                if name not in player_positions:
                    player_positions[name] = []
                player_positions[name].append({
                    'battle': battle_name,
                    'profession': player['profession'],
                    'position': player['inferred_position'],
                    'performance_score': calculate_performance_score(player)
                })
        
        print("\n🔄 玩家位置变化分析:")
        for name, positions in player_positions.items():
            if len(positions) > 1:  # 多场战斗的玩家
                pos_set = set([p['position'] for p in positions])
                if len(pos_set) > 1:  # 位置有变化
                    print(f"  {name}: {' → '.join([p['position'] for p in positions])}")
        
        print(f"\n✅ 分析完成！结果已保存到 battle_analysis.json")
        return battle_analysis
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_player_position(player_data):
    """根据玩家数据推测位置"""
    damage = player_data['player_damage']
    healing = player_data['healing']
    damage_taken = player_data['damage_taken']
    building_damage = player_data['building_damage']
    heavy_injuries = player_data['heavy_injuries']
    
    # 位置判断逻辑
    if healing > 30000000:  # 3千万治疗
        return "辅助位"
    elif damage_taken > 80000000:  # 8千万承伤
        return "坦克位"
    elif building_damage > damage * 0.8:  # 拆塔伤害占主要部分
        return "拆塔位"
    elif damage > 80000000:  # 8千万输出
        return "输出位"
    else:
        # 根据职业推测
        profession = player_data['profession']
        if profession in ['素问', '九灵']:
            return "辅助位"
        elif profession in ['铁衣', '碎梦']:
            return "坦克位"
        elif profession in ['神相']:
            return "拆塔位"
        else:
            return "输出位"

def calculate_performance_score(player_data):
    """计算玩家在单场战斗中的表现评分"""
    # 简化的评分算法
    kills_score = player_data['kills'] * 10
    assists_score = player_data['assists'] * 5
    damage_score = player_data['player_damage'] / 1000000  # 百万伤害为1分
    healing_score = player_data['healing'] / 2000000  # 200万治疗为1分
    building_score = player_data['building_damage'] / 5000000  # 500万拆塔为1分
    death_penalty = player_data['deaths'] * -5
    
    total_score = kills_score + assists_score + damage_score + healing_score + building_score + death_penalty
    return round(max(0, total_score), 1)

if __name__ == "__main__":
    analyze_each_battle()
