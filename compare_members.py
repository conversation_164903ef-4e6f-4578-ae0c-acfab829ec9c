#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比CSV和Excel中的成员信息，整理完整的帮会成员名单
"""

import json
import pandas as pd

def load_csv_members():
    """加载CSV中的成员"""
    with open('guild_members.json', 'r', encoding='utf-8') as f:
        csv_members = json.load(f)
    return {member['name']: member for member in csv_members}

def load_excel_members():
    """加载Excel中的成员"""
    with open('excel_members.json', 'r', encoding='utf-8') as f:
        excel_data = json.load(f)
    return excel_data

def filter_real_members(excel_data):
    """过滤出真正的成员名字"""
    # 排除明显不是玩家名字的内容
    exclude_keywords = [
        '统战', '指挥', '职业', '区名', '报名', '人数', '设置', '缺少', '位置', '到场',
        '房间', '密码', '欢迎', '总计', '页', 'ID', '指挥就位', '总设置人数', '人数统计'
    ]

    # 职业名称列表
    profession_names = [
        '素问', '玄机', '潮光', '龙吟', '铁衣', '九灵', '碎梦', '血河', '神相'
    ]

    real_members = []
    for member in excel_data:
        name = member['name']

        # 跳过职业名称
        if name in profession_names:
            continue

        # 跳过明显不是玩家名字的
        if any(keyword in name for keyword in exclude_keywords):
            continue

        # 跳过太短或包含特殊字符的
        if len(name) < 2 or '/' in name or ':' in name or '页' in name:
            continue

        # 跳过数字
        if name.isdigit():
            continue

        real_members.append(name)

    return real_members

def compare_and_merge():
    """对比并合并成员信息"""
    print("=" * 60)
    print("对比CSV和Excel中的成员信息")
    print("=" * 60)
    
    # 加载数据
    csv_members = load_csv_members()
    excel_data = load_excel_members()
    excel_members = filter_real_members(excel_data)
    
    print(f"CSV中的成员数量: {len(csv_members)}")
    print(f"Excel中过滤后的成员数量: {len(excel_members)}")
    
    # 找出在Excel中但不在CSV中的成员（可能是替补）
    csv_names = set(csv_members.keys())
    excel_names = set(excel_members)
    
    # 处理名字的细微差异
    name_mapping = {
        '晚凇': '晚淞',
        '搅屎的棍': '搅史的棍', 
        '楚晚宁': '楚晚寕',
        '啊橘喵': '阿橘喵',
        '夜朝暮': '朝夜',
        '饱饱': '饱饱丶',
        '淞鸦': '凇鴉'
    }
    
    # 应用名字映射
    normalized_excel_names = set()
    for name in excel_names:
        normalized_name = name_mapping.get(name, name)
        normalized_excel_names.add(normalized_name)
    
    # 找出差异
    only_in_csv = csv_names - normalized_excel_names
    only_in_excel = normalized_excel_names - csv_names
    common_members = csv_names & normalized_excel_names
    
    print(f"\n共同成员: {len(common_members)}人")
    print(f"只在CSV中的成员: {len(only_in_csv)}人")
    print(f"只在Excel中的成员: {len(only_in_excel)}人")
    
    if only_in_csv:
        print(f"\n只在CSV中的成员（可能是战斗中的临时成员）:")
        for name in sorted(only_in_csv):
            print(f"  - {name} ({csv_members[name]['profession']})")
    
    if only_in_excel:
        print(f"\n只在Excel中的成员（可能是替补成员）:")
        for name in sorted(only_in_excel):
            print(f"  - {name}")
    
    # 创建完整的成员列表
    complete_members = []
    
    # 添加CSV中的成员（有职业信息）
    for name, member_info in csv_members.items():
        complete_members.append({
            'name': name,
            'profession': member_info['profession'],
            'team': member_info.get('team', ''),
            'position': member_info.get('position', ''),
            'status': '主力' if name in common_members else '战斗参与',
            'source': 'CSV'
        })
    
    # 添加只在Excel中的成员（替补）
    for name in only_in_excel:
        complete_members.append({
            'name': name,
            'profession': '',  # 需要手动填写
            'team': '',
            'position': '',
            'status': '替补',
            'source': 'Excel'
        })
    
    # 保存完整名单
    with open('complete_guild_members.json', 'w', encoding='utf-8') as f:
        json.dump(complete_members, f, ensure_ascii=False, indent=2)
    
    print(f"\n完整的帮会成员名单已保存到: complete_guild_members.json")
    print(f"总成员数: {len(complete_members)}人")
    
    # 按状态统计
    status_count = {}
    for member in complete_members:
        status = member['status']
        status_count[status] = status_count.get(status, 0) + 1
    
    print(f"\n成员状态统计:")
    for status, count in status_count.items():
        print(f"  {status}: {count}人")
    
    return complete_members

def analyze_professions(complete_members):
    """分析职业分布"""
    print("\n" + "=" * 60)
    print("职业分布分析")
    print("=" * 60)
    
    profession_count = {}
    profession_members = {}
    
    for member in complete_members:
        prof = member['profession'] if member['profession'] else '未知'
        profession_count[prof] = profession_count.get(prof, 0) + 1
        
        if prof not in profession_members:
            profession_members[prof] = []
        profession_members[prof].append(member['name'])
    
    for profession, count in sorted(profession_count.items()):
        print(f"\n{profession}: {count}人")
        for name in profession_members[profession]:
            status = next(m['status'] for m in complete_members if m['name'] == name)
            print(f"  - {name} ({status})")

def main():
    """主函数"""
    complete_members = compare_and_merge()
    analyze_professions(complete_members)
    
    print(f"\n下一步：需要为替补成员补充职业信息")

if __name__ == "__main__":
    main()
