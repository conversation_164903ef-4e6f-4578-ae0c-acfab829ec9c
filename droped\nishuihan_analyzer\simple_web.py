"""
纸落云烟数据分析 - 简化Web版本
"""

from flask import Flask, render_template_string
import sys
import os

# 添加路径
sys.path.append('src')

app = Flask(__name__)

# 简化的HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>纸落云烟 - 数据分析系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #667eea;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border-left: 4px solid #667eea;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #764ba2;
            margin-bottom: 5px;
        }
        .player-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
        }
        .player-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid #667eea;
        }
        .player-name {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .player-profession {
            color: #666;
            margin-bottom: 10px;
        }
        .player-stats {
            font-size: 0.9em;
            color: #555;
        }
        .strength-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            margin-top: 8px;
            background: #667eea;
            color: white;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .unknown-player {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .suggestion {
            background: #d1ecf1;
            padding: 8px;
            border-radius: 5px;
            margin-top: 8px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>纸落云烟数据分析系统</h1>
            <p>专业的逆水寒手游公会数据分析平台</p>
        </div>
        
        <div class="section">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">{{ total_players }}</div>
                    <div>总玩家数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ total_battles }}</div>
                    <div>分析战斗数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ unknown_count }}</div>
                    <div>职业待补充</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ top_player_name }}</div>
                    <div>战力第一</div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>🏆 战力排行榜 (前10名)</h2>
            <div class="player-list">
                {% for player in top_players %}
                <div class="player-card">
                    <div class="player-name">{{ loop.index }}. {{ player.name }}</div>
                    <div class="player-profession">{{ player.profession }}</div>
                    <div class="player-stats">
                        战力评分: {{ player.combat_score }} | 
                        击败: {{ player.kills }} | 
                        助攻: {{ player.assists }} | 
                        KDA: {{ player.kda }}
                    </div>
                    <div class="strength-badge">{{ player.strength_level }}</div>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <div class="section">
            <h2>❓ 职业信息待补充 ({{ unknown_players|length }}名)</h2>
            <div class="player-list">
                {% for player in unknown_players %}
                <div class="player-card unknown-player">
                    <div class="player-name">{{ player.name }}</div>
                    <div class="player-profession">当前职业: {{ player.profession }}</div>
                    <div class="player-stats">
                        平均伤害: {{ "{:,}".format(player.avg_damage) }} | 
                        平均治疗: {{ "{:,}".format(player.avg_healing) }} | 
                        平均承伤: {{ "{:,}".format(player.avg_taken) }}
                    </div>
                    <div class="suggestion">
                        <strong>建议职业:</strong> {{ player.suggested_role }}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <div class="section">
            <h2>📊 各位置最佳人选</h2>
            <div class="stats-grid">
                {% for position in position_rankings %}
                <div class="stat-card">
                    <h3 style="color: #667eea; margin-bottom: 15px;">{{ position.name }}</h3>
                    {% for player in position.players[:3] %}
                    <div style="margin-bottom: 8px; text-align: left;">
                        {{ loop.index }}. {{ player.name }} ({{ player.score }})
                    </div>
                    {% endfor %}
                </div>
                {% endfor %}
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 40px; color: #666;">
            <p>💡 提示: 运行完整版Web应用 (python web_app.py) 获得更多功能</p>
            <p>📁 数据文件: data/ | 📊 输出结果: output/ | 📖 详细文档: README.md</p>
        </div>
    </div>
</body>
</html>
"""

@app.route('/')
def index():
    try:
        # 导入分析模块
        from data_processor import DataProcessor
        from analyzer import NishuihanPlayerAnalyzer
        
        # 加载数据
        processor = DataProcessor("data", "纸落弈酒.xlsx")
        raw_data = processor.load_all_data()
        df = processor.create_unified_dataframe()
        
        # 分析数据
        analyzer = NishuihanPlayerAnalyzer(df)
        player_stats = analyzer.calculate_player_scores()
        position_analysis = analyzer.analyze_position_suitability()
        
        # 准备数据
        total_players = len(player_stats)
        total_battles = len(raw_data)
        
        # 前10名玩家
        top_10 = player_stats.nlargest(10, 'combat_score')
        top_players = []
        for _, player in top_10.iterrows():
            top_players.append({
                'name': player['name_first'],
                'profession': player['profession_first'],
                'combat_score': round(player['combat_score'], 1),
                'kills': int(player['kills_sum']),
                'assists': int(player['assists_sum']),
                'kda': round(player['kda'], 2),
                'strength_level': player['strength_level']
            })
        
        # 职业不确定的玩家
        unknown_players = []
        for _, player in player_stats.iterrows():
            if player['profession_first'] in ['unknown', 'nil', 'nan', '']:
                # 推测职业
                avg_healing = player['healing_mean']
                avg_damage = player['player_damage_mean']
                avg_taken = player['damage_taken_mean']
                
                if avg_healing > 50000000:
                    suggested = "治疗职业 (素问/九灵)"
                elif avg_damage > 100000000:
                    suggested = "输出职业 (龙吟/铁衣/潮光等)"
                elif avg_taken > 80000000:
                    suggested = "坦克职业 (铁衣/碎梦等)"
                else:
                    suggested = "辅助/平衡职业"
                
                unknown_players.append({
                    'name': player['name_first'],
                    'profession': player['profession_first'],
                    'avg_damage': int(player['player_damage_mean']),
                    'avg_healing': int(player['healing_mean']),
                    'avg_taken': int(player['damage_taken_mean']),
                    'suggested_role': suggested
                })
        
        # 位置排行
        positions = [
            {'key': 'damage_dealer', 'name': '输出位'},
            {'key': 'tank', 'name': '坦克位'},
            {'key': 'support', 'name': '辅助位'},
            {'key': 'demolisher', 'name': '拆塔位'}
        ]
        
        position_rankings = []
        for pos in positions:
            rankings = analyzer.get_position_rankings(pos['key'], 5)
            position_rankings.append({
                'name': pos['name'],
                'players': [{'name': p['name'], 'score': f"{p['score']:.1f}"} for p in rankings]
            })
        
        return render_template_string(HTML_TEMPLATE,
            total_players=total_players,
            total_battles=total_battles,
            unknown_count=len(unknown_players),
            top_player_name=top_players[0]['name'] if top_players else '无',
            top_players=top_players,
            unknown_players=unknown_players,
            position_rankings=position_rankings
        )
        
    except Exception as e:
        return f"""
        <h1>数据加载失败</h1>
        <p>错误信息: {str(e)}</p>
        <p>请确保数据文件存在并且格式正确。</p>
        <p><a href="javascript:location.reload()">重新加载</a></p>
        """

if __name__ == '__main__':
    print("启动纸落云烟数据分析Web应用...")
    print("访问地址: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
