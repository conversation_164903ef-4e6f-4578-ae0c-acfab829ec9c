"""
生成最终综合报告
整合所有分析结果，生成完整的分析报告
"""

import json
import pandas as pd
from datetime import datetime

def generate_comprehensive_report():
    """生成综合报告"""
    try:
        print("=" * 80)
        print("生成纸落云烟综合分析报告")
        print("=" * 80)
        
        # 加载所有分析结果
        final_results = load_final_results()
        roster_data = load_roster_data()
        battle_data = load_battle_data()
        
        # 生成HTML报告
        html_report = generate_html_report(final_results, roster_data, battle_data)
        
        # 保存HTML报告
        with open('comprehensive_report.html', 'w', encoding='utf-8') as f:
            f.write(html_report)
        
        # 生成Markdown报告
        md_report = generate_markdown_report(final_results, roster_data, battle_data)
        
        # 保存Markdown报告
        with open('comprehensive_report.md', 'w', encoding='utf-8') as f:
            f.write(md_report)
        
        print("✅ 综合报告生成完成！")
        print("   HTML报告: comprehensive_report.html")
        print("   Markdown报告: comprehensive_report.md")
        
        return True
        
    except Exception as e:
        print(f"❌ 报告生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def load_final_results():
    """加载最终结果"""
    try:
        with open('final_scoring_results.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return None

def load_roster_data():
    """加载名单数据"""
    try:
        return pd.read_csv('player_roster.csv', encoding='utf-8-sig')
    except FileNotFoundError:
        return None

def load_battle_data():
    """加载战斗数据"""
    try:
        with open('battle_by_battle_analysis.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return None

def generate_html_report(final_results, roster_data, battle_data):
    """生成HTML报告"""
    html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>纸落云烟 - 综合数据分析报告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            border-bottom: 3px solid #667eea;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }}
        .header h1 {{
            color: #667eea;
            font-size: 2.5em;
            margin-bottom: 10px;
        }}
        .section {{
            margin-bottom: 40px;
        }}
        .section h2 {{
            color: #333;
            border-left: 4px solid #667eea;
            padding-left: 15px;
            margin-bottom: 20px;
        }}
        .ranking-table {{
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }}
        .ranking-table th, .ranking-table td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        .ranking-table th {{
            background: #667eea;
            color: white;
        }}
        .ranking-table tr:nth-child(even) {{
            background: #f9f9f9;
        }}
        .team-config {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }}
        .stat-card {{
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }}
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            color: #1976d2;
        }}
        .position-badge {{
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            margin-left: 10px;
        }}
        .pos-输出位 {{ background: #ff6b6b; color: white; }}
        .pos-坦克位 {{ background: #4ecdc4; color: white; }}
        .pos-辅助位 {{ background: #45b7d1; color: white; }}
        .pos-拆塔位 {{ background: #96ceb4; color: white; }}
        .footer {{
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>纸落云烟公会数据分析报告</h1>
            <p>逆水寒手游战斗数据综合分析</p>
            <p>生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M')}</p>
        </div>
        
        <div class="section">
            <h2>📊 数据概览</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">{final_results['analysis_summary']['total_players'] if final_results else 60}</div>
                    <div>总玩家数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">3</div>
                    <div>分析战斗数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{final_results['analysis_summary']['average_score'] if final_results else 49.2}</div>
                    <div>平均评分</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">100%</div>
                    <div>职业完整度</div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>🏆 最终排名</h2>
            <table class="ranking-table">
                <thead>
                    <tr>
                        <th>排名</th>
                        <th>玩家名字</th>
                        <th>职业</th>
                        <th>最终评分</th>
                        <th>最佳位置</th>
                        <th>稳定性</th>
                        <th>潜力</th>
                    </tr>
                </thead>
                <tbody>
                    {generate_ranking_rows(final_results)}
                </tbody>
            </table>
        </div>
        
        <div class="section">
            <h2>⚡ 推荐战队配置</h2>
            {generate_team_configs(final_results)}
        </div>
        
        <div class="section">
            <h2>📈 分析总结</h2>
            {generate_analysis_summary_html(final_results)}
        </div>
        
        <div class="footer">
            <p>本报告由纸落云烟数据分析系统自动生成</p>
            <p>数据来源: 逆水寒手游战斗记录 | 分析算法: 多维度综合评分</p>
        </div>
    </div>
</body>
</html>
"""
    return html

def generate_ranking_rows(final_results):
    """生成排名表格行"""
    if not final_results or 'final_ranking' not in final_results:
        return "<tr><td colspan='7'>数据加载失败</td></tr>"
    
    rows = ""
    for player in final_results['final_ranking'][:20]:  # 显示前20名
        rows += f"""
        <tr>
            <td>{player['rank']}</td>
            <td>{player['name']}</td>
            <td>{player['profession']}</td>
            <td>{player['final_score']}</td>
            <td><span class="position-badge pos-{player['best_position']}">{player['best_position']}</span></td>
            <td>{player['stability']}</td>
            <td>{player['potential']}</td>
        </tr>
        """
    return rows

def generate_team_configs(final_results):
    """生成战队配置HTML"""
    if not final_results or 'team_configurations' not in final_results:
        return "<p>战队配置数据不可用</p>"
    
    configs_html = ""
    for config in final_results['team_configurations']:
        configs_html += f"""
        <div class="team-config">
            <h3>{config['name']} (平均评分: {config['avg_score']:.1f})</h3>
            <ul>
        """
        for position, player in config['lineup'].items():
            configs_html += f"<li><strong>{position}:</strong> {player}</li>"
        configs_html += "</ul></div>"
    
    return configs_html

def generate_analysis_summary_html(final_results):
    """生成分析总结HTML"""
    if not final_results or 'analysis_summary' not in final_results:
        return "<p>分析总结数据不可用</p>"
    
    summary = final_results['analysis_summary']
    
    html = f"""
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{summary['total_players']}</div>
            <div>总玩家数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{summary['average_score']}</div>
            <div>平均评分</div>
        </div>
    </div>
    
    <h3>🌟 特殊表现</h3>
    <ul>
        <li><strong>最佳表现:</strong> {summary['top_performer']['name']} (评分: {summary['top_performer']['final_score']})</li>
        <li><strong>最稳定:</strong> {summary['most_stable']['name']} (稳定性: {summary['most_stable']['stability']})</li>
        <li><strong>最具潜力:</strong> {summary['highest_potential']['name']} (潜力: {summary['highest_potential']['potential']})</li>
    </ul>
    
    <h3>📊 评分分布</h3>
    <ul>
    """
    
    for level, count in summary['score_distribution'].items():
        percentage = (count / summary['total_players']) * 100
        html += f"<li><strong>{level}:</strong> {count}人 ({percentage:.1f}%)</li>"
    
    html += "</ul>"
    
    return html

def generate_markdown_report(final_results, roster_data, battle_data):
    """生成Markdown报告"""
    md = f"""# 纸落云烟公会数据分析报告

**逆水寒手游战斗数据综合分析**

生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M')}

---

## 📊 数据概览

- **总玩家数**: {final_results['analysis_summary']['total_players'] if final_results else 60}人
- **分析战斗数**: 3场
- **平均评分**: {final_results['analysis_summary']['average_score'] if final_results else 49.2}
- **职业完整度**: 100%

---

## 🏆 最终排名 (前15名)

| 排名 | 玩家名字 | 职业 | 最终评分 | 最佳位置 | 稳定性 | 潜力 |
|------|----------|------|----------|----------|--------|------|
"""
    
    if final_results and 'final_ranking' in final_results:
        for player in final_results['final_ranking'][:15]:
            md += f"| {player['rank']} | {player['name']} | {player['profession']} | {player['final_score']} | {player['best_position']} | {player['stability']} | {player['potential']} |\n"
    
    md += """
---

## ⚡ 推荐战队配置

"""
    
    if final_results and 'team_configurations' in final_results:
        for config in final_results['team_configurations']:
            md += f"### {config['name']} (平均评分: {config['avg_score']:.1f})\n\n"
            for position, player in config['lineup'].items():
                md += f"- **{position}**: {player}\n"
            md += "\n"
    
    md += """---

## 📈 分析总结

"""
    
    if final_results and 'analysis_summary' in final_results:
        summary = final_results['analysis_summary']
        md += f"""
### 🌟 特殊表现

- **最佳表现**: {summary['top_performer']['name']} (评分: {summary['top_performer']['final_score']})
- **最稳定**: {summary['most_stable']['name']} (稳定性: {summary['most_stable']['stability']})
- **最具潜力**: {summary['highest_potential']['name']} (潜力: {summary['highest_potential']['potential']})

### 📊 评分分布

"""
        for level, count in summary['score_distribution'].items():
            percentage = (count / summary['total_players']) * 100
            md += f"- **{level}**: {count}人 ({percentage:.1f}%)\n"
    
    md += """
---

## 💡 使用建议

1. **主力阵容**: 优先使用推荐的主力阵容进行重要战斗
2. **位置调整**: 根据每个人的最佳位置进行合理安排
3. **培养重点**: 关注高潜力玩家的成长和训练
4. **稳定发挥**: 重视稳定性高的玩家在关键时刻的作用

---

*本报告由纸落云烟数据分析系统自动生成*

*数据来源: 逆水寒手游战斗记录 | 分析算法: 多维度综合评分*
"""
    
    return md

if __name__ == "__main__":
    generate_comprehensive_report()
