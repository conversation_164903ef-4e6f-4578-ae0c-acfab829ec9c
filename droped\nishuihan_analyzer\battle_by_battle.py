"""
逐场战斗分析
分析每个人在每一场的具体表现
"""

import sys
import os
import pandas as pd
import json

# 添加路径
sys.path.append('src')

from data_processor import DataProcessor

def analyze_battle_by_battle():
    """逐场分析每个人的表现"""
    try:
        print("=" * 80)
        print("逐场战斗详细分析")
        print("=" * 80)
        
        # 加载数据
        processor = DataProcessor("data", "纸落弈酒.xlsx")
        raw_data = processor.load_all_data()
        df = processor.create_unified_dataframe()
        
        print(f"📊 数据概况: {len(raw_data)}场战斗, {len(df)}条记录")
        
        # 分析每场战斗
        all_battles = {}
        
        # 获取所有唯一的战斗日期
        battle_dates = df['battle_date'].unique()
        
        for i, battle_date in enumerate(battle_dates, 1):
            battle_df = df[df['battle_date'] == battle_date]
            
            # 找到对应的战斗文件名
            battle_filename = f"第{i}场战斗"
            for battle in raw_data:
                if str(battle_date).replace('-', '').replace(' 00:00:00', '') in battle['date']:
                    battle_filename = battle['filename'].replace('.csv', '')
                    break
            
            print(f"\n{'='*60}")
            print(f"第{i}场战斗: {battle_filename}")
            print(f"日期: {battle_date}")
            print(f"参战人数: {len(battle_df)}人")
            print(f"{'='*60}")
            
            # 分析这场战斗中每个玩家的表现
            battle_analysis = analyze_single_battle(battle_df, i)
            all_battles[f"battle_{i}"] = {
                'name': battle_filename,
                'date': str(battle_date),
                'participants': len(battle_df),
                'analysis': battle_analysis
            }
        
        # 生成每个玩家的跨战斗分析
        print(f"\n{'='*80}")
        print("跨战斗玩家分析")
        print(f"{'='*80}")
        
        player_cross_analysis = analyze_player_across_battles(df, all_battles)
        
        # 保存完整分析结果
        complete_analysis = {
            'battles': all_battles,
            'player_cross_analysis': player_cross_analysis,
            'summary': generate_summary(all_battles, player_cross_analysis)
        }
        
        with open('battle_by_battle_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(complete_analysis, f, ensure_ascii=False, indent=2)
        
        # 生成CSV报告
        generate_csv_reports(complete_analysis)
        
        print(f"\n{'='*80}")
        print("📊 分析总结")
        print(f"{'='*80}")
        
        summary = complete_analysis['summary']
        print(f"总战斗数: {summary['total_battles']}")
        print(f"总参战人次: {summary['total_participations']}")
        print(f"独特玩家数: {summary['unique_players']}")
        
        print(f"\n🏆 跨战斗表现最佳玩家:")
        for i, player in enumerate(summary['top_performers'][:10], 1):
            print(f"  {i:2d}. {player['name']} ({player['profession']}) - 平均评分: {player['avg_score']:.1f}")
        
        print(f"\n✅ 逐场战斗分析完成！")
        print(f"   详细数据: battle_by_battle_analysis.json")
        print(f"   CSV报告: battle_summary.csv, player_performance.csv")
        
        return complete_analysis
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_single_battle(battle_df, battle_num):
    """分析单场战斗"""
    print(f"\n分析第{battle_num}场战斗中的每个玩家:")
    
    battle_analysis = []
    
    # 按伤害排序
    sorted_players = battle_df.sort_values('player_damage', ascending=False)
    
    for rank, (_, player) in enumerate(sorted_players.iterrows(), 1):
        # 推测位置
        position = infer_battle_position(player)
        
        # 计算表现评分
        performance_score = calculate_battle_score(player)
        
        # 分析特点
        specialties = analyze_player_specialties(player, battle_df)
        
        analysis = {
            'rank': rank,
            'name': player['name'],
            'profession': player['profession'],
            'position': position,
            'performance_score': performance_score,
            'stats': {
                'kills': int(player['kills']),
                'assists': int(player['assists']),
                'deaths': int(player['deaths']),
                'kda': round(player['kda'], 2),
                'player_damage': int(player['player_damage']),
                'building_damage': int(player['building_damage']),
                'healing': int(player['healing']),
                'damage_taken': int(player['damage_taken']),
                'heavy_injuries': int(player['heavy_injuries']),
                'resurrections': int(player['resurrections']),
                'resources': int(player['resources'])
            },
            'specialties': specialties
        }
        
        battle_analysis.append(analysis)
        
        # 显示玩家信息
        print(f"  {rank:2d}. {player['name']} ({player['profession']}) - {position}")
        print(f"      评分: {performance_score:.1f} | KDA: {analysis['stats']['kda']} | 伤害: {analysis['stats']['player_damage']:,}")
        if specialties:
            print(f"      特长: {', '.join(specialties)}")
    
    return battle_analysis

def analyze_player_across_battles(df, all_battles):
    """分析玩家跨战斗表现"""
    print(f"\n分析每个玩家的跨战斗表现:")
    
    player_analysis = {}
    
    for unique_id in df['unique_id'].unique():
        player_data = df[df['unique_id'] == unique_id]
        player_name = player_data['name'].iloc[0]
        player_profession = player_data['profession'].iloc[0]
        
        # 收集该玩家在各场战斗中的表现
        battle_performances = []
        positions_used = []
        
        for battle_key, battle_info in all_battles.items():
            # 在这场战斗中找到该玩家
            for player_analysis_item in battle_info['analysis']:
                if player_analysis_item['name'] == player_name:
                    battle_performances.append(player_analysis_item['performance_score'])
                    positions_used.append(player_analysis_item['position'])
                    break
        
        if battle_performances:
            # 计算跨战斗统计
            avg_score = sum(battle_performances) / len(battle_performances)
            best_score = max(battle_performances)
            worst_score = min(battle_performances)
            consistency = 1 - (max(battle_performances) - min(battle_performances)) / max(max(battle_performances), 1)
            
            # 分析位置一致性
            most_common_position = max(set(positions_used), key=positions_used.count)
            position_consistency = positions_used.count(most_common_position) / len(positions_used)
            
            player_analysis[unique_id] = {
                'name': player_name,
                'profession': player_profession,
                'battles_participated': len(battle_performances),
                'avg_score': round(avg_score, 1),
                'best_score': round(best_score, 1),
                'worst_score': round(worst_score, 1),
                'consistency': round(consistency, 2),
                'most_common_position': most_common_position,
                'position_consistency': round(position_consistency, 2),
                'positions_used': positions_used,
                'battle_scores': battle_performances
            }
            
            print(f"  {player_name} ({player_profession}): 平均{avg_score:.1f} | 最佳{best_score:.1f} | 主位置{most_common_position}")
    
    return player_analysis

def infer_battle_position(player_data):
    """推测战斗位置"""
    healing = player_data['healing']
    damage = player_data['player_damage']
    damage_taken = player_data['damage_taken']
    building_damage = player_data['building_damage']
    
    if healing > 30000000:  # 3千万治疗
        return "辅助位"
    elif damage_taken > 80000000:  # 8千万承伤
        return "坦克位"
    elif building_damage > damage * 0.6:  # 拆塔伤害占主要部分
        return "拆塔位"
    else:
        return "输出位"

def calculate_battle_score(player_data):
    """计算战斗评分"""
    kills_score = player_data['kills'] * 15
    assists_score = player_data['assists'] * 8
    damage_score = player_data['player_damage'] / 2000000
    healing_score = player_data['healing'] / 3000000
    building_score = player_data['building_damage'] / 8000000
    death_penalty = player_data['deaths'] * -10
    kda_bonus = min(player_data['kda'] * 5, 50)
    
    total_score = (kills_score + assists_score + damage_score + healing_score + 
                  building_score + death_penalty + kda_bonus)
    
    return round(max(0, total_score), 1)

def analyze_player_specialties(player_data, battle_df):
    """分析玩家在本场战斗中的特长"""
    specialties = []
    
    # 计算相对于本场其他玩家的表现
    avg_damage = battle_df['player_damage'].mean()
    avg_healing = battle_df['healing'].mean()
    avg_building = battle_df['building_damage'].mean()
    avg_kills = battle_df['kills'].mean()
    
    if player_data['player_damage'] > avg_damage * 1.5:
        specialties.append("输出核心")
    
    if player_data['healing'] > avg_healing * 2:
        specialties.append("治疗专家")
    
    if player_data['building_damage'] > avg_building * 1.5:
        specialties.append("拆塔专家")
    
    if player_data['kills'] > avg_kills * 1.5:
        specialties.append("击杀专家")
    
    if player_data['assists'] > battle_df['assists'].mean() * 1.3:
        specialties.append("团队配合")
    
    if player_data['kda'] > 5:
        specialties.append("高KDA")
    
    return specialties

def generate_summary(all_battles, player_cross_analysis):
    """生成分析总结"""
    total_battles = len(all_battles)
    total_participations = sum([battle['participants'] for battle in all_battles.values()])
    unique_players = len(player_cross_analysis)
    
    # 排序玩家
    top_performers = sorted(player_cross_analysis.values(), 
                          key=lambda x: x['avg_score'], reverse=True)
    
    return {
        'total_battles': total_battles,
        'total_participations': total_participations,
        'unique_players': unique_players,
        'top_performers': top_performers
    }

def generate_csv_reports(complete_analysis):
    """生成CSV报告"""
    # 战斗总结报告
    battle_summary = []
    for battle_key, battle_info in complete_analysis['battles'].items():
        battle_summary.append({
            'battle_name': battle_info['name'],
            'date': battle_info['date'],
            'participants': battle_info['participants'],
            'avg_score': sum([p['performance_score'] for p in battle_info['analysis']]) / len(battle_info['analysis'])
        })
    
    pd.DataFrame(battle_summary).to_csv('battle_summary.csv', index=False, encoding='utf-8-sig')
    
    # 玩家表现报告
    player_performance = []
    for player_data in complete_analysis['player_cross_analysis'].values():
        player_performance.append(player_data)
    
    pd.DataFrame(player_performance).to_csv('player_performance.csv', index=False, encoding='utf-8-sig')

if __name__ == "__main__":
    analyze_battle_by_battle()
