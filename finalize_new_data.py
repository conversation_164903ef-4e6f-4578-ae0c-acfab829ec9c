#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终整理基于帮战名单表.xlsx的正确数据
"""

import json

def clean_and_finalize_data():
    """清理并最终确定数据"""
    print("=" * 80)
    print("🎯 最终整理纸落云烟帮会数据（基于帮战名单表.xlsx）")
    print("=" * 80)
    
    # 读取刚提取的数据
    with open('members_from_battle_roster.json', 'r', encoding='utf-8') as f:
        members = json.load(f)
    
    # 移除无效成员（表头等）
    invalid_names = ['团号', '分团', '到场', 'ID', '位置']
    valid_members = [m for m in members if m['name'] not in invalid_names]
    
    # 团队名称标准化
    team_mapping = {
        '进攻团': '进攻团',
        '防守团': '防守团'
    }
    
    # 根据原始数据中的团号信息重新分配
    for member in valid_members:
        # 从source信息中提取团号
        if '一团' in member.get('source', ''):
            member['team'] = '1团'
        elif '二团' in member.get('source', ''):
            member['team'] = '2团'
        elif '三团' in member.get('source', ''):
            member['team'] = '3团'
        elif '四团' in member.get('source', ''):
            member['team'] = '4团'
        elif member['team'] == '进攻团':
            # 保持进攻团标识，后续可以细分
            member['team'] = '进攻团'
        elif member['team'] == '防守团':
            member['team'] = '防守团'
    
    # 手动修正团队分配（基于帮战名单表的实际结构）
    team_corrections = {
        # 一团成员（进攻团）
        '有姝丶': '1团', '喵妍': '1团', '世味': '1团', '风悔': '1团', '和妤': '1团', '循梅': '1团',
        '霜华雪丶': '1团', '梧桐长枝': '1团', '怪味小面包': '1团', '甜心小蛋挞': '1团', '妙善': '1团', '淞鸦': '1团',
        '萌猫儿': '1团', '卟囄': '1团', '寂寞的花海': '1团', '霐塵': '1团', '君藺': '1团', '缠吻': '1团',
        
        # 二团成员（进攻团）
        '皆宜': '2团', '搅屎的棍': '2团', '春野雾奈': '2团', '彦遇': '2团', '小昭枝': '2团', '小呆橘': '2团',
        '以宁丶': '2团', '韎韐鵇丶': '2团', '爱鲲': '2团', '晚凇': '2团', '楚晚宁': '2团',
        
        # 三团成员（防守团）
        '啊橘喵': '3团', '叶问': '3团', '瑾亦': '3团', '玉米绝': '3团', '山野水野': '3团', '偷感': '3团',
        '朝夜': '3团', '李修缘': '3团', '浅鹤丶': '3团', '不命': '3团', '川遥丶': '3团', '私信': '3团',
        '夜朝暮': '3团', '金芝芝': '3团', '小辞星': '3团', '烤全羊': '3团', '古纸': '3团', '花枝弧雀': '3团',
        '冬予眠': '3团', '水狱': '3团', '沐慕': '3团', '沫白知画': '3团', '小雨珠': '3团', '饱饱': '3团',
        
        # 四团成员（进攻团）
        '彧墨': '4团', '妖怪': '4团', '金叶叶': '4团', '与假': '4团', '玄霄': '4团', '叩医': '4团'
    }
    
    # 应用团队修正
    for member in valid_members:
        if member['name'] in team_corrections:
            member['team'] = team_corrections[member['name']]
    
    # 根据职业特点分配位置
    position_mapping = {
        '素问': '治疗',
        '铁衣': '坦克',
        '潮光': '输出',
        '九灵': '输出',
        '龙吟': '输出',
        '血河': '输出',
        '碎梦': '输出',
        '玄机': '拆塔',
        '神相': '拆塔'
    }
    
    for member in valid_members:
        member['position'] = position_mapping.get(member['profession'], '输出')
        member['status'] = '主力'
        # 清理source字段
        if 'source' in member:
            del member['source']
    
    return valid_members

def generate_final_report(members):
    """生成最终报告"""
    print(f"\n📊 最终数据统计")
    print(f"总成员数: {len(members)}人")
    
    # 团队统计
    team_stats = {}
    for member in members:
        team = member['team']
        if team not in team_stats:
            team_stats[team] = {'count': 0, 'professions': {}}
        team_stats[team]['count'] += 1
        
        prof = member['profession']
        if prof not in team_stats[team]['professions']:
            team_stats[team]['professions'][prof] = []
        team_stats[team]['professions'][prof].append(member['name'])
    
    print(f"\n🎯 最终团队分配:")
    for team in ['1团', '2团', '3团', '4团']:
        if team in team_stats:
            stats = team_stats[team]
            print(f"\n【{team}】({stats['count']}人):")
            for prof, names in sorted(stats['professions'].items()):
                print(f"  {prof}({len(names)}): {', '.join(names)}")
    
    # 职业统计
    profession_stats = {}
    for member in members:
        prof = member['profession']
        profession_stats[prof] = profession_stats.get(prof, 0) + 1
    
    print(f"\n💼 职业分布:")
    for prof, count in sorted(profession_stats.items()):
        print(f"  {prof}: {count}人")
    
    # 团队配置分析
    print(f"\n⚖️ 团队配置分析:")
    for team in ['1团', '2团', '3团', '4团']:
        if team in team_stats:
            stats = team_stats[team]
            
            # 检查配置
            has_tank = '铁衣' in stats['professions']
            has_healer = '素问' in stats['professions']
            has_dps = any(prof in stats['professions'] for prof in ['潮光', '九灵', '龙吟', '血河', '碎梦'])
            has_support = any(prof in stats['professions'] for prof in ['神相', '玄机'])
            
            config_status = []
            config_status.append("坦克✓" if has_tank else "坦克✗")
            config_status.append("治疗✓" if has_healer else "治疗✗")
            config_status.append("输出✓" if has_dps else "输出✗")
            config_status.append("辅助✓" if has_support else "辅助✗")
            
            print(f"  {team}: {stats['count']}人 | {' | '.join(config_status)}")
    
    return members

def main():
    """主函数"""
    # 清理和最终确定数据
    final_members = clean_and_finalize_data()
    
    # 生成最终报告
    final_members = generate_final_report(final_members)
    
    # 保存最终数据
    with open('final_guild_members.json', 'w', encoding='utf-8') as f:
        json.dump(final_members, f, ensure_ascii=False, indent=2)
    
    print(f"\n" + "=" * 80)
    print("✅ 最终数据整理完成！")
    print("📁 最终数据已保存到: final_guild_members.json")
    print("🎯 这是基于帮战名单表.xlsx的正确、完整的帮会数据")
    print("📊 包含正确的团队分配、职业信息和位置分配")
    print("=" * 80)

if __name__ == "__main__":
    main()
