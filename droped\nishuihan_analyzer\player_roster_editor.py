"""
纸落云烟玩家名单编辑器
可编辑且可保存的玩家名单，确保职业和位置正确
"""

import json
import os
import sys
from flask import Flask, render_template_string, request, jsonify

# 添加路径
sys.path.append('src')

from data_processor import DataProcessor

app = Flask(__name__)

# HTML模板
ROSTER_EDITOR_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>纸落云烟 - 玩家名单编辑器</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #667eea;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-warning {
            background: #ffc107;
            color: #333;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .search-box {
            padding: 10px;
            border: 2px solid #667eea;
            border-radius: 8px;
            font-size: 1em;
            width: 300px;
        }
        .player-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .player-table th {
            background: #667eea;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: bold;
        }
        .player-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #e9ecef;
        }
        .player-table tr:hover {
            background: #f8f9fa;
        }
        .editable {
            border: none;
            background: transparent;
            width: 100%;
            padding: 5px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        .editable:focus {
            background: #fff3cd;
            outline: 2px solid #ffc107;
        }
        .profession-select, .position-select {
            width: 100%;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-complete { background: #28a745; }
        .status-incomplete { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #667eea;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        .save-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            display: none;
        }
        .save-success { background: #28a745; }
        .save-error { background: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>纸落云烟玩家名单编辑器</h1>
            <p>确保每个玩家的职业和位置信息正确</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalPlayers">{{ total_players }}</div>
                <div>总玩家数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="completePlayers">0</div>
                <div>信息完整</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="incompletePlayers">{{ total_players }}</div>
                <div>待完善</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalBattles">{{ total_battles }}</div>
                <div>总战斗数</div>
            </div>
        </div>
        
        <div class="controls">
            <input type="text" class="search-box" id="searchBox" placeholder="搜索玩家名字或职业..." onkeyup="filterPlayers()">
            <div>
                <button class="btn btn-primary" onclick="loadData()">重新加载</button>
                <button class="btn btn-success" onclick="saveData()">保存修改</button>
                <button class="btn btn-warning" onclick="exportData()">导出数据</button>
            </div>
        </div>
        
        <table class="player-table" id="playerTable">
            <thead>
                <tr>
                    <th>状态</th>
                    <th>排名</th>
                    <th>玩家名字</th>
                    <th>当前职业</th>
                    <th>正确职业</th>
                    <th>主要位置</th>
                    <th>备用位置</th>
                    <th>参战次数</th>
                    <th>战力评分</th>
                    <th>备注</th>
                </tr>
            </thead>
            <tbody id="playerTableBody">
                {% for player in players %}
                <tr data-player-id="{{ player.unique_id }}">
                    <td>
                        <span class="status-indicator status-incomplete" id="status-{{ loop.index }}"></span>
                    </td>
                    <td>{{ loop.index }}</td>
                    <td><strong>{{ player.name }}</strong></td>
                    <td>{{ player.current_profession }}</td>
                    <td>
                        <select class="profession-select" onchange="updatePlayer('{{ player.unique_id }}', 'profession', this.value)">
                            <option value="">请选择</option>
                            <option value="龙吟" {{ 'selected' if player.correct_profession == '龙吟' else '' }}>龙吟</option>
                            <option value="铁衣" {{ 'selected' if player.correct_profession == '铁衣' else '' }}>铁衣</option>
                            <option value="碎梦" {{ 'selected' if player.correct_profession == '碎梦' else '' }}>碎梦</option>
                            <option value="血河" {{ 'selected' if player.correct_profession == '血河' else '' }}>血河</option>
                            <option value="神相" {{ 'selected' if player.correct_profession == '神相' else '' }}>神相</option>
                            <option value="素问" {{ 'selected' if player.correct_profession == '素问' else '' }}>素问</option>
                            <option value="九灵" {{ 'selected' if player.correct_profession == '九灵' else '' }}>九灵</option>
                            <option value="潮光" {{ 'selected' if player.correct_profession == '潮光' else '' }}>潮光</option>
                            <option value="玄机" {{ 'selected' if player.correct_profession == '玄机' else '' }}>玄机</option>
                            <option value="紫霞" {{ 'selected' if player.correct_profession == '紫霞' else '' }}>紫霞</option>
                        </select>
                    </td>
                    <td>
                        <select class="position-select" onchange="updatePlayer('{{ player.unique_id }}', 'main_position', this.value)">
                            <option value="">请选择</option>
                            <option value="输出位" {{ 'selected' if player.main_position == '输出位' else '' }}>输出位</option>
                            <option value="坦克位" {{ 'selected' if player.main_position == '坦克位' else '' }}>坦克位</option>
                            <option value="辅助位" {{ 'selected' if player.main_position == '辅助位' else '' }}>辅助位</option>
                            <option value="拆塔位" {{ 'selected' if player.main_position == '拆塔位' else '' }}>拆塔位</option>
                        </select>
                    </td>
                    <td>
                        <select class="position-select" onchange="updatePlayer('{{ player.unique_id }}', 'backup_position', this.value)">
                            <option value="">无</option>
                            <option value="输出位" {{ 'selected' if player.backup_position == '输出位' else '' }}>输出位</option>
                            <option value="坦克位" {{ 'selected' if player.backup_position == '坦克位' else '' }}>坦克位</option>
                            <option value="辅助位" {{ 'selected' if player.backup_position == '辅助位' else '' }}>辅助位</option>
                            <option value="拆塔位" {{ 'selected' if player.backup_position == '拆塔位' else '' }}>拆塔位</option>
                        </select>
                    </td>
                    <td>{{ player.battles_count }}</td>
                    <td>{{ player.combat_score }}</td>
                    <td>
                        <input type="text" class="editable" placeholder="备注..." 
                               value="{{ player.notes }}" 
                               onchange="updatePlayer('{{ player.unique_id }}', 'notes', this.value)">
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <div class="save-status" id="saveStatus"></div>

    <script>
        let playerData = {};
        
        // 初始化玩家数据
        {% for player in players %}
        playerData['{{ player.unique_id }}'] = {
            name: '{{ player.name }}',
            current_profession: '{{ player.current_profession }}',
            correct_profession: '{{ player.correct_profession }}',
            main_position: '{{ player.main_position }}',
            backup_position: '{{ player.backup_position }}',
            notes: '{{ player.notes }}'
        };
        {% endfor %}
        
        // 更新玩家信息
        function updatePlayer(playerId, field, value) {
            playerData[playerId][field] = value;
            updateStatus(playerId);
            updateStats();
        }
        
        // 更新状态指示器
        function updateStatus(playerId) {
            const player = playerData[playerId];
            const row = document.querySelector(`tr[data-player-id="${playerId}"]`);
            const statusIndicator = row.querySelector('.status-indicator');
            
            if (player.correct_profession && player.main_position) {
                statusIndicator.className = 'status-indicator status-complete';
            } else if (player.correct_profession || player.main_position) {
                statusIndicator.className = 'status-indicator status-warning';
            } else {
                statusIndicator.className = 'status-indicator status-incomplete';
            }
        }
        
        // 更新统计数据
        function updateStats() {
            let complete = 0;
            let incomplete = 0;
            
            Object.values(playerData).forEach(player => {
                if (player.correct_profession && player.main_position) {
                    complete++;
                } else {
                    incomplete++;
                }
            });
            
            document.getElementById('completePlayers').textContent = complete;
            document.getElementById('incompletePlayers').textContent = incomplete;
        }
        
        // 过滤玩家
        function filterPlayers() {
            const searchTerm = document.getElementById('searchBox').value.toLowerCase();
            const rows = document.querySelectorAll('#playerTableBody tr');
            
            rows.forEach(row => {
                const name = row.cells[2].textContent.toLowerCase();
                const profession = row.cells[3].textContent.toLowerCase();
                
                if (name.includes(searchTerm) || profession.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        // 保存数据
        async function saveData() {
            try {
                const response = await fetch('/save_roster', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(playerData)
                });
                
                if (response.ok) {
                    showSaveStatus('数据保存成功！', 'success');
                } else {
                    showSaveStatus('保存失败，请重试', 'error');
                }
            } catch (error) {
                showSaveStatus('保存失败：' + error.message, 'error');
            }
        }
        
        // 显示保存状态
        function showSaveStatus(message, type) {
            const statusDiv = document.getElementById('saveStatus');
            statusDiv.textContent = message;
            statusDiv.className = `save-status save-${type}`;
            statusDiv.style.display = 'block';
            
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 3000);
        }
        
        // 导出数据
        function exportData() {
            const dataStr = JSON.stringify(playerData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'nishuihan_player_roster.json';
            link.click();
        }
        
        // 重新加载数据
        function loadData() {
            location.reload();
        }
        
        // 初始化状态
        document.addEventListener('DOMContentLoaded', function() {
            Object.keys(playerData).forEach(playerId => {
                updateStatus(playerId);
            });
            updateStats();
        });
    </script>
</body>
</html>
"""

@app.route('/')
def roster_editor():
    try:
        # 加载数据
        processor = DataProcessor("data", "纸落弈酒.xlsx")
        raw_data = processor.load_all_data()
        df = processor.create_unified_dataframe()
        
        # 加载现有的名单数据（如果存在）
        roster_file = "player_roster.json"
        existing_roster = {}
        if os.path.exists(roster_file):
            with open(roster_file, 'r', encoding='utf-8') as f:
                existing_roster = json.load(f)
        
        # 准备玩家数据
        players = []
        unique_players = df.groupby('unique_id').agg({
            'name': 'first',
            'profession': 'first',
            'battles_count': 'count',
            'player_damage': 'mean',
            'healing': 'mean',
            'building_damage': 'mean',
            'damage_taken': 'mean'
        }).reset_index()
        
        # 按平均伤害排序
        unique_players['avg_damage'] = unique_players['player_damage']
        unique_players = unique_players.sort_values('avg_damage', ascending=False)
        
        for _, player in unique_players.iterrows():
            unique_id = player['unique_id']
            existing_data = existing_roster.get(unique_id, {})
            
            players.append({
                'unique_id': unique_id,
                'name': player['name'],
                'current_profession': player['profession'],
                'correct_profession': existing_data.get('correct_profession', ''),
                'main_position': existing_data.get('main_position', ''),
                'backup_position': existing_data.get('backup_position', ''),
                'battles_count': int(player['battles_count']),
                'combat_score': round(player['avg_damage'] / 1000000, 1),  # 简化评分
                'notes': existing_data.get('notes', '')
            })
        
        return render_template_string(ROSTER_EDITOR_TEMPLATE,
            players=players,
            total_players=len(players),
            total_battles=len(raw_data)
        )
        
    except Exception as e:
        return f"<h1>加载失败</h1><p>错误: {str(e)}</p>"

@app.route('/save_roster', methods=['POST'])
def save_roster():
    try:
        roster_data = request.json
        
        # 保存到JSON文件
        with open("player_roster.json", 'w', encoding='utf-8') as f:
            json.dump(roster_data, f, ensure_ascii=False, indent=2)
        
        return jsonify({'status': 'success', 'message': '数据保存成功'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

if __name__ == '__main__':
    print("启动纸落云烟玩家名单编辑器...")
    print("访问地址: http://localhost:5002")
    print("可以编辑和保存每个玩家的职业和位置信息！")
    app.run(debug=True, host='0.0.0.0', port=5002)
