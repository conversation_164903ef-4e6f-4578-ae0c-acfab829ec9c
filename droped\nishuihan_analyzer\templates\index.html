<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>纸落云烟 - 数据分析系统</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #764ba2;
            margin-bottom: 10px;
        }
        
        .tabs {
            display: flex;
            background: white;
            border-radius: 15px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .tab {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            cursor: pointer;
            background: #f8f9fa;
            border: none;
            font-size: 1.1em;
            transition: all 0.3s ease;
        }
        
        .tab.active {
            background: #667eea;
            color: white;
        }
        
        .tab:hover {
            background: #5a6fd8;
            color: white;
        }
        
        .content {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            min-height: 500px;
        }
        
        .player-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .player-card {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 15px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .player-card:hover {
            border-color: #667eea;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }
        
        .player-name {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .player-profession {
            color: #666;
            margin-bottom: 10px;
        }
        
        .player-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 0.9em;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
        }
        
        .strength-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            margin-top: 10px;
        }
        
        .strength-超强 { background: #ff6b6b; color: white; }
        .strength-很强 { background: #ff8e53; color: white; }
        .strength-强 { background: #ffa726; color: white; }
        .strength-中等偏强 { background: #66bb6a; color: white; }
        .strength-中等 { background: #42a5f5; color: white; }
        .strength-中等偏弱 { background: #ab47bc; color: white; }
        .strength-弱 { background: #78909c; color: white; }
        .strength-很弱 { background: #90a4ae; color: white; }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        
        .search-box {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1em;
            margin-bottom: 20px;
        }
        
        .search-box:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .position-rankings {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .ranking-card {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 15px;
        }
        
        .ranking-title {
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #667eea;
        }
        
        .ranking-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .ranking-item:last-child {
            border-bottom: none;
        }
        
        .rank-number {
            font-weight: bold;
            color: #764ba2;
            margin-right: 10px;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 15px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #000;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>纸落云烟数据分析系统</h1>
            <p>专业的逆水寒手游公会数据分析平台</p>
        </div>
        
        <div class="dashboard" id="dashboard">
            <div class="card">
                <h3>总玩家数</h3>
                <div class="stat-number" id="totalPlayers">-</div>
                <p>已分析的公会成员</p>
            </div>
            <div class="card">
                <h3>职业分布</h3>
                <div class="chart-container">
                    <canvas id="professionChart"></canvas>
                </div>
            </div>
            <div class="card">
                <h3>强弱分布</h3>
                <div class="chart-container">
                    <canvas id="strengthChart"></canvas>
                </div>
            </div>
        </div>
        
        <div class="tabs">
            <button class="tab active" onclick="showTab('players')">全员分析</button>
            <button class="tab" onclick="showTab('rankings')">位置排行</button>
            <button class="tab" onclick="showTab('unknown')">职业待补充</button>
        </div>
        
        <div class="content">
            <div id="playersTab" class="tab-content">
                <input type="text" class="search-box" id="playerSearch" placeholder="搜索玩家名字或职业...">
                <div class="player-grid" id="playerGrid">
                    <div class="loading">正在加载玩家数据...</div>
                </div>
            </div>
            
            <div id="rankingsTab" class="tab-content" style="display: none;">
                <div class="position-rankings" id="positionRankings">
                    <div class="loading">正在加载排行榜数据...</div>
                </div>
            </div>
            
            <div id="unknownTab" class="tab-content" style="display: none;">
                <h3>职业信息待补充的玩家</h3>
                <p style="margin-bottom: 20px; color: #666;">以下玩家的职业信息不完整，请根据数据特征补充到纸落弈酒.xlsx文件中：</p>
                <div id="unknownPlayers">
                    <div class="loading">正在加载数据...</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 玩家详情模态框 -->
    <div id="playerModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="playerDetail">
                <div class="loading">正在加载玩家详情...</div>
            </div>
        </div>
    </div>

    <script src="/static/app.js"></script>
</body>
</html>
