#!/bin/bash

# 纸落云烟帮会管理系统 - 全服务启动脚本

echo "🚀 启动纸落云烟帮会管理系统..."
echo "=" * 60

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3，请先安装Python3"
    exit 1
fi

# 安装依赖
echo "📦 安装依赖包..."
pip3 install -r requirements.txt

# 创建必要的目录
mkdir -p data/uploads
mkdir -p static

# 设置环境变量
export FLASK_ENV=production
export HOST=0.0.0.0

echo "🌐 启动多个服务..."
echo "   - 管理后台: 端口 5888"
echo "   - API服务: 端口 5002" 
echo "   - 成员访问: 端口 5000"
echo ""

# 启动管理后台 (端口5888)
echo "🔧 启动管理后台..."
export PORT=5888
nohup python3 app.py > logs/backend.log 2>&1 &
BACKEND_PID=$!
echo "管理后台已启动 (PID: $BACKEND_PID)"

# 启动API服务 (端口5002)
echo "🔌 启动API服务..."
nohup python3 api_app.py > logs/api.log 2>&1 &
API_PID=$!
echo "API服务已启动 (PID: $API_PID)"

# 启动成员访问服务 (端口5000)
echo "👥 启动成员访问服务..."
export MEMBER_PORT=5000
nohup python3 member_app.py > logs/member.log 2>&1 &
MEMBER_PID=$!
echo "成员访问服务已启动 (PID: $MEMBER_PID)"

# 创建PID文件
mkdir -p pids
echo $BACKEND_PID > pids/backend.pid
echo $API_PID > pids/api.pid
echo $MEMBER_PID > pids/member.pid

echo ""
echo "✅ 所有服务启动完成！"
echo "=" * 60
echo "📱 访问地址:"
echo "   🔧 管理后台: http://localhost:5888 (管理员使用)"
echo "   🔌 API服务:  http://localhost:5002 (前端调用)"
echo "   👥 成员访问: http://localhost:5000 (成员查看)"
echo ""
echo "📋 服务管理:"
echo "   查看状态: ./status.sh"
echo "   停止服务: ./stop_all.sh"
echo "   查看日志: tail -f logs/*.log"
echo "=" * 60

# 等待几秒让服务完全启动
sleep 3

# 检查服务是否正常启动
echo "🔍 检查服务状态..."
if ps -p $BACKEND_PID > /dev/null; then
    echo "✅ 管理后台运行正常"
else
    echo "❌ 管理后台启动失败"
fi

if ps -p $API_PID > /dev/null; then
    echo "✅ API服务运行正常"
else
    echo "❌ API服务启动失败"
fi

if ps -p $MEMBER_PID > /dev/null; then
    echo "✅ 成员访问服务运行正常"
else
    echo "❌ 成员访问服务启动失败"
fi

echo ""
echo "🎉 系统启动完成！"
